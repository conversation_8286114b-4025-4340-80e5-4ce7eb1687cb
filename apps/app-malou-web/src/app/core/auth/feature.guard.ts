import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { delay, filter, map, switchMap } from 'rxjs/operators';

import { AppFeatureName } from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';

@Injectable()
export class FeatureGuard {
    private readonly _router = inject(Router);
    private readonly _experimentationService = inject(ExperimentationService);

    canActivate(route: ActivatedRouteSnapshot): Observable<boolean> {
        const featureName = route.data.feature as AppFeatureName;

        if (!featureName) {
            console.warn('FeatureGuard: No feature specified in route data');
            return this._redirectAndReturnFalse();
        }

        return this._experimentationService.isLoaded$.pipe(
            filter(Boolean),
            delay(100), // Ensure the service is loaded before checking the feature
            switchMap(() => this._experimentationService.isFeatureEnabled$(featureName)),
            map((isEnabled) => {
                if (isEnabled) {
                    return true;
                }
                this._router.navigate(['/groups']);
                return false;
            })
        );
    }

    private _redirectAndReturnFalse(): Observable<boolean> {
        this._router.navigate(['/groups']);
        return of(false);
    }
}
