import { Pipe, PipeTransform } from '@angular/core';

export enum Illustration {
    NotFound = '404',
    Burger = 'Burger',
    Cape = 'Cape',
    Cles = 'Cles',
    Cloche = 'Cloche',
    Cook = 'Cook',
    Cutlery = 'Cutlery',
    Default = 'default-picture-grey',
    Goggles = 'Goggles',
    GogoolOk = 'Gogool_ok',
    GreyBurger = 'Grey-burger',
    Icecream = 'Icecream',
    KarlOk = 'Karl_ok',
    <PERSON> = 'Karl',
    Pizza = 'Pizza',
    Placeholder = 'Placeholder',
    Plate = 'Plate',
    Screen = 'Screen',
    Stove = 'Stove',
    Taster = 'Taster',
    MessagesOk = 'Messages_ok',
    ManTaster = 'ManTaster',
    WheelOfFortune = 'wheel-of-fortune',
    Search = 'Search',
    Chef = 'Chef',
    Google = 'Google',
    BestFoodInTown = 'BestFoodInTown',
    Stars = 'Stars',
    Calque = 'Calque',
    OkHand = 'OkHand',
}

@Pipe({
    name: 'illustrationPathResolver',
    standalone: true,
})
export class IllustrationPathResolverPipe implements PipeTransform {
    transform(key: string): string {
        return `/assets/illustrations/${key}.png`;
    }
}
