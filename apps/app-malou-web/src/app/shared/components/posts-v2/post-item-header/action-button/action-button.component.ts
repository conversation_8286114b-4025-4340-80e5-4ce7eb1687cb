import { ChangeDetectionStrategy, Component, input, OnD<PERSON>roy, ViewChild } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule, MatMenuTrigger } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';

import { SvgIcon } from ':shared/modules/svg-icon.enum';

export type ActionButtonProps = {
    id: string;
    label: string;
    disabled: boolean;
    tooltipDisabled: boolean;
    tooltip: string;
    prefixIcon?: SvgIcon;
} & ({ onClick: () => void; hasActionMenu: false } | { hasActionMenu: true; actions: ActionButtonProps[] });

@Component({
    selector: 'app-action-button',
    templateUrl: './action-button.component.html',
    styleUrls: ['./action-button.component.scss'],
    imports: [MatIconModule, MatMenuModule, MatTooltipModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActionButtonComponent implements OnD<PERSON>roy {
    readonly data = input.required<ActionButtonProps>();

    @ViewChild(MatMenuTrigger) menuTrigger?: MatMenuTrigger;

    private _hoverTimeout?: number;
    private _isHoveringMenu = false;
    private readonly _HOVER_DELAY = 200; // milliseconds

    readonly SvgIcon = SvgIcon;

    onMouseEnter(): void {
        if (this.data().hasActionMenu && this.menuTrigger) {
            // Clear any existing close timeout
            if (this._hoverTimeout) {
                clearTimeout(this._hoverTimeout);
                this._hoverTimeout = undefined;
            }

            // Open menu immediately if not already open
            if (!this.menuTrigger.menuOpen) {
                this.menuTrigger.openMenu();
            }
        }
    }

    onMouseLeave(): void {
        if (this.data().hasActionMenu && this.menuTrigger) {
            // Close menu after delay when leaving the menu area
            this._hoverTimeout = window.setTimeout(() => {
                if (!this._isHoveringMenu) {
                    this.menuTrigger?.closeMenu();
                }
            }, this._HOVER_DELAY);
        }
    }

    onMenuMouseEnter(): void {
        this._isHoveringMenu = true;
        // Clear any close timeout when entering the menu
        if (this._hoverTimeout) {
            clearTimeout(this._hoverTimeout);
            this._hoverTimeout = undefined;
        }
    }

    onMenuMouseLeave(): void {
        this._isHoveringMenu = false;
        if (this.data().hasActionMenu && this.menuTrigger) {
            // Close menu after delay when leaving the menu area
            this._hoverTimeout = window.setTimeout(() => {
                this.menuTrigger?.closeMenu();
            }, this._HOVER_DELAY);
        }
    }

    ngOnDestroy(): void {
        if (this._hoverTimeout) {
            clearTimeout(this._hoverTimeout);
        }
    }
}
