<div class="flex items-center justify-between">
    <div class="flex gap-x-3">
        <div class="flex gap-x-1">
            @for (platformKey of sortedPlatformKeys(); track platformKey) {
                <app-platform-logo imgClasses="h-6 w-6" [logo]="platformKey"></app-platform-logo>
            }
        </div>

        <div
            class="malou-text-10--medium flex h-6 items-center gap-x-2 rounded-[3px] px-2"
            [attr.data-testid]="'social-post-status-' + published()"
            [ngClass]="{
                'bg-[#E6E9EC] text-malou-color-text-2': published() === PostPublicationStatus.PUBLISHED && isActive() === false,
                'bg-malou-color-background-success text-malou-color-text-green':
                    published() === PostPublicationStatus.PUBLISHED && isActive() !== false,
                'bg-malou-color-background-warning text-malou-color-state-warn':
                    published() === PostPublicationStatus.PENDING && !isPublishing(),
                'bg-malou-color-background-lavender-light text-malou-color-chart-purple--accent':
                    published() === PostPublicationStatus.PENDING && isPublishing(),
                'bg-malou-color-background-pending text-malou-color-text-purple--light': published() === PostPublicationStatus.DRAFT,
                'bg-malou-color-background-error text-malou-color-state-error': published() === PostPublicationStatus.ERROR,
            }">
            @if (published() === PostPublicationStatus.PENDING && isPublishing()) {
                <app-malou-spinner size="xs" color="#AC32B7"></app-malou-spinner>
                <div>{{ 'social_post.is_publishing' | translate }}</div>
            } @else if (published() === PostPublicationStatus.PUBLISHED && isActive() === true) {
                <div>{{ 'stories.status.active' | translate }}</div>
            } @else if (published() === PostPublicationStatus.PUBLISHED && isActive() === false) {
                <div>{{ 'stories.status.inactive' | translate }}</div>
            } @else {
                {{ published() | enumTranslate: 'publication_status' }}
            }
        </div>

        @if (postDate(); as postDate) {
            <app-post-date-picker
                [heapTrackBtnId]="'tracking_social_posts_init_post_date_picker_v2'"
                [selectedDate]="postDate"
                [disabled]="!canSchedule() || isReadonly() || !userCanManagePost()"
                [matTooltipDisabled]="userCanManagePost()"
                [matTooltip]="'casl.wrong_role' | translate"
                (selectedDateChange)="onPostDateChange($event)" />
        }
    </div>

    <div class="flex items-center">
        @if (authorInitials(); as authorInitials) {
            <div
                class="malou-text-10--bold flex h-[24px] min-w-[24px] items-center justify-center rounded-full bg-malou-color-background-dark px-1 text-malou-color-primary"
                [matTooltip]="'social_post.author' | translate: { author: authorName() }">
                {{ authorInitials }}
            </div>
        }
        @if (!isReadonly()) {
            <ng-container [ngTemplateOutlet]="actionsButtonTemplate"></ng-container>
        }
    </div>
</div>

<ng-template #actionsButtonTemplate>
    <div class="flex items-center">
        @if (showActionsButton()) {
            <button class="malou-btn-icon flex items-center" mat-icon-button [matMenuTriggerFor]="actionsMenu">
                <mat-icon class="!h-4" color="primary" [svgIcon]="SvgIcon.ELLIPSIS"></mat-icon>
            </button>
            <mat-menu class="malou-mat-menu malou-box-shadow !rounded-md" xPosition="before" #actionsMenu="matMenu">
                @for (action of actions(); track action) {
                    <app-action-button [data]="action"></app-action-button>
                }
            </mat-menu>
        }
    </div>
</ng-template>
