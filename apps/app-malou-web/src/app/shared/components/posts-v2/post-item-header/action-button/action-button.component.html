@if (data(); as data) {
    @if (data.hasActionMenu) {
        <button
            class="flex place-items-center"
            mat-menu-item
            [matMenuTriggerFor]="actionsMenu"
            [disabled]="data.disabled"
            [matTooltipDisabled]="data.tooltipDisabled"
            [matTooltip]="data.tooltip"
            (mouseenter)="onMouseEnter()"
            (mouseleave)="onMouseLeave()"
            #trigger="matMenuTrigger">
            @if (data.prefixIcon) {
                <mat-icon class="!mr-4 !h-4 !w-4" color="primary" [svgIcon]="data.prefixIcon"></mat-icon>
            }
            <div class="flex w-full items-center justify-between">
                <span class="malou-text-14--regular">{{ data.label }}</span>
                <mat-icon class="!mr-0 ml-3 !h-4 !w-4" color="primary" [svgIcon]="SvgIcon.CHEVRON_RIGHT"></mat-icon>
            </div>
        </button>

        <mat-menu class="malou-mat-menu malou-box-shadow !rounded-[10px]" [hasBackdrop]="false" #actionsMenu="matMenu">
            <div (mouseenter)="onMenuMouseEnter()" (mouseleave)="onMenuMouseLeave()">
                @for (action of data.actions; track action) {
                    <app-action-button [data]="action"></app-action-button>
                }
            </div>
        </mat-menu>
    } @else {
        <button
            class="flex items-center"
            mat-menu-item
            [disabled]="data.disabled"
            [matTooltipDisabled]="data.tooltipDisabled"
            [matTooltip]="data.tooltip"
            (click)="data.onClick()">
            @if (data.prefixIcon) {
                <mat-icon class="!mr-4 !h-4 !w-4" color="primary" [svgIcon]="data.prefixIcon"></mat-icon>
            }
            <span class="malou-text-14--regular">{{ data.label }}</span>
        </button>
    }
}
