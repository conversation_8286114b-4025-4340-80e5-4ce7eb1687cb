import { NgClass, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, input, output } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { PostDatePickerComponent } from ':modules/posts-v2/post-date-picker/post-date-picker.component';
import { PlatformLogoComponent } from ':shared/components/platform-logo/platform-logo.component';
import {
    ActionButtonComponent,
    ActionButtonProps,
} from ':shared/components/posts-v2/post-item-header/action-button/action-button.component';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';

@Component({
    selector: 'app-post-item-header',
    templateUrl: './post-item-header.component.html',
    styleUrls: ['./post-item-header.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        NgClass,
        NgTemplateOutlet,
        MatIconModule,
        MatMenuModule,
        MatTooltipModule,
        TranslateModule,
        MalouSpinnerComponent,
        PlatformLogoComponent,
        PostDatePickerComponent,
        EnumTranslatePipe,
        ActionButtonComponent,
    ],
})
export class PostItemHeaderComponent {
    readonly sortedPlatformKeys = input.required<PlatformKey[]>();
    readonly published = input.required<PostPublicationStatus>();
    readonly isPublishing = input.required<boolean>();
    readonly isActive = input<boolean | null>(null);
    readonly postDate = input.required<Date | undefined>();
    readonly authorInitials = input.required<string | undefined>();
    readonly authorName = input.required<string | undefined>();
    readonly canSchedule = input.required<boolean>();
    readonly showActionsButton = input.required<boolean>();
    readonly isReadonly = input.required<boolean>();
    readonly userCanManagePost = input.required<boolean>();
    readonly actions = input.required<ActionButtonProps[]>();

    readonly postDateChange = output<Date>();
    readonly updatePost = output<void>();
    readonly openSocialLink = output<void>();
    readonly deletePost = output<void>();

    readonly PostPublicationStatus = PostPublicationStatus;
    readonly SvgIcon = SvgIcon;

    onPostDateChange(date: Date | null): void {
        if (!date) {
            return;
        }
        this.postDateChange.emit(date);
    }

    onUpdatePost(): void {
        this.updatePost.emit();
    }

    onOpenSocialLink(): void {
        this.openSocialLink.emit();
    }

    onDeletePost(): void {
        this.deletePost.emit();
    }
}
