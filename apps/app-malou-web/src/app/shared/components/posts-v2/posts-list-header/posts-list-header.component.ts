import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, input, model, output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxChange, MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';

@Component({
    selector: 'app-posts-list-header',
    templateUrl: './posts-list-header.component.html',
    styleUrls: ['./posts-list-header.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        EnumTranslatePipe,
        MatButtonModule,
        MatCheckboxModule,
        MatIconModule,
        MatMenuModule,
        MatTooltipModule,
        NgClass,
        TranslateModule,
    ],
})
export class PostsListHeaderComponent<T extends string> {
    readonly selectedFilter = model.required<T>();
    readonly errorFilter = input.required<T>();
    readonly allFilter = input.required<T>();
    readonly filterOptionsAndCount = input.required<{ filterOption: T; count: number | null }[]>();
    readonly deleteButtonId = input.required<string>();
    readonly filterOptionsIdFn = input.required<(filterOption: T) => string>();

    readonly atLeastOnePostInList = input.required<boolean>();

    readonly isSelecting = input.required<boolean>();
    readonly allPostsSelected = input.required<boolean>();

    readonly duplicateTooltip = input.required<string>();
    readonly deleteTooltip = input.required<string>();

    readonly userCanManagePost = input.required<boolean>();

    readonly deleteSelection = output<void>();
    readonly setIsSelecting = output<boolean>();
    readonly toggleSelectAll = output<boolean>();

    readonly SvgIcon = SvgIcon;

    readonly totalPostsCount = computed(() => {
        const filterOption = this.selectedFilter();
        return this.filterOptionsAndCount().find((filterOptionCount) => filterOptionCount.filterOption === filterOption)?.count ?? 0;
    });

    readonly canDuplicate = computed(() => this.duplicateTooltip() === '');
    readonly canDelete = computed(() => this.deleteTooltip() === '');

    selectFilter(filterOption: T): void {
        this.selectedFilter.set(filterOption);
    }

    onDeleteSelection(): void {
        this.deleteSelection.emit();
    }

    selectAll(event: MatCheckboxChange): void {
        this.toggleSelectAll.emit(event.checked);
    }
}
