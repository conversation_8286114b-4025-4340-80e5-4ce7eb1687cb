<div class="flex items-center justify-between border-b border-malou-color-border-primary bg-malou-color-background-white px-6 py-2">
    @if (isSelecting()) {
        <div>
            <mat-checkbox color="primary" [checked]="allPostsSelected()" (click)="$event.stopPropagation()" (change)="selectAll($event)">
                @if (allPostsSelected()) {
                    <span class="malou-text-12--semibold ml-2.5 text-malou-color-primary">
                        {{ 'common.unselect_all_with_count' | translate: { count: totalPostsCount() } }}
                    </span>
                } @else {
                    <span class="malou-text-12--semibold ml-2.5 text-malou-color-primary">
                        {{ 'common.select_all_with_count' | translate: { count: totalPostsCount() } }}
                    </span>
                }
            </mat-checkbox>
        </div>
        <div class="flex h-[50px] items-center gap-x-2">
            @if (atLeastOnePostInList()) {
                <button class="malou-btn-flat" mat-button (click)="setIsSelecting.emit(false)">
                    {{ 'common.cancel' | translate }}
                </button>

                <div [matTooltip]="userCanManagePost() ? duplicateTooltip() : ('casl.wrong_role' | translate)">
                    <button
                        class="malou-btn-icon--secondary"
                        mat-icon-button
                        [matMenuTriggerFor]="duplicateActions"
                        [disabled]="!canDuplicate() || !userCanManagePost">
                        <mat-icon color="primary" [svgIcon]="SvgIcon.DUPLICATE"></mat-icon>
                    </button>
                </div>

                <div [matTooltip]="userCanManagePost() ? deleteTooltip() : ('casl.wrong_role' | translate)">
                    <button
                        class="malou-btn-icon--secondary"
                        mat-icon-button
                        [id]="deleteButtonId()"
                        [disabled]="!canDelete() || !userCanManagePost"
                        (click)="onDeleteSelection()">
                        <mat-icon color="warn" [svgIcon]="SvgIcon.TRASH"></mat-icon>
                    </button>
                </div>
            }
        </div>
    } @else {
        <div class="flex items-center gap-x-1">
            @for (filterOptionAndCount of filterOptionsAndCount(); track filterOptionAndCount.filterOption) {
                @let filterOption = filterOptionAndCount.filterOption;
                @let count = filterOptionAndCount.count;
                <div
                    class="flex cursor-pointer gap-x-[2px] rounded p-2 text-malou-color-text-1"
                    [id]="filterOptionsIdFn()(filterOption)"
                    [ngClass]="{
                        'malou-text-12--regular': filterOption !== selectedFilter(),
                        'malou-text-12--semibold bg-malou-color-background-dark': filterOption === selectedFilter(),
                        '!text-malou-color-chart-pink--accent': filterOption === errorFilter(),
                    }"
                    (click)="selectFilter(filterOption)">
                    <div>{{ filterOption | enumTranslate: 'social_posts_list_filter' }}</div>
                    @if (count && filterOption !== allFilter()) {
                        <div class="italic">({{ count }})</div>
                    }
                </div>
            }
        </div>

        <div class="flex items-center gap-x-1">
            <button class="malou-btn-flat" mat-button (click)="setIsSelecting.emit(true)">
                {{ 'common.select' | translate }}
            </button>

            <ng-content select="[createButton]"></ng-content>
        </div>
    }
</div>

<mat-menu class="malou-mat-menu malou-box-shadow !rounded-[10px]" #duplicateActions="matMenu">
    <ng-content select="[duplicateActions]"></ng-content>
</mat-menu>
