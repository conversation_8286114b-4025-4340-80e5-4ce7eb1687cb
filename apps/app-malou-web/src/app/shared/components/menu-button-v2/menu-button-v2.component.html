<app-button
    [id]="id()"
    [text]="text()"
    [tooltip]="tooltip()"
    [disabled]="disabled()"
    [loading]="loading()"
    [buttonClasses]="customButtonClasses()"
    [customStyle]="customStyle"
    [testId]="testId()"
    [matMenuTriggerFor]="matMenu"
    [isSquareButton]="true"
    [ngClass]="{ 'pointer-events-none': disabled() || loading() }">
    <ng-template #textTemplate>
        <div
            class="flex items-center gap-x-3"
            [ngClass]="{ 'h-[36px]': size() === MenuButtonSize.MEDIUM, 'h-[50px]': size() === MenuButtonSize.LARGE }">
            <div class="malou-text-12--medium flex h-full items-center">
                {{ text() }}
            </div>
            <div class="flex items-center">
                <mat-icon class="!w-4" [svgIcon]="SvgIcon.CHEVRON_DOWN"></mat-icon>
            </div>
        </div>
    </ng-template>
</app-button>

<mat-menu class="malou-mat-menu malou-box-shadow !rounded-xl" #matMenu="matMenu">
    <ng-content></ng-content>
</mat-menu>
