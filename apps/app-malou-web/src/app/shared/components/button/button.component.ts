import { Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, contentChild, input, output, TemplateRef } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';

@Component({
    selector: 'app-button',
    templateUrl: './button.component.html',
    styleUrls: ['./button.component.scss'],
    imports: [NgClass, NgTemplateOutlet, MatButtonModule, MatTooltipModule, MalouSpinnerComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ButtonComponent {
    readonly id = input<string>('');
    readonly text = input<string>('');
    readonly loading = input<boolean>(false);
    readonly disabled = input<boolean>(false);
    readonly buttonClasses = input<string>('');
    readonly customStyle = input<string>(''); // PLEASE USE buttonClasses INSTEAD, THIS SHOULD BE USED ONLY FOR EXCEPTIONAL CASES
    readonly tooltip = input<string>('');
    readonly testId = input<string>('');
    readonly theme = input<'primary' | 'secondary' | 'secondary--alt' | 'null'>('primary');
    readonly isSquareButton = input<boolean>(false);
    readonly size = input<'medium' | 'large'>('medium');

    readonly buttonClick = output<void>();

    readonly textTemplate = contentChild<TemplateRef<any>>('textTemplate');

    readonly loaderColor = computed(() => (this.theme() === 'secondary--alt' ? '#ac32b7' : 'white'));
    readonly themeCssClass = computed(() => `malou-btn-raised--${this.theme()}`);

    onClick(): void {
        if (this.loading() || this.disabled()) {
            return;
        }

        this.buttonClick.emit();
    }
}
