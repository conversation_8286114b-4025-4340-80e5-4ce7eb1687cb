import { ChangeDetectionStrategy, Component, effect, inject, input, output, WritableSignal } from '@angular/core';
import { FormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';

import { ApplicationLanguage, IBreakdown, KeywordScoreTextType } from '@malou-io/package-utils';

import { AnswerReviewKeywordScoreGaugeComponent } from ':shared/components/keywords-score-gauge/answer-review-keyword-score-gauge/answer-review-keyword-score-gauge.component';
import { DetailedKeywordScoreGaugeComponent } from ':shared/components/keywords-score-gauge/detailed-keyword-score-gauge/detailed-keyword-score-gauge.component';
import { KeywordsScoreGaugeContext } from ':shared/components/keywords-score-gauge/keywords-score-gauge.context';
import { Indication, KeywordsScoreGaugeType } from ':shared/components/keywords-score-gauge/keywords-score-gauge.interface';
import { SimpleKeywordScoreGaugeComponent } from ':shared/components/keywords-score-gauge/simple-keyword-score-gauge/simple-keyword-score-gauge.component';
import { DEFAULT_REVIEWER_NAME_VALIDATION, Keyword, Restaurant, ReviewerNameValidation } from ':shared/models/';
import { RestaurantAiSettings } from ':shared/models/restaurant-ai-settings';

@Component({
    selector: 'app-keywords-score-gauge',
    templateUrl: './keywords-score-gauge.component.html',
    styleUrls: ['./keywords-score-gauge.component.scss'],
    imports: [SimpleKeywordScoreGaugeComponent, DetailedKeywordScoreGaugeComponent, AnswerReviewKeywordScoreGaugeComponent],
    providers: [KeywordsScoreGaugeContext],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class KeywordsScoreGaugeComponent {
    readonly title = input<string | null>('');
    readonly type = input<KeywordsScoreGaugeType>(KeywordsScoreGaugeType.SIMPLE);
    readonly langOptions = input<(ApplicationLanguage | string)[]>(Object.values(ApplicationLanguage));
    readonly text = input<string>();
    readonly textType = input<KeywordScoreTextType>();
    readonly restaurant = input<Restaurant | null>(null);
    readonly keywords = input<Keyword[]>([]);
    readonly lang = input<string | null>(null);
    readonly responseTime = input<number>(0);
    readonly reviewerName = input<string | undefined>('');
    readonly parentElementId = input<string | null>(null);
    readonly shouldCacheScore = input<boolean>(true);
    readonly areDisabledBricks = input<boolean>(false);
    readonly restaurantAiSettings = input<RestaurantAiSettings | undefined>(undefined);
    readonly savedKeywordsScore = input<number | null>(null);
    readonly relevantBricks = input<IBreakdown[]>([]);
    readonly reviewerNameValidation = input<ReviewerNameValidation>(DEFAULT_REVIEWER_NAME_VALIDATION);

    readonly addKeyword = output<string>();
    readonly indicationListChanged = output<Indication[]>();
    readonly langChanged = output<string>();

    private readonly _translateService = inject(TranslateService);
    public readonly keywordsScoreGaugeContext = inject(KeywordsScoreGaugeContext);

    readonly KeywordsScoreGaugeType = KeywordsScoreGaugeType;

    constructor() {
        this.keywordsScoreGaugeContext.setAddKeywordCallback((value) => this.addKeyword.emit(value));
        this.keywordsScoreGaugeContext.setIndicationListChangedCallback((value) => this.indicationListChanged.emit(value));
        this.keywordsScoreGaugeContext.setLangChangedCallback((value) => this.langChanged.emit(value));

        // Sync inputs with context
        effect(() =>
            this.keywordsScoreGaugeContext.title.set(
                this.title() === null ? '' : this.title() || this._translateService.instant('keywords.score')
            )
        );
        effect(() => this.keywordsScoreGaugeContext.type.set(this.type()));
        effect(() => this.keywordsScoreGaugeContext.langOptions.set(this.langOptions()));
        effect(() => this.keywordsScoreGaugeContext.text.set(this.text()));
        effect(() => this.keywordsScoreGaugeContext.textType.set(this.textType()));
        effect(() => this.keywordsScoreGaugeContext.restaurant.set(this.restaurant()));
        effect(() => this.keywordsScoreGaugeContext.keywords.set(this.keywords()));
        effect(() => this.keywordsScoreGaugeContext.lang.set(this.lang()));
        effect(() => this.keywordsScoreGaugeContext.responseTime.set(this.responseTime()));
        effect(() => this.keywordsScoreGaugeContext.reviewerName.set(this.reviewerName()));
        effect(() => this.keywordsScoreGaugeContext.parentElementId.set(this.parentElementId()));
        effect(() => this.keywordsScoreGaugeContext.shouldCacheScore.set(this.shouldCacheScore()));
        effect(() => this.keywordsScoreGaugeContext.areDisabledBricks.set(this.areDisabledBricks()));
        effect(() => this.keywordsScoreGaugeContext.restaurantAiSettings.set(this.restaurantAiSettings()));
        effect(() => this.keywordsScoreGaugeContext.savedKeywordsScore.set(this.savedKeywordsScore()));
        effect(() => this.keywordsScoreGaugeContext.relevantBricks.set(this.relevantBricks()));
        effect(() => this.keywordsScoreGaugeContext.reviewerNameValidation.set(this.reviewerNameValidation()));
    }

    // Expose context properties for backward compatibility
    get score(): WritableSignal<number> {
        return this.keywordsScoreGaugeContext.score;
    }

    get bricksFound(): WritableSignal<string[]> {
        return this.keywordsScoreGaugeContext.bricksFound;
    }

    get brickLangControl(): FormControl<string> {
        return this.keywordsScoreGaugeContext.brickLangControl;
    }
}
