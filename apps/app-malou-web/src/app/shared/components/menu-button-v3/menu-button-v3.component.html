<div class="flex items-center justify-center" [ngClass]="wrapperClassesComputed()">
    <app-button
        [id]="id()"
        [text]="text()"
        [tooltip]="tooltip()"
        [disabled]="isMainButtonDisabled()"
        [loading]="loading()"
        [buttonClasses]="mainButtonClassesComputed()"
        [customStyle]="customStyle"
        [testId]="testId()"
        [ngClass]="{ 'pointer-events-none': isMainButtonDisabled() || loading() }"
        (buttonClick)="onMainButtonClick.emit()">
        <ng-template #textTemplate>
            <div
                class="flex items-center gap-x-3"
                [ngClass]="{ 'h-[36px]': size() === MenuButtonSize.MEDIUM, 'h-[50px]': size() === MenuButtonSize.LARGE }">
                <div class="malou-text-12--medium flex h-full items-center">
                    {{ text() }}
                </div>
            </div>
        </ng-template>
    </app-button>
    <div class="!w-[0.5px] bg-white/50"></div>
    <app-button
        [disabled]="isMenuButtonDisabled()"
        [loading]="loading()"
        [customStyle]="customStyle"
        [buttonClasses]="menuButtonClassesComputed()"
        [matMenuTriggerFor]="matMenu"
        [ngClass]="{ 'pointer-events-none': isMenuButtonDisabled() || loading() }">
        <ng-template #textTemplate>
            <div
                class="flex items-center gap-x-3"
                [ngClass]="{ 'h-[36px]': size() === MenuButtonSize.MEDIUM, 'h-[50px]': size() === MenuButtonSize.LARGE }">
                <div class="flex items-center">
                    <mat-icon class="!w-4.5" [svgIcon]="SvgIcon.CHEVRON_DOWN"></mat-icon>
                </div>
            </div>
        </ng-template>
    </app-button>

    <mat-menu class="malou-mat-menu malou-box-shadow !rounded-xl" #matMenu="matMenu">
        <ng-content></ng-content>
    </mat-menu>
</div>
