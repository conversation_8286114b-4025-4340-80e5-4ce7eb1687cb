import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, input, output } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';

import { ButtonComponent } from ':shared/components/button/button.component';
import { MenuButtonSize } from ':shared/components/menu-button-v2/menu-button-v2.interface';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-menu-button-v3',
    templateUrl: './menu-button-v3.component.html',
    styleUrls: ['./menu-button-v3.component.scss'],
    imports: [NgClass, MatIconModule, MatMenuModule, ButtonComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MenuButtonV2Component {
    readonly id = input<string>('');
    readonly text = input<string>('');
    readonly loading = input<boolean>(false);
    readonly isMainButtonDisabled = input<boolean>(false);
    readonly isMenuButtonDisabled = input<boolean>(false);

    readonly wrapperClasses = input<string>('');
    readonly mainButtonClasses = input<string>('');
    readonly menuButtonClasses = input<string>('');
    readonly tooltip = input<string>('');
    readonly testId = input<string>('');
    readonly size = input<MenuButtonSize>(MenuButtonSize.MEDIUM);

    readonly onMainButtonClick = output<void>();

    readonly wrapperClassesComputed = computed(
        () =>
            `flex items-center justify-center ${this.wrapperClasses()} ${this.size() === MenuButtonSize.LARGE ? 'h-[50px]' : 'h-[36px]'}
            ${this.size() === MenuButtonSize.MEDIUM ? 'min-w-[150px]' : 'min-w-[200px]'}`
    );
    readonly mainButtonClassesComputed = computed(
        () =>
            `!shadow-none !py-0 !rounded-l-[5px] !rounded-r-none ${this.size() === MenuButtonSize.LARGE ? 'min-h-[50px]' : ''} ${this.mainButtonClasses()}`
    );

    readonly menuButtonClassesComputed = computed(
        () =>
            `!shadow-none !py-0 !rounded-r-[5px] !rounded-l-none ${this.size() === MenuButtonSize.LARGE ? 'min-h-[50px]' : ''} ${this.menuButtonClasses()}`
    );

    readonly customStyle = 'box-shadow: none !important;';

    readonly MenuButtonSize = MenuButtonSize;
    readonly SvgIcon = SvgIcon;
}
