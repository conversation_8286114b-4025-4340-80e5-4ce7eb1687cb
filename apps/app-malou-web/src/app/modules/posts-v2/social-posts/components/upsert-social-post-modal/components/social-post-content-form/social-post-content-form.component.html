<div class="flex h-full flex-col gap-6 overflow-y-auto px-6 py-5 lg:px-10 lg:py-8">
    <div class="malou-text-14--semibold text-malou-color-text-1">
        {{ 'social_posts.upsert_social_post_modal.content_form.section_title' | translate }}
    </div>

    <div class="flex flex-col gap-2">
        <div class="malou-text-10--regular text-malou-color-text-2">
            {{ 'social_posts.upsert_social_post_modal.content_form.media.title' | translate }}
        </div>

        <app-social-post-medias
            [medias]="attachments()"
            [publicationType]="publicationType()"
            [isReadonly]="isReadonly()"
            (mediasChange)="onMediaChange($event)"></app-social-post-medias>
    </div>

    @let reelAttachmentValue = reelAttachment();
    @if (publicationType() === PublicationType.REEL && reelAttachmentValue && (isInstagramSelected() || isTiktokSelected())) {
        <div class="flex flex-col gap-2">
            <div class="malou-text-10--regular text-malou-color-text-2">
                {{ 'social_posts.upsert_social_post_modal.content_form.reel_thumbnail.title' | translate }}
            </div>

            <app-reel-thumbnail [media]="reelAttachmentValue" [isReadonly]="isReadonly()"></app-reel-thumbnail>
        </div>
    }

    @if (isMapstrSelected() || (isTiktokSelected() && !isReel())) {
        <div class="flex flex-col gap-2">
            <div class="malou-text-10--regular flex items-center gap-x-1 text-malou-color-text-2">
                {{ 'social_posts.upsert_social_post_modal.content_form.title.title' | translate }}
                @if (isMapstrSelected()) {
                    *
                }
                @if (isMapstrSelected() && !(isTiktokSelected() && !isReel())) {
                    <mat-icon
                        class="!h-3 !w-3"
                        [svgIcon]="SvgIcon.INFO_ROUND"
                        [matTooltip]="'social_posts.upsert_social_post_modal.content_form.title.only_for_mapstr' | translate"></mat-icon>
                } @else if (!isMapstrSelected() && isTiktokSelected() && !isReel()) {
                    <mat-icon
                        class="!h-3 !w-3"
                        [svgIcon]="SvgIcon.INFO_ROUND"
                        [matTooltip]="'social_posts.upsert_social_post_modal.content_form.title.only_for_tiktok' | translate"></mat-icon>
                } @else if (isMapstrSelected() && isTiktokSelected() && !isReel()) {
                    <mat-icon
                        class="!h-3 !w-3"
                        [svgIcon]="SvgIcon.INFO_ROUND"
                        [matTooltip]="
                            'social_posts.upsert_social_post_modal.content_form.title.for_mapstr_and_tiktok' | translate
                        "></mat-icon>
                }
            </div>

            <app-input-text
                autocomplete="off"
                [defaultValue]="postTitle()"
                [maxLength]="titleMaxLength()"
                [showMaxLength]="true"
                [disabled]="isReadonly()"
                (inputTextChange)="onTitleChange($event)"></app-input-text>
        </div>
    }

    <div class="flex flex-col gap-2">
        <div class="malou-text-10--regular text-malou-color-text-2">
            {{ 'social_posts.upsert_social_post_modal.content_form.caption.title' | translate }}
        </div>

        <app-social-post-caption
            [value]="postCaption()"
            [isMapstrPlatformChecked]="isMapstrSelected()"
            [isReel]="false"
            [isDisabled]="isReadonly()"
            [showHashtags]="areFacebookOrInstagramSelected() || isTiktokSelected()"
            (valueChange)="onCaptionChange($event)"></app-social-post-caption>
    </div>

    @if (areFacebookOrInstagramSelected()) {
        <div class="flex flex-col gap-2">
            <div class="malou-text-10--regular malou-color-text-2 flex items-center gap-x-1">
                <div>
                    {{ 'social_posts.upsert_social_post_modal.content_form.place.title' | translate }}
                </div>
                @if (isMapstrSelected()) {
                    <mat-icon
                        class="malou-color-text-2 !h-4 !w-4"
                        [svgIcon]="SvgIcon.INFO_ROUND"
                        [matTooltip]="'social_posts.new_social_post.location_only_for_facebook_and_instagram' | translate"></mat-icon>
                }
            </div>
            <app-social-post-place
                [location]="postLocation()"
                [platformSocialId]="facebookPlatformSocialId()"
                [isDisabled]="!facebookPlatformSocialId() || isReadonly()"
                [isLoadingLocation]="isLoadingLocation()"
                (locationChange)="onLocationChange($event)"></app-social-post-place>
        </div>
    }

    @if (isMapstrSelected()) {
        <div class="flex flex-col gap-2">
            <div class="malou-text-10--regular flex items-center gap-x-1 text-malou-color-text-2">
                <div>
                    {{ 'social_posts.upsert_social_post_modal.content_form.cta.title' | translate }}
                </div>
                <mat-icon
                    class="malou-color-text-2 !h-4 !w-4"
                    [svgIcon]="SvgIcon.INFO_ROUND"
                    [matTooltip]="'social_posts.new_social_post.button_cta_only_for_mapstr' | translate"></mat-icon>
            </div>
            <app-social-post-cta></app-social-post-cta>
        </div>
    }

    @if (isReel() && isInstagramSelected()) {
        <div class="flex flex-col gap-2">
            <div class="malou-text-10--regular flex gap-x-[2px] text-malou-color-text-2">
                <span>{{ 'social_posts.upsert_social_post_modal.content_form.user_tags.title' | translate }}</span>
                <span class="italic">{{ 'social_posts.upsert_social_post_modal.content_form.user_tags.subtitle' | translate }}</span>
            </div>
            <app-tag-users-input></app-tag-users-input>
        </div>
    }

    @if (isInstagramSelected()) {
        <div class="flex flex-col gap-2">
            <div class="malou-text-10--regular flex gap-x-[2px] text-malou-color-text-2">
                <span>{{ 'social_posts.upsert_social_post_modal.content_form.collaborators.title' | translate }}</span>
                <span class="italic">{{ 'social_posts.upsert_social_post_modal.content_form.collaborators.subtitle' | translate }}</span>
                <mat-icon
                    class="malou-color-text-2 !h-4 !w-4"
                    [svgIcon]="SvgIcon.INFO_ROUND"
                    [matTooltip]="'social_posts.upsert_social_post_modal.content_form.collaborators.info' | translate"></mat-icon>
            </div>
            <app-instagram-collaborators-input></app-instagram-collaborators-input>
        </div>
    }

    @if (isTiktokSelected()) {
        <app-tiktok-options
            [isDisabled]="isReadonly()"
            [tiktokOptions]="tiktokOptions()"
            [isTiktokCommentDisabled]="isTiktokCommentDisabled()"
            [isTiktokDuetDisabled]="isTiktokDuetDisabled()"
            [isTiktokStitchDisabled]="isTiktokStitchDisabled()"
            [isReel]="isReel()"
            (contentDisclosureSettingsBrandedContentChange)="onContentDisclosureSettingsBrandedContentChange($event)"
            (contentDisclosureSettingsIsActivatedChange)="onContentDisclosureSettingsIsActivatedChange($event)"
            (contentDisclosureSettingsYourBrandChange)="onContentDisclosureSettingsYourBrandChange($event)"
            (interactionAbilityCommentChange)="onCommentChange($event)"
            (interactionAbilityDuetChange)="onDuetChange($event)"
            (interactionAbilityStitchChange)="onStitchChange($event)"
            (privacyStatusChange)="onPrivacyStatusChange($event)"
            (autoAddMusicChange)="onAutoAddMusicChange($event)"></app-tiktok-options>
    }
</div>
