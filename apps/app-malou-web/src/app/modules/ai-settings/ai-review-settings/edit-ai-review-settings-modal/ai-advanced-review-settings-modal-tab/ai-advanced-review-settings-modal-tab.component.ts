import { ChangeDetectionStrategy, Component, inject, input, output, Signal, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

import { CustomerNaming, FrenchTutoiementVouvoiement } from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';
import { SelectChipListComponent } from ':shared/components/select-chip-list/select-chip-list.component';
import { TextAreaComponent } from ':shared/components/text-area/text-area.component';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';

@Component({
    selector: 'app-ai-advanced-review-settings-modal-tab',
    templateUrl: './ai-advanced-review-settings-modal-tab.component.html',
    styleUrls: ['./ai-advanced-review-settings-modal-tab.component.scss'],
    imports: [
        MatCheckboxModule,
        MatIconModule,
        TranslateModule,
        SelectChipListComponent,
        ApplyPurePipe,
        FormsModule,
        ReactiveFormsModule,
        ApplyPurePipe,
        TextAreaComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AiAdvancedReviewSettingsModalTabComponent {
    private readonly _experimentationService = inject(ExperimentationService);

    readonly aiReviewSettingsForm = input.required<
        FormGroup<{
            replyTone: FormControl<FrenchTutoiementVouvoiement>;
            catchphrase: FormControl<string>;
            customerNaming: FormControl<CustomerNaming>;
            shouldTranslateCatchphrase: FormControl<boolean>;
            signatures: FormArray<FormControl<string>>;
            restaurantKeywordIds: FormControl<string[]>;
            forbiddenWords: FormControl<string[]>;
            shouldTranslateSignature: FormControl<boolean>;
            prompt: FormControl<string | null>;
        }>
    >();

    get restaurantKeywordIds(): FormControl<string[]> {
        return this.aiReviewSettingsForm().controls['restaurantKeywordIds'] as FormControl<string[]>;
    }

    readonly close = output();

    readonly SvgIcon = SvgIcon;

    readonly MAX_FORBIDDEN_WORDS = 10;
    readonly PROMPT_MAX_LENGTH = 1500;

    readonly isFirstTimeUpdate = signal<boolean>(true);

    readonly isReviewsAiSettingsCustomPromptEnabled: Signal<boolean> = toSignal(
        this._experimentationService.isFeatureEnabledForRestaurant$('release-reviews-ai-settings-custom-prompt'),
        { initialValue: false }
    );

    displayForbiddenWordWith = (forbiddenWord: string): string => forbiddenWord;

    buildForbiddenWordFromText = (text: string): string => text.trim();
}
