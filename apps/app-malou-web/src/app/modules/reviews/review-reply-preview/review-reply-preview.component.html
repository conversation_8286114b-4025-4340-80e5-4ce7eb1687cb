<div class="malou-color-background-purple flex gap-3 rounded-lg p-6">
    <div class="flex flex-col justify-end">
        <label class="malou-text-12--semibold malou-color-text-1">{{ 'keywords_score.score' | translate }}</label>
        <app-keywords-score-gauge
            [text]="replyText()"
            [title]="null"
            [restaurant]="restaurant()"
            [textType]="gaugeTextType()"
            [lang]="computedLang()"
            [keywords]="restaurantKeywords()"
            [reviewerName]="reviewerName()"
            [responseTime]="responseTime()"
            [restaurantAiSettings]="restaurantAiSettings()"
            [savedKeywordsScore]="reviewCommentSavedScore()"
            [reviewerNameValidation]="reviewerNameValidation()"
            [type]="KeywordsScoreGaugeType.SIMPLE_AS_COLUMN">
        </app-keywords-score-gauge>
    </div>
    <div class="malou-card__vertical-separator"></div>
    <div class="flex flex-col gap-4">
        <span
            class="malou-text-10--regular malou-color-text-2 italic"
            [ngClass]="{ 'malou-text-12--regular whitespace-pre-wrap text-malou-color-text-1': isReplySectionHidden() }"
            [innerHTML]="replyText()">
        </span>
        <span
            class="malou-text-12--semibold cursor-pointer text-malou-color-primary"
            data-testid="hide-reply-section-button"
            [class.hidden]="!isReplySectionHidden()"
            (click)="onEditButton()"
            >{{ 'common.edit' | translate }}</span
        >
    </div>
</div>
