import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, input, model } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { APP_DEFAULT_LANGUAGE, getTimeDifferenceInHours, KeywordScoreTextType, MIN_POSITIVE_REVIEW_RATING } from '@malou-io/package-utils';

import { KeywordsScoreGaugeComponent } from ':shared/components/keywords-score-gauge/keywords-score-gauge.component';
import { KeywordsScoreGaugeType } from ':shared/components/keywords-score-gauge/keywords-score-gauge.interface';
import { DEFAULT_REVIEWER_NAME_VALIDATION, Keyword, Restaurant, ReviewerNameValidation } from ':shared/models';
import { RestaurantAiSettings } from ':shared/models/restaurant-ai-settings';

@Component({
    selector: 'app-review-reply-preview',
    templateUrl: './review-reply-preview.component.html',
    styleUrls: ['./review-reply-preview.component.scss'],
    imports: [KeywordsScoreGaugeComponent, TranslateModule, NgClass],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewReplyPreviewComponent {
    readonly restaurant = input<Restaurant | null>(null);
    readonly restaurantKeywords = input<Keyword[]>([]);
    readonly restaurantAiSettings = input<RestaurantAiSettings | undefined>(undefined);
    readonly reviewerName = input<string>('');
    readonly reviewCommentSavedScore = input<number | null>(null);
    readonly rating = input.required<number>();
    readonly replyText = input.required<string>();
    readonly keywordsLang = input<string | undefined>();
    readonly lang = input<string | undefined>();
    readonly reviewSocialCreatedAt = input.required<Date>();
    readonly commentDate = input.required<Date | string | null>();
    readonly reviewerNameValidation = input<ReviewerNameValidation>(DEFAULT_REVIEWER_NAME_VALIDATION);
    readonly isReplySectionHidden = model(false);

    readonly KeywordsScoreGaugeType = KeywordsScoreGaugeType;

    readonly gaugeTextType = computed(() => this._getTextTypeScore(this.rating()));
    readonly computedLang = computed(() => this.keywordsLang() ?? this.lang() ?? APP_DEFAULT_LANGUAGE);
    readonly responseTime = computed(() => this._getResponseTime(this.reviewSocialCreatedAt(), this.commentDate()));

    onEditButton(): void {
        this.isReplySectionHidden.update((value) => !value);
    }

    private _getTextTypeScore(rating: number): KeywordScoreTextType {
        return rating < MIN_POSITIVE_REVIEW_RATING ? KeywordScoreTextType.LOW_RATE_REVIEW : KeywordScoreTextType.HIGH_RATE_REVIEW;
    }

    private _getResponseTime(socialCreatedAt: Date, commentDate: Date | string | null): number {
        commentDate = typeof commentDate === 'string' ? new Date(commentDate) : commentDate;
        return Math.floor(getTimeDifferenceInHours(commentDate ?? new Date(), socialCreatedAt));
    }
}
