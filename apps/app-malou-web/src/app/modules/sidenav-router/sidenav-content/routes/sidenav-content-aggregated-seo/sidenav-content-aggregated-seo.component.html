<app-sidenav-content-route-group
    [options]="{
        routerLink: ['/groups', 'seo'],
        leftPart: {
            text: 'sidenav_content.seo' | translate,
            colorClassWhenActive: 'malou-color-text-1',
            svgIcon: SvgIcon.MAGNIFYING_GLASS,
            svgIconSize: 'medium',
        },
    }"
    [childrenOptions]="[
        {
            routerLink: ['/groups', 'seo', 'storeLocator'],
            leftPart: {
                text: 'sidenav_content.store_locator' | translate,
                colorClassWhenActive: 'malou-color-primary',
                svgIcon: SvgIcon.DOT,
                svgIconSize: 'small',
                hideIconWhenActive: true,
            },
        },
    ]">
</app-sidenav-content-route-group>
