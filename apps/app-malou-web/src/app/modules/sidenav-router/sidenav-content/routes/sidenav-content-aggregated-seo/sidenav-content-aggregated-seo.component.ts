import { ChangeDetectionStrategy, Component } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { SidenavContentRouteGroupComponent } from ':modules/sidenav-router/sidenav-content/sidenav-content-route-group/sidenav-content-route-group.component';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-sidenav-content-aggregated-seo',
    imports: [SidenavContentRouteGroupComponent, TranslateModule],
    templateUrl: './sidenav-content-aggregated-seo.component.html',
    styleUrl: './sidenav-content-aggregated-seo.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SidenavContentAggregatedSeoComponent {
    readonly SvgIcon = SvgIcon;
}
