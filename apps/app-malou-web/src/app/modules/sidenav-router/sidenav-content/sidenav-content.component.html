<div
    class="width-transition flex h-full flex-col md:!w-screen"
    [ngClass]="{ 'w-[250px]': isSidenavOpened(), 'w-[80px]': !isSidenavOpened() }">
    <div class="flex min-h-0 grow flex-col">
        @if (!isSidenavOpened()) {
            <ng-container [ngTemplateOutlet]="openPanelButtonTemplate"></ng-container>
        }
        <div [class.mt-4]="isSidenavOpened()">
            <app-sidenav-content-select-restaurant [isEmptyState]="!!isRestaurantsListRoute()"></app-sidenav-content-select-restaurant>
        </div>
        @if (isAllRestaurantsSelected() && !isRestaurantsListRoute()) {
            <ng-container [ngTemplateOutlet]="groupRoutesTemplate"></ng-container>
        } @else {
            <ng-container [ngTemplateOutlet]="routesTemplate"></ng-container>
        }
        <ng-container [ngTemplateOutlet]="separatorTemplate"></ng-container>
        <ng-container [ngTemplateOutlet]="adminRouteTemplate"></ng-container>
    </div>
    <div>
        <ng-container [ngTemplateOutlet]="chatbotLogoTemplate"></ng-container>
        <ng-container [ngTemplateOutlet]="malouLogoTemplate"></ng-container>
    </div>
</div>

<ng-template #openPanelButtonTemplate>
    <div class="mx-4 my-1 flex justify-end">
        <app-sidenav-toggle-button
            [svgIconWhenClosed]="SvgIcon.LEFT_PANEL_CLOSE"
            [svgIconWhenOpen]="SvgIcon.LEFT_PANEL_CLOSE"></app-sidenav-toggle-button>
    </div>
</ng-template>

<ng-template #routesTemplate>
    @let restaurant = selectedRestaurant();
    <div
        class="hide-scrollbar flex flex-col gap-y-2 overflow-y-auto p-4"
        appScrollState
        (verticalScrollState)="verticalScrollState.set($event)">
        @let base = ['/restaurants', restaurant?._id ?? ''];

        <app-sidenav-content-route
            [options]="{
                routerLink: base | concat: ['dashboard'],
                leftPart: {
                    text: 'sidenav_content.calendar' | translate,
                    colorClassWhenActive: 'malou-color-text-1',
                    svgIcon: SvgIcon.CALENDAR,
                    svgIconSize: 'medium',
                },
            }" />

        <app-sidenav-content-seo-routes></app-sidenav-content-seo-routes>

        @if (!isBrandBusiness()) {
            <app-sidenav-content-e-reputation-routes></app-sidenav-content-e-reputation-routes>
        }

        <app-sidenav-content-boosters-routes></app-sidenav-content-boosters-routes>

        <app-sidenav-content-social-networks-routes></app-sidenav-content-social-networks-routes>

        <app-sidenav-content-interactions-routes></app-sidenav-content-interactions-routes>

        <app-sidenav-content-insights-routes></app-sidenav-content-insights-routes>

        <div class="h-3 shrink-0"></div>

        <app-sidenav-content-resources-routes></app-sidenav-content-resources-routes>

        <app-sidenav-content-settings-routes></app-sidenav-content-settings-routes>
    </div>
</ng-template>

<ng-template #groupRoutesTemplate>
    <div class="hide-scrollbar flex flex-col gap-y-2 overflow-y-auto p-4">
        @if (isFeatureEnabledForUser()) {
            <app-sidenav-content-aggregated-seo></app-sidenav-content-aggregated-seo>
        }

        <app-sidenav-content-route
            [options]="{
                routerLink: ['/groups', 'reputation'],
                leftPart: {
                    text: 'sidenav_content.e_reputation' | translate,
                    colorClassWhenActive: 'malou-color-text-1',
                    svgIcon: SvgIcon.STAR_BORDER,
                    svgIconSize: 'medium',
                },
            }" />

        @if (!this.isPhoneScreen() || !hasOneRestaurantWithBoosterPackActivated()) {
            <app-sidenav-content-route
                [options]="{
                    routerLink: !hasOneRestaurantWithBoosterPackActivated()
                        ? ['/groups', 'boosters', 'presentation']
                        : ['/groups', 'boosters'],
                    leftPart: {
                        text: 'sidenav_content.boosters' | translate,
                        colorClassWhenActive: 'malou-color-text-1',
                        svgIcon: SvgIcon.ROCKET,
                        svgIconSize: 'medium',
                        secondarySvgIcon: !hasOneRestaurantWithBoosterPackActivated() ? SvgIcon.CROWN : undefined,
                        secondarySvgIconColorClass: 'text-malou-color-state-warn',
                    },
                }" />
        }

        <app-sidenav-content-aggregated-insights-routes></app-sidenav-content-aggregated-insights-routes>
    </div>
</ng-template>

<ng-template #separatorTemplate>
    <div class="h-[1px] bg-malou-color-background-dark"></div>
</ng-template>

<ng-template #adminRouteTemplate>
    @if (userInfos()?.role === Role.ADMIN) {
        <div class="p-4" [ngClass]="{ 'box-shadow-top': ['atStart', 'scrolling'] | includes: verticalScrollState() }">
            <app-sidenav-content-route
                [options]="{
                    routerLink: ['admin'],
                    leftPart: {
                        text: 'sidenav_content.admin' | translate,
                        colorClassWhenActive: 'malou-color-text-1',
                        svgIcon: SvgIcon.FOLDER_CHECK,
                        svgIconSize: 'medium',
                    },
                }" />
        </div>
    }
</ng-template>

<ng-template #chatbotLogoTemplate>
    <div class="flex items-center justify-center">
        <app-chatbot-button></app-chatbot-button>
    </div>
</ng-template>

<ng-template #malouLogoTemplate>
    <div class="flex justify-center rounded py-4">
        <img
            class="h-5 w-auto"
            [ngClass]="{
                '!h-12': christmasLogo() && isSidenavOpened(),
            }"
            [src]="logo() | imagePathResolver" />
    </div>
</ng-template>
