import { Routes } from '@angular/router';

import { RoutePath } from '@malou-io/package-utils';

import { INFORMATIONS_ROUTES } from ':modules/informations/informations-routing';
import { POSTS_ROUTES } from ':modules/posts/posts.routing';

export const SEO_ROUTES: Routes = [
    {
        path: '',
        loadComponent: () => import('./seo.component').then((m) => m.SeoComponent),
        children: [
            {
                path: '',
                pathMatch: 'full',
                redirectTo: 'informations',
            },
            {
                path: 'informations',
                loadChildren: () => INFORMATIONS_ROUTES,
                data: { routePath: RoutePath.INFORMATIONS },
            },
            {
                path: 'posts',
                loadChildren: () => POSTS_ROUTES,
                data: { routePath: RoutePath.POSTS },
            },
        ],
    },
];
