import { Routes } from '@angular/router';

export const AGGREGATED_SEO_ROUTES: Routes = [
    {
        path: '',
        loadComponent: () => import('./aggregated-seo.component').then((m) => m.AggregatedSeoComponent),
        children: [
            {
                path: '',
                pathMatch: 'full',
                redirectTo: 'storeLocator',
            },
            {
                path: 'storeLocator',
                loadComponent: () => import('../store-locator/store-locator.component').then((m) => m.StoreLocatorComponent),
            },
        ],
    },
];
