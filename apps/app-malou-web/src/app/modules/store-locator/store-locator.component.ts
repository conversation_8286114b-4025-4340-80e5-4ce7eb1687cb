import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';

import { StoreLocatorJobType } from '@malou-io/package-utils';

import { StoreLocatorJobContext } from ':modules/store-locator/contexts/store-locator-jobs.context';
import { MarketingPageComponent } from ':modules/store-locator/marketing-page/marketing-page.component';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/models/store-locator-organization-config';
import { StoreLocatorJobState } from ':modules/store-locator/models/store-locator.interface';
import { OrganizationConfigurationComponent } from ':modules/store-locator/organization-configuration/organization-configuration.component';
import { SelectOrganizationComponent } from ':modules/store-locator/select-organization/select-organization.component';
import { StoreLocatorGlobalConfigComponent } from ':modules/store-locator/store-locator-global-config/store-locator-global-config.component';
import { StoreLocatorContext } from ':modules/store-locator/store-locator.context';
import { LoaderPageComponent } from ':shared/components/loader-page/loader-page.component';
import { Illustration } from ':shared/pipes/illustration-path-resolver.pipe';

@Component({
    selector: 'app-store-locator',
    templateUrl: './store-locator.component.html',
    styleUrls: ['./store-locator.component.scss'],
    imports: [
        NgTemplateOutlet,
        OrganizationConfigurationComponent,
        StoreLocatorGlobalConfigComponent,
        LoaderPageComponent,
        MatProgressSpinner,
        TranslateModule,
        SelectOrganizationComponent,
        MarketingPageComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorComponent {
    private readonly _storeLocatorContext = inject(StoreLocatorContext);
    private readonly _storeLocatorJobContext = inject(StoreLocatorJobContext);

    readonly Illustration = Illustration;

    readonly storeLocatorOrganizationConfiguration = computed<StoreLocatorOrganizationConfiguration | null>(() =>
        this._storeLocatorContext.storeLocatorOrganizationConfiguration()
    );
    readonly storeLocatorOrganizationRestaurants = computed(() => this._storeLocatorContext.storeLocatorOrganizationRestaurants());
    readonly organizationRestaurantKeywords = computed(() => this._storeLocatorContext.organizationRestaurantKeywords());
    readonly storeLocatorRestaurantsPageState = computed(() => this._storeLocatorContext.storeLocatorRestaurantsPageState());
    readonly isLoading = computed(() => this._storeLocatorContext.isLoading());

    readonly organizationJobState = computed<StoreLocatorJobState | undefined | null>(() => {
        const organizationId = this._storeLocatorContext.storeLocatorOrganizationConfiguration()?.organizationId;
        if (!organizationId) {
            return null;
        }
        return this._storeLocatorJobContext.storeLocatorJobState()[organizationId];
    });

    readonly shouldShowPageLoader = computed(() => {
        const jobState = this.organizationJobState();
        return jobState?.isJobRunning && jobState.jobType === StoreLocatorJobType.CONTENT_GENERATION;
    });

    readonly jobLoaderText = computed(() => this._storeLocatorJobContext.getJobLoaderText(this.organizationJobState()?.jobType));

    startPagesGeneration(): void {
        const organization = this._storeLocatorContext.storeLocatorOrganizationConfiguration()?.organization;
        if (!organization) {
            return;
        }
        this._storeLocatorJobContext.executeJobForOrganization(organization, StoreLocatorJobType.CONTENT_GENERATION);
    }
}
