import { signal, WritableSignal } from '@angular/core';

import { UpdateOrganizationConfigurationStorePagesBodyDto } from '@malou-io/package-dto';
import { StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/models/store-locator-organization-config';

export class StoreLocatorOrganizationStylesConfiguration {
    colors: StoreLocatorOrganizationConfiguration['styles']['colors'];
    fonts: StoreLocatorOrganizationConfiguration['styles']['fonts'];
    storePages: StoreLocatorOrganizationConfiguration['styles']['pages']['store'];

    readonly isDirty: WritableSignal<boolean> = signal(false);

    readonly updateStorePages: WritableSignal<Partial<Record<StoreLocatorRestaurantPageElementIds, string[]>>> = signal({});

    constructor(init: StoreLocatorOrganizationConfiguration['styles']) {
        this.colors = init.colors;
        this.fonts = init.fonts;
        this.storePages = { ...init.pages.store, ...(init.pages.storeDraft ?? {}) };
    }

    getStorePageElementStyle(elementId: StoreLocatorRestaurantPageElementIds): string[] {
        const updatedStyleClasses = this.updateStorePages()[elementId];
        if (updatedStyleClasses) {
            return updatedStyleClasses;
        }
        return this.storePages[elementId];
    }

    updateStyle(data: Partial<Record<StoreLocatorRestaurantPageElementIds, string[]>>): void {
        const currentStorePages = this.updateStorePages();
        this.updateStorePages.set({
            ...currentStorePages,
            ...data,
        });
        if (!this.isDirty()) {
            this.isDirty.set(true);
        }
    }

    toDto(): UpdateOrganizationConfigurationStorePagesBodyDto | null {
        if (Object.keys(this.updateStorePages()).length === 0) {
            return null;
        }
        return {
            data: this.updateStorePages(),
        };
    }
}
