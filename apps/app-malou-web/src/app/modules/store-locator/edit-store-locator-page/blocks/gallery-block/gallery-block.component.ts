import { Ng<PERSON><PERSON>, Ng<PERSON>tyle } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed } from '@angular/core';
import { LazyLoadImageModule } from 'ng-lazyload-image';

import { StoreLocatorPageBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { ImageAssets, ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-store-locator-edit-page-gallery-block',
    templateUrl: './gallery-block.component.html',
    styleUrls: ['./gallery-block.component.scss'],
    imports: [NgStyle, ImagePathResolverPipe, LazyLoadImageModule, NgClass],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageGalleryBlockComponent extends StoreLocatorPageBlockComponent {
    readonly galleryBlockStaticData = computed(() => this.storeLocatorPageState()?.getBlockStaticData(StoreLocatorPageBlockType.GALLERY));

    readonly galleryBlockUpdatedData = computed(
        () => this.storeLocatorPageState()?.getBlockUpdatedData$(StoreLocatorPageBlockType.GALLERY)?.()?.data
    );

    readonly ImageAssets = ImageAssets;

    readonly title = computed(() => this.galleryBlockUpdatedData()?.title ?? this.galleryBlockStaticData()?.title);
    readonly subtitle = computed(() => this.galleryBlockUpdatedData()?.subtitle ?? this.galleryBlockStaticData()?.subtitle);
    readonly images = computed(() => this.galleryBlockUpdatedData()?.images ?? this.galleryBlockStaticData()?.images ?? []);

    readonly isBlockSelected = computed(() => this.editStoreLocatorPageContext.currentEditingBlock() === StoreLocatorPageBlockType.GALLERY);

    constructor() {
        super(StoreLocatorPageBlockType.GALLERY);
    }
}
