import { ChangeDetectionStrategy, Component } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'app-store-locator-edit-page-header-block',
    templateUrl: './header-block.component.html',
    styleUrls: ['./header-block.component.scss'],
    imports: [TranslateModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageHeaderBlockComponent {
    constructor() {}
}
