import { ChangeDetectionStrategy, Component } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-store-locator-edit-page-footer-block',
    templateUrl: './footer-block.component.html',
    styleUrls: ['./footer-block.component.scss'],
    imports: [ImagePathResolverPipe, TranslateModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageFooterBlockComponent {
    constructor() {}
}
