import { Ng<PERSON><PERSON>, Ng<PERSON>tyle } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed } from '@angular/core';

import { StoreLocatorPageBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';

@Component({
    selector: 'app-store-locator-edit-page-cta-block',
    templateUrl: './cta-block.component.html',
    styleUrls: ['./cta-block.component.scss'],
    imports: [NgStyle, NgClass],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageCtaBlockComponent extends StoreLocatorPageBlockComponent {
    readonly ctaBlockStaticData = computed(() =>
        this.storeLocatorPageState()?.getBlockStaticData(StoreLocatorPageBlockType.CALL_TO_ACTION)
    );

    readonly ctaBlockUpdateData = computed(
        () => this.storeLocatorPageState()?.getBlockUpdatedData$(StoreLocatorPageBlockType.CALL_TO_ACTION)?.()?.data
    );

    readonly title = computed(() => this.ctaBlockUpdateData()?.title ?? this.ctaBlockStaticData()?.title);
    readonly links = computed(() => this.ctaBlockUpdateData()?.links ?? this.ctaBlockStaticData()?.links ?? []);

    readonly isBlockSelected = computed(
        () => this.editStoreLocatorPageContext.currentEditingBlock() === StoreLocatorPageBlockType.CALL_TO_ACTION
    );

    constructor() {
        super(StoreLocatorPageBlockType.CALL_TO_ACTION);
    }
}
