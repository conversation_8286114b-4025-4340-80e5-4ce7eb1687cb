import { FormArray, FormControl, FormGroup } from '@angular/forms';

export interface CtaBlockContentFormData {
    title: string;
    ctaButtons: {
        text: string;
        url: string;
    }[];
}

export type CtaButtonsFormGroup = FormGroup<{
    text: FormControl<string>;
    url: FormControl<string>;
}>;

export interface CtaBlockContentForm {
    title: FormControl<string>;
    ctaButtons: FormArray<CtaButtonsFormGroup>;
}

export interface CtaBlockStyleData {
    general: {
        backgroundColor: string;
        titleColor: string;
    };
    buttons: {
        radius: number;
        textColor: string;
        backgroundColor: string;
        borderColor: string;
    };
}

export interface CtaBlockStyleForm {
    general: FormGroup<{
        backgroundColor: FormControl<string>;
        titleColor: FormControl<string>;
    }>;
    buttons: FormGroup<{
        radius: FormControl<number>;
        textColor: FormControl<string>;
        backgroundColor: FormControl<string>;
        borderColor: FormControl<string>;
    }>;
}

export enum CtaBlockContentFormInputValidation {
    TITLE_MAX_LENGTH = 60,
    CTA_TEXT_MAX_LENGTH = 20,
}
