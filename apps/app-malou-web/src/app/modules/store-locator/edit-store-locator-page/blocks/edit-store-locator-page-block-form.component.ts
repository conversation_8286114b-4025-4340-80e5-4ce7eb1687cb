import { NgClass, NgTemplateOutlet } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    inject,
    input,
    signal,
    TemplateRef,
    WritableSignal,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';

import { GenerateStoreLocatorContentType, MimeType, StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { parseConfigurationStyleClassesToCssStyle } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page.utils';
import { EditStoreLocatorPageOrganizationRestaurantsSelectorComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/organization-restaurants-selector/organization-restaurants-selector.component';
import { EditStoreLocatorPageContext } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.context';
import { EXTRA_COLORS } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { StoreLocatorOrganizationRestaurant } from ':modules/store-locator/models/store-locator-organization-restaurant';
import { DefaultErrorMessage } from ':shared/components/input-text/input-text.component';
import { InputTextTheme } from ':shared/components/input-text/input-text.interface';
import { InputTextAreaTheme } from ':shared/components/text-area/text-area.interface';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

enum BlockFromTabs {
    CONTENT,
    STYLE,
}
@Component({
    selector: 'app-edit-store-locator-page-block-form-wrapper',
    templateUrl: './edit-store-locator-page-block-form.component.html',
    styleUrls: ['./edit-store-locator-page-block-form.component.scss'],
    imports: [
        MatButtonModule,
        MatTabsModule,
        TranslateModule,
        NgTemplateOutlet,
        EditStoreLocatorPageOrganizationRestaurantsSelectorComponent,
        MatIconModule,
        NgClass,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageBlockFormComponent {
    readonly contentFormTemplate = input<TemplateRef<any>>();
    readonly styleFormTemplate = input<TemplateRef<any>>();

    readonly destroyRef = inject(DestroyRef);
    readonly editStoreLocatorPageContext = inject(EditStoreLocatorPageContext);

    readonly organizationRestaurants = computed(() => this.editStoreLocatorPageContext.organizationRestaurants());
    readonly currentEditingRestaurant = computed(() => this.editStoreLocatorPageContext.currentEditingRestaurant());
    readonly organizationStyleConfiguration = computed(() => this.editStoreLocatorPageContext.organizationStyleConfiguration());
    readonly colorOptions = computed(() => [
        ...(this.editStoreLocatorPageContext.organizationStyleConfiguration()?.colors.map((color) => color.value) ?? []),
        ...EXTRA_COLORS,
    ]);

    readonly selectedTabIndex: WritableSignal<number> = signal(BlockFromTabs.CONTENT);
    readonly selectedRestaurant = this.editStoreLocatorPageContext.currentEditingRestaurant;

    readonly InputTextTheme = InputTextTheme;
    readonly InputTextAreaTheme = InputTextAreaTheme;
    readonly DefaultErrorMessage = DefaultErrorMessage;
    readonly GenerateStoreLocatorContentType = GenerateStoreLocatorContentType;
    readonly SvgIcon = SvgIcon;
    readonly MimeType = MimeType;

    restaurantsFilterControl: FormControl<StoreLocatorOrganizationRestaurant> = new FormControl<StoreLocatorOrganizationRestaurant | null>(
        this.editStoreLocatorPageContext.currentEditingRestaurant()
    ) as FormControl<StoreLocatorOrganizationRestaurant>;

    readonly storeLocatorPageState = computed(() => this.editStoreLocatorPageContext.selectedRestaurantStorePageState());

    readonly isBlockInError = computed(() => this.editStoreLocatorPageContext.isBlockInError().isError);
    readonly shouldDisableModal = computed(() => this.editStoreLocatorPageContext.shouldDisableModal());

    constructor() {
        this.restaurantsFilterControl.valueChanges.subscribe((restaurant) => {
            if (restaurant) {
                this.editStoreLocatorPageContext.updateCurrentEditingRestaurant(restaurant);
            }
        });
    }

    handleTabChange(event: number): void {
        this.selectedTabIndex.set(event);
    }

    handleDuplicateBlockUpdatesToAll(): void {
        const isBlockInError = this.editStoreLocatorPageContext.isBlockInError();
        if (!isBlockInError.isError) {
            this.editStoreLocatorPageContext.duplicateBlockDataToAll();
        }
    }

    protected getStyleMap(elementIds: StoreLocatorRestaurantPageElementIds[]): Record<string, Record<string, string>> {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();
        if (!organizationStyleConfiguration) {
            return {};
        }

        const styleMap: Record<string, Record<string, string>> = {};

        elementIds.forEach((key) => {
            styleMap[key] = parseConfigurationStyleClassesToCssStyle(
                organizationStyleConfiguration.getStorePageElementStyle(key) as string[],
                {
                    colors: organizationStyleConfiguration.colors,
                    fonts: organizationStyleConfiguration.fonts,
                }
            );
        });

        return styleMap;
    }
}
