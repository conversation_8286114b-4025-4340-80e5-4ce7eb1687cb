import { ChangeDetectionStrategy, Component, effect, input, signal, WritableSignal } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatSliderModule } from '@angular/material/slider';
import { LazyLoadImageModule } from 'ng-lazyload-image';

enum DefaultRadiusValues {
    MIN = 0,
    MAX = 100,
}

@Component({
    selector: 'app-store-locator-edit-page-radius-slider',
    templateUrl: './radius-slider.component.html',
    styleUrls: ['./radius-slider.component.scss'],
    imports: [LazyLoadImageModule, MatIconModule, MatSliderModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditStoreLocatorPageRadiusSliderComponent {
    readonly title = input.required<string>();
    readonly control = input.required<any>();
    readonly disabled = input<boolean>(false);

    readonly min = input<number>(DefaultRadiusValues.MIN);
    readonly max = input<number>(DefaultRadiusValues.MAX);

    readonly sliderValue: WritableSignal<number> = signal(DefaultRadiusValues.MIN);

    constructor() {
        effect(() => {
            this.sliderValue.set(this.control().value);
        });
    }

    onSliderInput(event: Event): void {
        const raw = +(event.target as HTMLInputElement).value;
        this.sliderValue.set(raw);
        this.control().setValue(raw);
    }
}
