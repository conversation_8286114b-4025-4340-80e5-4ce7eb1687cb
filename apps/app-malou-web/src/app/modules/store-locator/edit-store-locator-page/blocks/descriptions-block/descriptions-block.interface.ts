import { FormArray, FormControl, FormGroup } from '@angular/forms';

export interface DescriptionBlockSection {
    title: string;
    imageUrl: string;
    content: {
        subtitle: string;
        text: string;
    }[];
}

export interface DescriptionsBlockSectionForm {
    item: FormGroup<{
        title: FormControl<string>;
        imageUrl: FormControl<string>;
        content: FormArray<
            FormGroup<{
                subtitle: FormControl<string>;
                text: FormControl<string>;
            }>
        >;
    }>;
}

export interface DescriptionsBlockStyleData {
    generalEven: {
        backgroundColor: string;
        textColor: string;
    };
    generalUneven: {
        backgroundColor: string;
        textColor: string;
    };
}

export interface DescriptionsBlockStyleForm {
    generalEven: FormGroup<{
        backgroundColor: FormControl<string>;
        textColor: FormControl<string>;
    }>;
    generalUneven: FormGroup<{
        backgroundColor: FormControl<string>;
        textColor: FormControl<string>;
    }>;
}

export enum DescriptionsBlockContentFormInputValidation {
    TITLE_MAX_LENGTH = 60,
    SUBTITLE_MAX_LENGTH = 100,
    TEXT_MAX_LENGTH = 500,
}

export enum DescriptionsBlockControlType {
    TITLE = 'title',
    IMAGE = 'image',
}
