<app-edit-store-locator-page-block-form-wrapper [contentFormTemplate]="contentFormTemplate" [styleFormTemplate]="styleFormTemplate" />

<ng-template #contentFormTemplate>
    <div class="flex flex-col gap-5" [formGroup]="contentForm">
        <div class="px-5">
            <app-store-locator-edit-page-ai-suggestion
                [title]="'store_locator.edit_modal.controls.title.name' | translate"
                [generateStoreLocatorContentType]="GenerateStoreLocatorContentType.H1_TITLE_GENERATION"
                [isDisabled]="shouldDisableModal()"
                [control]="titleControl">
                <app-input-text
                    class="malou-text-14--bold!"
                    formControlName="title"
                    [theme]="InputTextTheme.EDIT_STORE_LOCATOR"
                    [defaultValue]="title()"
                    [disabled]="false"
                    [errorMessage]="
                        titleControl?.errors && titleControl?.errors?.maxlength
                            ? ('store_locator.edit_modal.controls.title.length_error'
                              | translate
                                  : {
                                        maxLength: InformationBlockContentFormInputValidation.TITLE_MAX_LENGTH,
                                    })
                            : ''
                    "
                    [showMaxLength]="true"
                    [maxLength]="InformationBlockContentFormInputValidation.TITLE_MAX_LENGTH"
                    [isEmojiPickerEnabled]="false"
                    [placeholder]="'store_locator.edit_modal.controls.title.placeholder' | translate"
                    [autocapitalize]="'none'">
                </app-input-text
            ></app-store-locator-edit-page-ai-suggestion>
        </div>

        <div class="border border-malou-color-background-dark"></div>

        <div class="w-fit px-5">
            @if (selectedRestaurant()) {
                <app-image-uploader
                    titleClass="!malou-text-14--bold !text-malou-color-text-1"
                    [(media)]="uploadedMedia"
                    [acceptedMimeTypes]="[MimeType.IMAGE_PNG, MimeType.IMAGE_JPEG]"
                    [restaurant]="selectedRestaurant()!"
                    [title]="'store_locator.edit_modal.controls.image_upload.name' | translate"
                    [wrapperClass]="'!p-0'"
                    (onMediaSelected)="onMediaSelected($event)"></app-image-uploader>
            }
        </div>

        <div class="border border-malou-color-background-dark"></div>

        <div class="expansion-header malou-expansion-panel px-5">
            <mat-accordion>
                <mat-expansion-panel class="!border-none" hideToggle [expanded]="false">
                    <mat-expansion-panel-header class="!pl-0" (click)="isButtonsPanelExpanded.set(!isButtonsPanelExpanded())">
                        <div class="flex w-full items-center justify-between">
                            <div class="malou-text-13--bold text-malou-color-text-1">
                                {{ 'store_locator.edit_modal.controls.cta.header' | translate }}
                            </div>
                            <div class="flex items-center">
                                <mat-icon
                                    class="!w-3 transition-all"
                                    color="primary"
                                    [svgIcon]="SvgIcon.CHEVRON_DOWN"
                                    [class.rotate-180]="isButtonsPanelExpanded()"></mat-icon>
                            </div>
                        </div>
                    </mat-expansion-panel-header>

                    <ng-template matExpansionPanelContent>
                        <div class="flex flex-col gap-4">
                            <div class="flex flex-col gap-2 !bg-malou-color-background-light">
                                @if (primaryCtaTextControl && primaryCtaUrlControl) {
                                    <app-store-locator-edit-page-call-to-action
                                        [urlOptions]="callToActionsSuggestions()"
                                        [canBeDisabled]="true"
                                        [title]="'store_locator.edit_modal.controls.cta.primary_button' | translate"
                                        [ctaTextControl]="primaryCtaTextControl"
                                        [ctaUrlControl]="primaryCtaUrlControl"
                                        (onCtaGlobalToggle)="onPrimaryCtaToggle($event)"></app-store-locator-edit-page-call-to-action>
                                }
                                @if (secondaryCtaTextControl && secondaryCtaUrlControl) {
                                    <app-store-locator-edit-page-call-to-action
                                        [urlOptions]="callToActionsSuggestions()"
                                        [canBeDisabled]="true"
                                        [title]="'store_locator.edit_modal.controls.cta.secondary_button' | translate"
                                        [ctaTextControl]="secondaryCtaTextControl"
                                        [disabled]="shouldDisableSecondaryCta()"
                                        [ctaUrlControl]="secondaryCtaUrlControl"></app-store-locator-edit-page-call-to-action>
                                }
                            </div>
                        </div>
                    </ng-template>
                </mat-expansion-panel>
            </mat-accordion>
        </div>
    </div>
</ng-template>

<ng-template #styleFormTemplate>
    <form class="flex flex-col gap-5" [formGroup]="styleForm">
        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" formGroupName="general">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit_modal.style.general' | translate }}
            </div>
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.backgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.title' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.titleColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.text' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.textColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.icon' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.iconColor')" />
        </div>
        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" formGroupName="buttons">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit_modal.style.buttons.title' | translate }}
            </div>

            <app-store-locator-edit-page-radius-slider
                [title]="'store_locator.edit_modal.style.buttons.radius' | translate"
                [control]="styleForm.get('buttons.radius')"></app-store-locator-edit-page-radius-slider>

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.buttons.primary_background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.primaryBackgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.buttons.primary_border' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.primaryBorderColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.buttons.primary_text' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.primaryTextColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.buttons.secondary_background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.secondaryBackgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.buttons.secondary_border' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.secondaryBorderColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.buttons.secondary_text' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.secondaryTextColor')" />
        </div>
    </form>
</ng-template>
