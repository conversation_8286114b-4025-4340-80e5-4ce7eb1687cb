<app-edit-store-locator-page-block-form-wrapper [contentFormTemplate]="contentFormTemplate" [styleFormTemplate]="styleFormTemplate" />

<ng-template #contentFormTemplate>
    <div class="flex flex-col gap-5" [formGroup]="contentForm">
        <div class="flex flex-col gap-1 px-5">
            <app-store-locator-edit-page-ai-suggestion
                [title]="'store_locator.edit_modal.controls.title.name' | translate"
                [generateStoreLocatorContentType]="GenerateStoreLocatorContentType.REVIEWS_BLOCK_TITLE_GENERATION"
                [isDisabled]="shouldDisableModal()"
                [control]="titleControl">
                <app-input-text
                    class="!malou-text-14--bold"
                    formControlName="title"
                    [theme]="InputTextTheme.EDIT_STORE_LOCATOR"
                    [defaultValue]="title()"
                    [disabled]="false"
                    [errorMessage]="
                        titleControl?.errors && (titleControl?.errors?.minlength || titleControl?.errors?.maxlength)
                            ? ('store_locator.edit_modal.controls.title.length_error'
                              | translate
                                  : {
                                        maxLength: ReviewsBlockContentFormInputValidation.TITLE_MAX_LENGTH,
                                    })
                            : ''
                    "
                    [defaultErrorMessage]="DefaultErrorMessage.REQUIRED"
                    [isEmojiPickerEnabled]="false"
                    [showMaxLength]="true"
                    [maxLength]="ReviewsBlockContentFormInputValidation.TITLE_MAX_LENGTH"
                    [placeholder]="'store_locator.edit_modal.controls.title.placeholder' | translate"
                    [autocapitalize]="'none'">
                </app-input-text
            ></app-store-locator-edit-page-ai-suggestion>
        </div>
        <div class="border border-malou-color-background-dark"></div>

        <div class="expansion-header malou-expansion-panel px-5">
            <mat-accordion>
                <mat-expansion-panel class="!border-none" hideToggle [expanded]="false">
                    <mat-expansion-panel-header class="!pl-0" (click)="isButtonsPanelExpanded.set(!isButtonsPanelExpanded())">
                        <div class="flex w-full items-center justify-between">
                            <div class="malou-text-13--bold text-malou-color-text-1">
                                {{ 'store_locator.edit_modal.controls.cta.header' | translate }}
                            </div>
                            <div class="flex items-center">
                                <mat-icon
                                    class="!w-3 transition-all"
                                    color="primary"
                                    [svgIcon]="SvgIcon.CHEVRON_DOWN"
                                    [class.rotate-180]="isButtonsPanelExpanded()"></mat-icon>
                            </div>
                        </div>
                    </mat-expansion-panel-header>

                    <ng-template matExpansionPanelContent>
                        <div class="flex flex-col gap-4">
                            <div class="flex flex-col gap-2 !bg-malou-color-background-light" formGroupName="cta">
                                @if (ctaTextControl && ctaUrlControl) {
                                    <app-store-locator-edit-page-call-to-action
                                        [urlOptions]="callToActionsSuggestions()"
                                        [canBeDisabled]="true"
                                        [title]="'store_locator.edit_modal.controls.cta.primary_button' | translate"
                                        [ctaTextControl]="ctaTextControl"
                                        [ctaUrlControl]="ctaUrlControl"></app-store-locator-edit-page-call-to-action>
                                }
                            </div>
                        </div>
                    </ng-template>
                </mat-expansion-panel>
            </mat-accordion>
        </div>
    </div>
</ng-template>

<ng-template #styleFormTemplate>
    <form class="flex flex-col gap-5" [formGroup]="styleForm">
        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" formGroupName="general">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit_modal.style.general' | translate }}
            </div>
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.backgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.title' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.titleColor')" />
        </div>
        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" formGroupName="buttons">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit_modal.style.buttons.title' | translate }}
            </div>
            <app-store-locator-edit-page-radius-slider
                [title]="'store_locator.edit_modal.style.buttons.radius' | translate"
                [control]="styleForm.get('buttons.radius')"></app-store-locator-edit-page-radius-slider>

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.buttons.primary_background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.primaryBackgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.buttons.primary_border' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.primaryBorderColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.buttons.primary_text' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.primaryTextColor')" />
        </div>
    </form>
</ng-template>
