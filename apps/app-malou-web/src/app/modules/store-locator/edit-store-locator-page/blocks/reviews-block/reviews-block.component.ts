import { <PERSON><PERSON><PERSON>, Ng<PERSON>tyle } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';

import { GetStoreLocatorDraftStoreDto, GetStoreLocatorReviewDto } from '@malou-io/package-dto';
import { getDefaultReviewAvatar, PlatformKey } from '@malou-io/package-utils';

import { StoreLocatorPageBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { PlatformLogoPathResolverPipe } from ':shared/pipes/platform-logo-path-resolver.pipe';

@Component({
    selector: 'app-store-locator-edit-page-reviews-block',
    templateUrl: './reviews-block.component.html',
    styleUrls: ['./reviews-block.component.scss'],
    imports: [NgStyle, MatIconModule, PlatformLogoPathResolverPipe, NgClass],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageReviewsBlockComponent extends StoreLocatorPageBlockComponent {
    readonly PlatformKey = PlatformKey;
    readonly MAX_REVIEWS_COUNT = 8;
    readonly reviewsBlockStaticData = computed(() => this.storeLocatorPageState()?.getBlockStaticData(StoreLocatorPageBlockType.REVIEWS));
    readonly reviewsBlockUpdatedData = computed(
        () => this.storeLocatorPageState()?.getBlockUpdatedData$(StoreLocatorPageBlockType.REVIEWS)?.()?.data
    );
    readonly reviews = computed(() => this.reviewsBlockStaticData()?.reviews ?? this.getPlaceholderReviews());
    readonly title = computed(() => this.reviewsBlockUpdatedData()?.title ?? this.reviewsBlockStaticData()?.title);
    readonly cta = computed(() => {
        const reviewsBlockUpdatedData = this.reviewsBlockUpdatedData();
        if (reviewsBlockUpdatedData) {
            return reviewsBlockUpdatedData.cta;
        }
        return this.reviewsBlockStaticData()?.cta;
    });

    readonly isBlockSelected = computed(() => this.editStoreLocatorPageContext.currentEditingBlock() === StoreLocatorPageBlockType.REVIEWS);

    constructor() {
        super(StoreLocatorPageBlockType.REVIEWS);
    }

    getProfilePictureUrl(review: GetStoreLocatorReviewDto): string | undefined {
        return (review.picture as { url: string }).url;
    }

    getProfileAvatar(review: GetStoreLocatorReviewDto):
        | {
              initials: string;
              color: string;
          }
        | undefined {
        return review.picture as { initials: string; color: string };
    }

    getPlaceholderReviews(): NonNullable<GetStoreLocatorDraftStoreDto['reviewsBlock']>['reviews'] {
        return Array.from({ length: this.MAX_REVIEWS_COUNT }, (_, index) => {
            const { initials, color } = getDefaultReviewAvatar();

            return {
                userName: this._translate.instant('store_locator.blocks.reviews.username_placeholder', { index: index + 1 }),
                content: this._translate.instant('store_locator.blocks.reviews.text_placeholder'),
                publishedAt: new Date().toLocaleDateString('fr-FR', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric',
                }),
                picture: {
                    initials,
                    color,
                },
                avatar: getDefaultReviewAvatar(),
                platformKey: PlatformKey.GMB,
                starsCount: 5,
            };
        });
    }
}
