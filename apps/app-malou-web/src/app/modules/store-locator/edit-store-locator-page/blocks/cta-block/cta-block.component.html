<div
    class="relative flex h-fit flex-col"
    [ngClass]="{
        'cursor-not-allowed': shouldShowCursorNotAllowed(),
        'cursor-pointer opacity-60 hover:opacity-100': !isBlockSelected(),
    }"
    [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_WRAPPER]"
    (click)="handleShowFormBlock(StoreLocatorPageBlockType.CALL_TO_ACTION)">
    <div class="flex max-w-[1600px] flex-col items-center justify-center px-2 py-14 sm:mx-auto sm:px-14">
        <h2
            class="pb-8 text-center text-3xl font-extrabold sm:text-4xl"
            [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_TITLE]">
            {{ title() }}
        </h2>
        @for (link of links(); track $index) {
            <a
                class="mb-4 block w-full max-w-[320px] rounded-lg border-[1px] border-solid p-4 text-center text-sm"
                target="_blank"
                [href]="link.url"
                [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_CTA]">
                {{ link.text.toUpperCase() }}
            </a>
        }
    </div>
    <div
        class="absolute left-0 top-0 h-full w-full bg-transparent hover:border-4 hover:border-malou-color-primary"
        [class.border-malou-color-primary]="isBlockSelected()"
        [class.border-4]="isBlockSelected()"></div>
</div>
