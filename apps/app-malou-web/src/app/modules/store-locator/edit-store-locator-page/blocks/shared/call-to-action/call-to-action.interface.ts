import { FormControl, FormGroup } from '@angular/forms';

export interface CallToActionContentForm {
    text: string;
    url: string;
}

export enum CallToActionOptionKey {
    ORDER_URL = 'orderUrl',
    RESERVATION_URL = 'reservationUrl',
    MENU_URL = 'menuUrl',
    WEBSITE = 'website',
    BACKUP = 'backup',
    CUSTOM = 'custom',
}

export interface EditStorePageCallToActionUrlOption {
    key: string;
    value: string;
}

export type CtaButtonsFormGroup = FormGroup<{
    text: FormControl<string>;
    url: FormControl<string>;
}>;

export enum CtaBlockContentFormInputValidation {
    CTA_TEXT_MAX_LENGTH = 20,
}
