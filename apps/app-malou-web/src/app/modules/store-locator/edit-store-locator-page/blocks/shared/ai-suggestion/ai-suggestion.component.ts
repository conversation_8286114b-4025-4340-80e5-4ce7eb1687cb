import { NgClass } from '@angular/common';
import {
    AfterContentInit,
    ChangeDetectionStrategy,
    Component,
    computed,
    ContentChild,
    DestroyRef,
    inject,
    input,
    signal,
    WritableSignal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AbstractControl } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { LazyLoadImageModule } from 'ng-lazyload-image';

import { GenerateStoreLocatorContentResponseDto } from '@malou-io/package-dto';
import { GenerateStoreLocatorContentType } from '@malou-io/package-utils';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { DescriptionBlockItemControlType } from ':modules/store-locator/edit-store-locator-page/blocks/shared/ai-suggestion/ai-suggestion.interface';
import { EditStoreLocatorPageContext } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.context';
import { StoreLocatorService } from ':modules/store-locator/store-locator.service';
import { ButtonComponent } from ':shared/components/button/button.component';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { TextAreaComponent } from ':shared/components/text-area/text-area.component';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-store-locator-edit-page-ai-suggestion',
    templateUrl: './ai-suggestion.component.html',
    styleUrls: ['./ai-suggestion.component.scss'],
    imports: [LazyLoadImageModule, MatIconModule, MatTooltipModule, MalouSpinnerComponent, NgClass, ButtonComponent, TranslateModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditStoreLocatorPageAiSuggestionComponent implements AfterContentInit {
    @ContentChild(InputTextComponent) inputTextComponent!: InputTextComponent;
    @ContentChild(TextAreaComponent) textAreaComponent!: TextAreaComponent;

    readonly title = input<string | null>(null);
    readonly control = input<AbstractControl | null>(null);
    readonly descriptionItemData = input<{ type: DescriptionBlockItemControlType; itemIndex: number; controlIndex: number } | null>(null);
    readonly generateStoreLocatorContentType = input.required<GenerateStoreLocatorContentType>();
    readonly isDisabled = input.required<boolean>();
    readonly forceHideButtons = input<boolean>(false);
    readonly forceHideMagicWand = input<boolean>(false);

    private readonly _editStoreLocatorPageContext = inject(EditStoreLocatorPageContext);
    private readonly _storeLocatorService = inject(StoreLocatorService);
    private readonly _destroyRef = inject(DestroyRef);

    readonly isLoadingGeneration: WritableSignal<boolean> = signal(false);
    readonly isLoadingOptimization: WritableSignal<boolean> = signal(false);

    readonly shouldHideMagicWand: WritableSignal<boolean> = signal(true);
    readonly isFocused: WritableSignal<boolean> = signal(false);

    readonly storePageState = computed(() => this._editStoreLocatorPageContext.selectedRestaurantStorePageState());

    readonly SvgIcon = SvgIcon;

    ngAfterContentInit(): void {
        this.control()
            ?.valueChanges.pipe(takeUntilDestroyed(this._destroyRef))
            .subscribe((valueChanges) => {
                if (valueChanges === null || valueChanges === undefined || valueChanges.trim() === '') {
                    this.shouldHideMagicWand.set(false);
                } else {
                    this.shouldHideMagicWand.set(true);
                }
            });
        if (this.inputTextComponent) {
            this.inputTextComponent.onFocus.subscribe((focusEvent) => this.isFocused.set(focusEvent.type === 'focus'));
        }
        if (this.textAreaComponent) {
            this.textAreaComponent.onFocus.subscribe((focusEvent) => this.isFocused.set(focusEvent.type === 'focus'));
        }
    }

    generateStoreLocatorContent(event?: Event): void {
        event?.stopPropagation();
        const updates = this.storePageState()?.toDto({
            shouldIgnoreCheck: true,
        });
        if (!updates) {
            return;
        }
        this.isLoadingGeneration.set(true);
        this._storeLocatorService
            .generateStoreLocatorContent({
                organizationId: this._editStoreLocatorPageContext.organizationData().id,
                body: {
                    type: this.generateStoreLocatorContentType(),
                    updates,
                },
            })
            .pipe(takeUntilDestroyed(this._destroyRef))
            .subscribe({
                next: (result) => {
                    if (result.text) {
                        this.control()?.setValue(result.text);
                    } else {
                        this._mapValueToControlType(result.blocks as GenerateStoreLocatorContentResponseDto['blocks']);
                    }
                    this.isLoadingGeneration.set(false);
                },
                error: () => {
                    this.isLoadingGeneration.set(false);
                },
            });
    }

    optimizeStoreLocatorContent(): void {
        const updates = this.storePageState()?.toDto({
            shouldIgnoreCheck: true,
        });
        if (!updates) {
            return;
        }
        this.isLoadingOptimization.set(true);
        this._storeLocatorService
            .generateStoreLocatorContent({
                organizationId: this._editStoreLocatorPageContext.organizationData().id,
                body: {
                    type: this.generateStoreLocatorContentType(),
                    updates,
                    currentContent: this.control()?.value || '',
                },
            })
            .pipe(takeUntilDestroyed(this._destroyRef))
            .subscribe({
                next: (result) => {
                    if (result.text) {
                        this.control()?.setValue(result.text);
                    } else {
                        this._mapValueToControlType(result.blocks as GenerateStoreLocatorContentResponseDto['blocks']);
                    }
                    this.isLoadingOptimization.set(false);
                },
                error: () => {
                    this.isLoadingOptimization.set(false);
                },
            });
    }

    private _mapValueToControlType(value: GenerateStoreLocatorContentResponseDto['blocks']): void {
        if (!value || !this.descriptionItemData()) {
            return;
        }
        const { type, itemIndex, controlIndex } = this.descriptionItemData()!;
        switch (type) {
            case DescriptionBlockItemControlType.TITLE:
                this.control()?.setValue(value[itemIndex].title);
                break;
            case DescriptionBlockItemControlType.SUBTITLE:
                this.control()?.setValue(value[itemIndex].sections[controlIndex].subtitle);
                break;
            case DescriptionBlockItemControlType.TEXT:
                this.control()?.setValue(value[itemIndex].sections[controlIndex].text);
                break;
            default:
                console.warn('Unknown control type:', type);
                break;
        }
    }
}
