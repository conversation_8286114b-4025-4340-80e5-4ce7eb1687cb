<div
    class="relative flex h-fit justify-center sm:flex-row"
    [ngClass]="{
        'cursor-not-allowed': shouldShowCursorNotAllowed(),
        'cursor-pointer opacity-60 hover:opacity-100': !isBlockSelected(),
    }"
    [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.GALLERY_WRAPPER]"
    (click)="handleShowFormBlock(StoreLocatorPageBlockType.GALLERY)">
    <div class="flex max-w-[1600px] flex-col items-center justify-center py-12">
        <h2
            class="px-1 pb-4 text-center text-2xl font-extrabold sm:px-12 sm:text-4xl"
            [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.GALLERY_TITLE]">
            {{ title() }}
        </h2>

        <div class="px-1 pb-8 text-center text-lg sm:px-12 sm:text-xl">{{ subtitle() }}</div>

        <div class="flex w-full flex-row gap-2 px-8">
            @if (images()[0]) {
                <div class="h-[508px] w-3/5 overflow-hidden" [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.GALLERY_PICTURE]">
                    <img
                        class="h-full w-full object-cover object-center"
                        [alt]="images()[0].description"
                        [defaultImage]="ImageAssets.DEFAULT_POST | imagePathResolver"
                        [lazyLoad]="images()[0].url || ''" />
                </div>
            }

            <div class="grid w-full grid-cols-3 flex-row flex-wrap gap-2 lg:h-[508px] lg:w-2/3 lg:grid-cols-3">
                @for (image of images().slice(1); track $index) {
                    <div class="h-[250px] overflow-hidden" [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.GALLERY_PICTURE]">
                        <img
                            class="h-full w-full object-cover"
                            [alt]="image.description"
                            [defaultImage]="ImageAssets.DEFAULT_POST | imagePathResolver"
                            [lazyLoad]="image.url || ''" />
                    </div>
                }
            </div>
        </div>
    </div>
    <div
        class="absolute left-0 top-0 h-full w-full bg-transparent hover:border-4 hover:border-malou-color-primary"
        [class.border-malou-color-primary]="isBlockSelected()"
        [class.border-4]="isBlockSelected()"></div>
</div>
