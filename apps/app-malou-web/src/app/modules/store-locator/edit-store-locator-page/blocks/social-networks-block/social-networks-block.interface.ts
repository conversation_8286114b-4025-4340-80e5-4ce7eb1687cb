import { FormControl, FormGroup } from '@angular/forms';

export interface SocialNetworksBlockContentFormData {
    title: string;
}

export interface SocialNetworksBlockContentForm {
    title: FormControl<string>;
}

export interface SocialNetworksBlockStyleData {
    general: {
        backgroundColor: string;
        titleColor: string;
    };
}

export interface SocialNetworksBlockStyleForm {
    general: FormGroup<{
        backgroundColor: FormControl<string>;
        titleColor: FormControl<string>;
    }>;
}

export enum SocialNetworksBlockContentFormInputValidation {
    TITLE_MAX_LENGTH = 60,
}
