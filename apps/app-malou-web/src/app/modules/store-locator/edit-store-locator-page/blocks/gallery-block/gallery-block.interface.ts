import { FormControl, FormGroup } from '@angular/forms';

export interface GalleryBlockContentForm {
    title: FormControl<string>;
    subtitle: FormControl<string>;
}

export interface GalleryBlockContent {
    title: string;
    subtitle: string;
}

export interface GalleryBlockStyleData {
    general: {
        backgroundColor: string;
        titleColor: string;
        textColor: string;
    };
}

export interface GalleryBlockStyleForm {
    general: FormGroup<{
        backgroundColor: FormControl<string>;
        titleColor: FormControl<string>;
        textColor: FormControl<string>;
    }>;
}

export enum GalleryBlockContentFormInputValidation {
    TITLE_MAX_LENGTH = 60,
    SUBTITLE_MAX_LENGTH = 80,
}
