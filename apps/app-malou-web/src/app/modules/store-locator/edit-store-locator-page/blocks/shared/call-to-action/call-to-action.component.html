<div class="flex flex-col gap-2">
    <div class="flex items-center">
        @if (canBeDisabled()) {
            <app-slide-toggle class="mr-3" [checked]="isCtaEnabled()" [disabled]="disabled()" (onToggle)="onCtaToggle()">
            </app-slide-toggle>
        }
        <div class="font-regular text-[12px] text-malou-color-text-1">
            {{ title() }}
        </div>
    </div>

    <div class="flex flex-col gap-2">
        <app-input-text
            class="!malou-text-14"
            [formControl]="ctaTextControl()"
            [placeholder]="'store_locator.edit_modal.controls.cta.title' | translate"
            [errorMessage]="
                ctaTextControl().errors && ctaTextControl().errors?.maxlength
                    ? ('store_locator.edit_modal.controls.cta.length_error.title'
                      | translate
                          : {
                                maxLength: CtaBlockContentFormInputValidation.CTA_TEXT_MAX_LENGTH,
                            })
                    : ''
            "
            [defaultErrorMessage]="DefaultErrorMessage.REQUIRED"
            [showMaxLength]="true"
            [maxLength]="CtaBlockContentFormInputValidation.CTA_TEXT_MAX_LENGTH"
            [autocapitalize]="'none'">
        </app-input-text>
        <app-select
            [values]="urlOptionsToShow()"
            [formControl]="selectorControl"
            [displayWith]="displayUrlOptionWith"
            (selectChange)="onSelectChange($event)">
            <ng-template let-value="value" #simpleSelectedValueTemplate>
                <div class="ml-3 flex items-center gap-x-2 py-1">
                    <div class="malou-text-12--semibold">
                        {{ displayUrlOptionWith | applyPure: value }}
                    </div>
                </div>
            </ng-template>
            <ng-template let-value="value" let-isValueSelected="isValueSelected" #optionTemplate>
                <div class="py-1">
                    <div class="flex items-center gap-x-2">
                        <div class="malou-text-12--semibold" [class.malou-color-text-1]="isValueSelected">
                            {{ displayUrlOptionWith | applyPure: value }}
                        </div>
                    </div>
                </div>
            </ng-template>
        </app-select>
        @if (isCustomUrlOptionSelected()) {
            <app-input-text
                class="malou-text-14--bold!"
                [formControl]="ctaUrlControl()"
                [defaultErrorMessage]="DefaultErrorMessage.REQUIRED"
                [placeholder]="'store_locator.edit_modal.controls.cta.placeholder' | translate"
                [autocapitalize]="'none'">
            </app-input-text>
        }
    </div>
</div>
