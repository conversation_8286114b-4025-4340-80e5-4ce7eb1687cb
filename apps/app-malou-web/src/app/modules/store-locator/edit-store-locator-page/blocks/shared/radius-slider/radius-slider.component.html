<div class="flex items-center justify-between">
    <div class="malou-text-11--bold text-malou-color-text-1">
        {{ title() }}
    </div>
    <div class="w-[180px] border border-malou-color-background-dark py-3 pl-3">
        <mat-slider
            class="malou-mat-slider--no-thumb w-full"
            color="primary"
            showTickMarks
            discrete
            showTickMarks
            [min]="min()"
            [max]="max()"
            [step]="1"
            [disabled]="disabled()"
            #ngSlider>
            <input matSliderThumb [value]="sliderValue()" (input)="onSliderInput($event)" #ngSliderThumb="matSliderThumb" />
        </mat-slider>
        <span class="malou-text-14 font-medium text-malou-color-text-1">
            {{ sliderValue() }}
        </span>
    </div>
</div>
