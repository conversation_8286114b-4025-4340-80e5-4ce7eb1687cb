import { Ng<PERSON><PERSON>, Ng<PERSON>tyle } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { LazyLoadImageModule } from 'ng-lazyload-image';

import { StoreLocatorPageBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { Illustration } from ':shared/pipes/illustration-path-resolver.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-store-locator-edit-page-descriptions-block',
    templateUrl: './descriptions-block.component.html',
    styleUrls: ['./descriptions-block.component.scss'],
    imports: [NgStyle, LazyLoadImageModule, ImagePathResolverPipe, NgClass],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageDescriptionBlockComponent extends StoreLocatorPageBlockComponent {
    // To specify which item to select of the descriptions block in the store locator.
    readonly descriptionItemIndex = input.required<number>();

    readonly descriptionsBlockStaticData = computed(() =>
        this.storeLocatorPageState()?.getBlockStaticData(StoreLocatorPageBlockType.DESCRIPTION)
    );

    readonly descriptionsBlockUpdatedData = computed(
        () => this.storeLocatorPageState()?.getBlockUpdatedData$(StoreLocatorPageBlockType.DESCRIPTION)?.()?.data
    );

    readonly Illustration = Illustration;

    readonly item = computed(() => {
        const updatedItem = this.descriptionsBlockUpdatedData()?.items[this.descriptionItemIndex()];
        if (updatedItem) {
            return {
                title: updatedItem.title,
                imageUrl: updatedItem.image.url,
                imageDescription: updatedItem.image.description,
                blocks: updatedItem.blocks.map((block) => ({
                    title: block.title,
                    text: block.text,
                })),
            };
        }
        return this.descriptionsBlockStaticData()?.items[this.descriptionItemIndex()] ?? null;
    });

    readonly isEven = computed(() => this.descriptionItemIndex() % 2 === 0);

    readonly isBlockSelected = computed(
        () =>
            this.editStoreLocatorPageContext.currentEditingBlock() === StoreLocatorPageBlockType.DESCRIPTION &&
            this.editStoreLocatorPageContext.descriptionsBlockSelectedItemIndex() === this.descriptionItemIndex()
    );

    constructor() {
        super(StoreLocatorPageBlockType.DESCRIPTION);
    }
}
