import { FormControl, FormGroup } from '@angular/forms';

export type InformationBlockCtaFromGroup = FormGroup<{
    text: FormControl<string>;
    url: FormControl<string>;
}>;

export interface InformationBlockContentForm {
    title: FormControl<string>;
    primaryCta: InformationBlockCtaFromGroup;
    secondaryCta: InformationBlockCtaFromGroup;
}

export interface InformationBlockContent {
    title: string;
    primaryCta?: {
        text: string;
        url: string;
    };
    secondaryCta?: {
        text: string;
        url: string;
    };
}

export interface InformationBlockStyleData {
    general: {
        backgroundColor: string;
        titleColor: string;
        textColor: string;
        iconColor: string;
    };
    buttons: {
        radius: number;
        primaryBackgroundColor: string;
        primaryBorderColor: string;
        primaryTextColor: string;
        secondaryBackgroundColor: string;
        secondaryBorderColor: string;
        secondaryTextColor: string;
    };
}
export interface InformationBlockStyleForm {
    general: FormGroup<{
        backgroundColor: FormControl<string>;
        titleColor: FormControl<string>;
        textColor: FormControl<string>;
        iconColor: FormControl<string>;
    }>;
    buttons: FormGroup<{
        radius: FormControl<number>;
        primaryBackgroundColor: FormControl<string>;
        primaryBorderColor: FormControl<string>;
        primaryTextColor: FormControl<string>;
        secondaryBackgroundColor: FormControl<string>;
        secondaryBorderColor: FormControl<string>;
        secondaryTextColor: FormControl<string>;
    }>;
}

export enum InformationBlockContentFormInputValidation {
    TITLE_MAX_LENGTH = 60,
    CTA_TEXT_MAX_LENGTH = 20,
}
