import { FormControl, FormGroup } from '@angular/forms';

export interface ReviewsBlockContentForm {
    title: FormControl<string>;
    cta: FormGroup<{
        text: FormControl<string>;
        url: FormControl<string>;
    }>;
}

export interface ReviewsBlockContent {
    title: string;
    cta?: {
        text: string;
        url: string;
    };
}

export interface ReviewsBlockStyleData {
    general: {
        backgroundColor: string;
        titleColor: string;
        textColor: string;
    };
    buttons: {
        radius: number;
        primaryBackgroundColor: string;
        primaryBorderColor: string;
        primaryTextColor: string;
    };
}
export interface ReviewsBlockStyleForm {
    general: FormGroup<{
        backgroundColor: FormControl<string>;
        titleColor: FormControl<string>;
        textColor: FormControl<string>;
    }>;
    buttons: FormGroup<{
        radius: FormControl<number>;
        primaryBackgroundColor: FormControl<string>;
        primaryBorderColor: FormControl<string>;
        primaryTextColor: FormControl<string>;
    }>;
}

export enum ReviewsBlockContentFormInputValidation {
    TITLE_MAX_LENGTH = 60,
    CTA_TEXT_MAX_LENGTH = 20,
}
