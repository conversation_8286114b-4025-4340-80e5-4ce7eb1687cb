<app-edit-store-locator-page-block-form-wrapper [contentFormTemplate]="contentFormTemplate" [styleFormTemplate]="styleFormTemplate" />

<ng-template #contentFormTemplate>
    <div class="flex flex-col gap-5" [formGroup]="contentForm">
        <div class="flex flex-col gap-1 px-5">
            <app-store-locator-edit-page-ai-suggestion
                [title]="'store_locator.edit_modal.controls.title.name' | translate"
                [generateStoreLocatorContentType]="GenerateStoreLocatorContentType.SOCIAL_MEDIA_BLOCK_TITLE_GENERATION"
                [isDisabled]="shouldDisableModal()"
                [control]="titleControl">
                <app-input-text
                    class="!malou-text-14"
                    formControlName="title"
                    [theme]="InputTextTheme.EDIT_STORE_LOCATOR"
                    [defaultValue]="title()"
                    [disabled]="false"
                    [errorMessage]="
                        titleControl?.errors && (titleControl?.errors?.minlength || titleControl?.errors?.maxlength)
                            ? ('store_locator.edit_modal.controls.title.length_error'
                              | translate
                                  : {
                                        maxLength: SocialNetworksBlockContentFormInputValidation.TITLE_MAX_LENGTH,
                                    })
                            : ''
                    "
                    [defaultErrorMessage]="DefaultErrorMessage.REQUIRED"
                    [isEmojiPickerEnabled]="false"
                    [showMaxLength]="true"
                    [maxLength]="SocialNetworksBlockContentFormInputValidation.TITLE_MAX_LENGTH"
                    [placeholder]="'store_locator.edit_modal.controls.title.placeholder_text' | translate"
                    [autocapitalize]="'none'">
                </app-input-text
            ></app-store-locator-edit-page-ai-suggestion>
        </div>
    </div>
</ng-template>

<ng-template #styleFormTemplate>
    <form class="flex flex-col gap-5" [formGroup]="styleForm">
        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" formGroupName="general">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit_modal.style.general' | translate }}
            </div>
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.backgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.title' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.titleColor')" />
        </div>
    </form>
</ng-template>
