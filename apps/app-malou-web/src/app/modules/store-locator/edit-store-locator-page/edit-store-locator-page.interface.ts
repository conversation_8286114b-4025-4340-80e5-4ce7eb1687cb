import { TimeInMilliseconds } from '@malou-io/package-utils';

import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/models/store-locator-organization-config';
import { StoreLocatorOrganizationRestaurant } from ':modules/store-locator/models/store-locator-organization-restaurant';

// Input
export interface EditStoreLocatorPageModalInputData {
    organizationConfiguration: StoreLocatorOrganizationConfiguration;
    organizationRestaurants: StoreLocatorOrganizationRestaurant[];
}

// WATCHER
export const DEFAULT_PUBLICATION_ESTIMATED_TIME = 5 * TimeInMilliseconds.MINUTE;

// GENERAL
export enum StoreLocatorPageBlockType {
    INFORMATION = 'information',
    GALLERY = 'gallery',
    REVIEWS = 'reviews',
    CALL_TO_ACTION = 'call_to_action',
    DESCRIPTION = 'description',
    SOCIAL_NETWORKS = 'social_networks',
}

export enum ExtraColor {
    WHITE = 'white',
}

export const ColorHexValue = {
    white: '#ffffff',
};

export const EXTRA_COLORS = [ColorHexValue.white];

// BLOCS

export interface CallToActionButton {
    label: string;
    url: string;
}
