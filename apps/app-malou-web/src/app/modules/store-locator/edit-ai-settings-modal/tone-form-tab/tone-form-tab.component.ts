import { KeyValuePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, input, OnInit, signal, WritableSignal } from '@angular/core';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatRadioModule } from '@angular/material/radio';
import { TranslateModule } from '@ngx-translate/core';

import { StoreLocatorAiSettingsDefaultTone, StoreLocatorAiSettingsLanguageStyle } from '@malou-io/package-utils';

import { SpecialAttributeForm } from ':modules/store-locator/edit-ai-settings-modal/edit-ai-settings-modal.interface';
import { SelectChipListComponent } from ':shared/components/select-chip-list/select-chip-list.component';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';

@Component({
    selector: 'app-tone-form-tab',
    imports: [
        FormsModule,
        ReactiveFormsModule,
        MatCheckboxModule,
        KeyValuePipe,
        SelectChipListComponent,
        MatRadioModule,
        TranslateModule,
        EnumTranslatePipe,
    ],
    templateUrl: './tone-form-tab.component.html',
    styleUrl: './tone-form-tab.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ToneFormTabComponent implements OnInit {
    readonly aiSettingsForm = input.required<
        FormGroup<{
            predefinedTones: FormControl<string[]>;
            customTones: FormControl<string[]>;
            languageStyle: FormControl<StoreLocatorAiSettingsLanguageStyle | null>;
            attributeIds: FormControl<string[]>;
            restaurantKeywordIds: FormControl<string[]>;
            specialAttributes: FormArray<FormGroup<SpecialAttributeForm>>;
        }>
    >();

    readonly StoreLocatorAiSettingsLanguageStyle = StoreLocatorAiSettingsLanguageStyle;

    readonly predefinedTones: WritableSignal<{ [tone: string]: boolean }> = signal({});

    ngOnInit(): void {
        this.predefinedTones.set(this._initPredefinedTones());
    }

    buildToneFromText = (text: string): string => text.trim();

    private _initPredefinedTones(): { [tone: string]: boolean } {
        const defaultTones = Object.values(StoreLocatorAiSettingsDefaultTone);
        const savedTones = this.aiSettingsForm().controls.predefinedTones.value || [];
        const allTones = defaultTones.map((tone) => ({
            tone,
            isChecked: savedTones.includes(tone),
        }));

        return Object.fromEntries(allTones.map(({ tone, isChecked }) => [tone, isChecked]));
    }

    onToneToggle(tone: string, isChecked: boolean): void {
        const currentTones = { ...this.predefinedTones() };
        currentTones[tone] = isChecked;
        this.predefinedTones.set(currentTones);

        const predefinedFormTones = this.aiSettingsForm().controls.predefinedTones;
        const currentFormValue = predefinedFormTones.value || [];

        if (isChecked && !currentFormValue.includes(tone)) {
            predefinedFormTones.setValue([...currentFormValue, tone]);
        } else if (!isChecked && currentFormValue.includes(tone)) {
            predefinedFormTones.setValue(currentFormValue.filter((t) => t !== tone));
        }
    }
}
