<div class="flex w-full flex-col">
    <form class="flex flex-col gap-6" [formGroup]="aiSettingsForm()">
        <div class="mt-2 flex flex-col gap-3">
            <div class="malou-text-14--bold text-malou-color-text-1">
                {{ 'store_locator.edit_ai_settings_modal.tone_form.communication_tone' | translate }}
            </div>
            <div class="grid grid-cols-3 gap-2">
                @for (item of predefinedTones() | keyvalue; track item) {
                    <div class="mr-12 flex w-fit">
                        <mat-checkbox color="primary" [checked]="item.value" (change)="onToneToggle(item.key, $event.checked)">
                            <span class="malou-text-12--medium text-malou-color-text-1">
                                <span>{{ item.key | enumTranslate: 'store_locator_ai_settings_default_tone' }}</span>
                            </span>
                        </mat-checkbox>
                    </div>
                }
            </div>

            <div class="flex">
                <app-select-chip-list
                    class="w-full"
                    formControlName="customTones"
                    [title]="'store_locator.edit_ai_settings_modal.tone_form.other' | translate"
                    [buildValueFromText]="buildToneFromText"
                    [placeholder]="'store_locator.edit_ai_settings_modal.tone_form.other_tone_placeholder' | translate"
                    [hideArrow]="true">
                </app-select-chip-list>
            </div>
        </div>

        <div class="flex flex-col gap-3">
            <div class="malou-text-14--bold text-malou-color-text-1">
                {{ 'store_locator.edit_ai_settings_modal.tone_form.formal_informal' | translate }}
            </div>
            <div class="flex">
                <mat-radio-group formControlName="languageStyle">
                    <mat-radio-button class="malou-radio-button--primary" [value]="StoreLocatorAiSettingsLanguageStyle.FORMAL">
                        {{ 'store_locator.edit_ai_settings_modal.tone_form.formal' | translate }}
                    </mat-radio-button>
                    <mat-radio-button class="malou-radio-button--primary" [value]="StoreLocatorAiSettingsLanguageStyle.INFORMAL">
                        {{ 'store_locator.edit_ai_settings_modal.tone_form.informal' | translate }}
                    </mat-radio-button>
                </mat-radio-group>
            </div>
        </div>
    </form>
</div>
