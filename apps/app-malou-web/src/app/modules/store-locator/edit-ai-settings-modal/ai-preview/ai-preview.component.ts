import { ChangeDetectionStrategy, Component, inject, input, output, signal } from '@angular/core';
import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { StoreLocatorAiSettingsLanguageStyle } from '@malou-io/package-utils';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { AiService } from ':core/services/ai.service';
import { ToastService } from ':core/services/toast.service';
import { SpecialAttributeForm } from ':modules/store-locator/edit-ai-settings-modal/edit-ai-settings-modal.interface';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-ai-preview',
    imports: [MatButtonModule, MatIconModule, TranslateModule, MalouSpinnerComponent],
    templateUrl: './ai-preview.component.html',
    styleUrl: './ai-preview.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AiPreviewComponent {
    readonly emitClose = output<void>();
    readonly aiSettingsForm = input.required<
        FormGroup<{
            predefinedTones: FormControl<string[]>;
            customTones: FormControl<string[]>;
            languageStyle: FormControl<StoreLocatorAiSettingsLanguageStyle | null>;
            attributeIds: FormControl<string[]>;
            restaurantKeywordIds: FormControl<string[]>;
            specialAttributes: FormArray<FormGroup<SpecialAttributeForm>>;
        }>
    >();
    readonly organizationId = input.required<string>();

    private readonly _aiService = inject(AiService);
    private readonly _toastService = inject(ToastService);
    private readonly _translate = inject(TranslateService);

    readonly SvgIcon = SvgIcon;

    readonly isDescriptionLoading = signal(false);
    readonly descriptionPreview = signal<string | null>(null);

    generatePreview(): void {
        const formValue = this.aiSettingsForm().getRawValue();
        const aiSettings = {
            tone: [...formValue.predefinedTones, ...formValue.customTones],
            languageStyle: formValue.languageStyle ?? StoreLocatorAiSettingsLanguageStyle.FORMAL,
            attributeIds: formValue.attributeIds,
            restaurantKeywordIds: formValue.restaurantKeywordIds,
            specialAttributes: formValue.specialAttributes.filter(
                (attr) => attr.text.trim().length > 0 && attr.restaurantId.trim().length > 0
            ),
        };
        this.isDescriptionLoading.set(true);

        this._aiService.generateStoreLocatorDescriptionPreview({ aiSettings, organizationId: this.organizationId() }).subscribe({
            next: (response) => {
                this.isDescriptionLoading.set(false);
                this.descriptionPreview.set(response);
            },
            error: () => {
                this.isDescriptionLoading.set(false);
                this._toastService.openErrorToast(this._translate.instant('common.unknown_error'));
            },
        });
    }

    close(): void {
        this.emitClose.emit();
    }
}
