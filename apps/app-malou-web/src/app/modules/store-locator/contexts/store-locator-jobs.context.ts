import { computed, inject, Injectable, signal, WritableSignal } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, filter, interval, Observable, switchMap, takeUntil } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';

import { GetStoreLocatorOrganizationJobResponseDto, WatchStoreLocatorJobResponseDto } from '@malou-io/package-dto';
import { StoreLocatorJobType, TimeInMilliseconds, WatcherStatus } from '@malou-io/package-utils';

import { ToastService } from ':core/services/toast.service';
import { DEFAULT_PUBLICATION_ESTIMATED_TIME } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/models/store-locator-organization-config';
import { StoreLocatorJobState } from ':modules/store-locator/models/store-locator.interface';
import { StoreLocatorJobPopinComponent } from ':modules/store-locator/store-locator-job-popin/store-locator-job-popin.component';
import { StoreLocatorService } from ':modules/store-locator/store-locator.service';
import { ToastDuration } from ':shared/components-v3/toast/toast-item/toast-item.component';
import { FooterPopinService } from ':shared/components/footer-popin/footer-popin.service';

@Injectable({
    providedIn: 'root',
})
export abstract class StoreLocatorJobContext {
    private readonly _storeLocatorService = inject(StoreLocatorService);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);
    private readonly _footerPopinService = inject(FooterPopinService);

    private readonly _DEFAULT_JOB_ESTIMATED_TIME = 90 * TimeInMilliseconds.SECOND;
    private readonly _DEFAULT_JOB_WATCH_INTERVAL = 5 * TimeInMilliseconds.SECOND;

    private readonly _storeLocatorJobWatchers = signal<Record<string, BehaviorSubject<boolean>>>({});
    private readonly _killStoreLocatorJobWatchers = computed(() => {
        const watchers: Record<string, Observable<boolean>> = {};
        Object.keys(this._storeLocatorJobWatchers()).forEach(
            (key) => (watchers[key] = this._storeLocatorJobWatchers()[key].pipe(filter((value) => value)))
        );
        return watchers;
    });
    readonly storeLocatorJobState = signal<{ [organizationId: string]: StoreLocatorJobState }>({});

    readonly jobEstimatedTime: WritableSignal<number> = signal(this._DEFAULT_JOB_ESTIMATED_TIME);
    readonly jobWatchInterval: WritableSignal<number> = signal(this._DEFAULT_JOB_WATCH_INTERVAL);

    constructor() {}

    executeJobForOrganization(
        organization: StoreLocatorOrganizationConfiguration['organization'],
        jobType: StoreLocatorJobType,
        options: {
            jobEstimatedTime?: number;
            jobWatchInterval?: number;
        } = {}
    ): void {
        this.jobEstimatedTime.set(options.jobEstimatedTime ?? this._DEFAULT_JOB_ESTIMATED_TIME);
        this.jobWatchInterval.set(options.jobWatchInterval ?? this._DEFAULT_JOB_WATCH_INTERVAL);
        this._executeStoreLocatorJobForOrganization(organization, jobType);
    }

    updateStoreLocatorJobState(
        organization: StoreLocatorOrganizationConfiguration['organization'],
        organizationJobs: GetStoreLocatorOrganizationJobResponseDto[]
    ): void {
        organizationJobs.forEach((job) => {
            const { jobId, jobType, jobStartDate } = job;
            if (jobType === StoreLocatorJobType.PUBLICATION) {
                this.jobEstimatedTime.set(DEFAULT_PUBLICATION_ESTIMATED_TIME);
            }
            this._initJobStateForOrganization({
                organization,
                jobType,
                ...(jobStartDate && { jobStartDate: new Date(jobStartDate) }),
            });
            const uuid = this._createJobWatcher();
            this._startWatcher({ organizationId: organization!.id, uuid, jobId, jobType });
            this._footerPopinService.open(StoreLocatorJobPopinComponent, {
                organizationsJobs: this.storeLocatorJobState(),
            });
        });
    }

    getJobLoaderText(jobType: StoreLocatorJobType | undefined): { title: string; footerText: string } {
        if (!jobType) {
            return { title: '', footerText: '' };
        }
        switch (jobType) {
            case StoreLocatorJobType.CONTENT_GENERATION:
                return {
                    title: this._translateService.instant('store_locator.generation.creation_in_progress'),
                    footerText: this._translateService.instant('store_locator.generation.can_leave_page'),
                };
            case StoreLocatorJobType.PUBLICATION:
                return {
                    title: this._translateService.instant('store_locator.publication.publication_in_progress'),
                    footerText: this._translateService.instant('store_locator.publication.can_leave_page'),
                };
            default:
                return {
                    title: '',
                    footerText: '',
                };
        }
    }

    private _executeStoreLocatorJobForOrganization(
        organization: StoreLocatorOrganizationConfiguration['organization'],
        jobType: StoreLocatorJobType
    ): void {
        this._startJob(organization.id, jobType).subscribe({
            next: (jobId) => {
                if (jobId) {
                    this._initJobStateForOrganization({ organization, jobType });
                    const uuid = this._createJobWatcher();
                    this._startWatcher({ organizationId: organization!.id, uuid, jobId, jobType });
                    this._footerPopinService.open(StoreLocatorJobPopinComponent, {
                        organizationsJobs: this.storeLocatorJobState(),
                    });
                }
            },
            error: () => {
                this._toastService.openErrorToast(this._getErrorMessageForJobType(jobType), ToastDuration.MEDIUM);
                this._completeJobStateForOrganization(organization!.id);
            },
        });
    }

    private _initJobStateForOrganization({
        organization,
        jobType,
        jobStartDate,
    }: {
        organization: StoreLocatorOrganizationConfiguration['organization'];
        jobType: StoreLocatorJobType;
        jobStartDate?: Date;
    }): void {
        this.storeLocatorJobState.set({
            ...this.storeLocatorJobState(),
            [organization.id]: {
                organizationId: organization.id,
                jobType,
                organizationName: organization!.name,
                isJobRunning: true,
                jobStartDate: jobStartDate ?? new Date(),
                jobEstimatedTime: this.jobEstimatedTime(),
                wasLastResultSeen: false,
            },
        });
    }

    private _completeJobStateForOrganization(organizationId: string): void {
        const newState = {
            ...this.storeLocatorJobState(),
            [organizationId]: {
                ...this.storeLocatorJobState()[organizationId],
                isJobRunning: false,
                jobStartDate: null,
                wasLastResultSeen: false,
            },
        };

        this.storeLocatorJobState.set(newState);
        this._footerPopinService.updateInputs({ organizationsJobs: newState });
    }

    private _startWatcher({
        organizationId,
        uuid,
        jobId,
        jobType,
    }: {
        organizationId: string;
        uuid: string;
        jobId: string;
        jobType: StoreLocatorJobType;
    }): void {
        interval(this.jobWatchInterval())
            .pipe(
                switchMap(() => this._watchStoreLocatorJob({ organizationId, jobType, jobId })),
                takeUntil(this._killStoreLocatorJobWatchers()[uuid])
            )
            .subscribe({
                next: (res) => {
                    switch (res.status) {
                        case WatcherStatus.FINISHED:
                            this._storeLocatorJobWatchers()[uuid].next(true);
                            this._completeJobStateForOrganization(organizationId);
                            return;

                        case WatcherStatus.FAILED:
                            this._storeLocatorJobWatchers()[uuid].next(true);
                            this._completeJobStateForOrganization(organizationId);
                            const errorMessage = res.error ?? this._getErrorMessageForJobType(jobType);
                            this._toastService.openErrorToast(errorMessage, ToastDuration.MEDIUM);
                            return;
                        case WatcherStatus.RUNNING:
                        default:
                            return;
                    }
                },
                error: (error) => {
                    if ([502, 504].includes(error.status)) {
                        return this._startWatcher({ organizationId, uuid, jobId, jobType });
                    }
                    this._toastService.openErrorToast(this._getErrorMessageForJobType(jobType), ToastDuration.MEDIUM);
                    this._completeJobStateForOrganization(organizationId);
                },
            });
    }

    private _createJobWatcher(): string {
        const uuid = uuidv4().toString();

        this._storeLocatorJobWatchers.update((watchers) => {
            for (const key in watchers) {
                if (watchers[key].value) {
                    delete watchers[key];
                }
            }

            watchers[uuid] = new BehaviorSubject<boolean>(false);
            return { ...watchers };
        });

        return uuid;
    }

    private _watchStoreLocatorJob({
        organizationId,
        jobType,
        jobId,
    }: {
        organizationId: string;
        jobType: StoreLocatorJobType;
        jobId: string;
    }): Observable<WatchStoreLocatorJobResponseDto> {
        switch (jobType) {
            case StoreLocatorJobType.CONTENT_GENERATION:
                return this._storeLocatorService.watchStoreLocatorPagesGeneration({
                    organizationId,
                    jobId,
                });
            case StoreLocatorJobType.PUBLICATION:
                return this._storeLocatorService.watchStoreLocatorPublication({
                    organizationId,
                    jobId,
                });
            default:
                throw new Error(`Unsupported job type: ${jobType}`);
        }
    }

    private _startJob(organizationId: string, jobType: StoreLocatorJobType): Observable<string> {
        switch (jobType) {
            case StoreLocatorJobType.CONTENT_GENERATION:
                return this._storeLocatorService.startStoreLocatorPagesGeneration({
                    organizationId,
                });
            case StoreLocatorJobType.PUBLICATION:
                return this._storeLocatorService.startStoreLocatorPublication({
                    organizationId,
                });
            default:
                throw new Error(`Unsupported job type: ${jobType}`);
        }
    }

    private _getErrorMessageForJobType(jobType: StoreLocatorJobType): string {
        switch (jobType) {
            case StoreLocatorJobType.CONTENT_GENERATION:
                return this._translateService.instant('store_locator.generation.creation_error');
            case StoreLocatorJobType.PUBLICATION:
                return this._translateService.instant('store_locator.publication.publication_error');
            default:
                return this._translateService.instant('store_locator.job.unknown_error');
        }
    }
}
