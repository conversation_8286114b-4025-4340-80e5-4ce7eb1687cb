import { DestroyRef, inject, Injectable } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { Store } from '@ngrx/store';
import { BehaviorSubject } from 'rxjs';

import { selectUserOrganizations } from ':modules/user/store/user.selectors';
import { Organization } from ':shared/models/organization';

@Injectable({
    providedIn: 'root',
})
export class StoreLocatorOrganizationsContext {
    private readonly _store = inject(Store);
    private readonly _destroyRef = inject(DestroyRef);

    readonly userOrganizations = toSignal(this._store.select(selectUserOrganizations).pipe(takeUntilDestroyed(this._destroyRef)), {
        initialValue: [],
    });
    readonly selectedOrganization$: BehaviorSubject<Organization | null> = new BehaviorSubject<Organization | null>(null);
}
