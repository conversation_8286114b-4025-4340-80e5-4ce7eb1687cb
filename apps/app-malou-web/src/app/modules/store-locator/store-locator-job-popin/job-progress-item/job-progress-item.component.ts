/* eslint-disable @typescript-eslint/member-ordering */
import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, input, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { interval, startWith } from 'rxjs';

import { isNotNil, TimeInMilliseconds } from '@malou-io/package-utils';

import { StoreLocatorJobState } from ':modules/store-locator/models/store-locator.interface';
import { MAX_PROGRESS } from ':shared/components/loader-page/loader-page.component';
import { LoaderProgressComponent } from ':shared/components/loader-progress/loader-progress.component';

@Component({
    selector: 'app-job-progress-item',
    imports: [LoaderProgressComponent],
    templateUrl: './job-progress-item.component.html',
    styleUrl: './job-progress-item.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class JobProgressItemComponent {
    readonly organizationJobState = input.required<StoreLocatorJobState>();

    private readonly _destroyRef = inject(DestroyRef);
    private readonly _translateService = inject(TranslateService);
    private readonly _router = inject(Router);

    private readonly _interval$ = interval(1 * TimeInMilliseconds.SECOND).pipe(startWith(0), takeUntilDestroyed(this._destroyRef));
    private readonly _interval = toSignal(this._interval$);

    readonly jobProgress = computed(() => {
        const jobStartDate = this.organizationJobState().jobStartDate;
        if (!jobStartDate) {
            return 100;
        }
        if (isNotNil(this._interval())) {
            const now = new Date();
            const progressTime = now.getTime() - jobStartDate.getTime();
            const progressPercentage = Math.round((progressTime / this.organizationJobState().jobEstimatedTime) * 100);
            return Math.min(progressPercentage, MAX_PROGRESS);
        }
        return 0;
    });

    readonly generationCompletedText = computed(() =>
        this.organizationJobState().wasLastResultSeen ? this._translateService.instant('common.seen') : undefined
    );

    readonly redirectToStoreLocatorView = (): void => {
        this._router.navigate(['groups', 'seo', 'storeLocator'], {
            queryParams: { organizationId: this.organizationJobState().organizationId },
        });
    };

    readonly generationCompletedAction = signal({
        message: this._translateService.instant('common.see'),
        action: this.redirectToStoreLocatorView,
    });
}
