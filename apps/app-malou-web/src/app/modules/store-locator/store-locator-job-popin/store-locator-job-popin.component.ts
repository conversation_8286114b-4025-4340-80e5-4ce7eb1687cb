import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { StoreLocatorJobType } from '@malou-io/package-utils';

import { StoreLocatorJobState } from ':modules/store-locator/models/store-locator.interface';

import { JobProgressItemComponent } from './job-progress-item/job-progress-item.component';

@Component({
    selector: 'app-store-locator-job-popin',
    imports: [TranslateModule, JobProgressItemComponent],
    templateUrl: './store-locator-job-popin.component.html',
    styleUrl: './store-locator-job-popin.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorJobPopinComponent {
    readonly organizationsJobs = input.required<{ [organizationId: string]: StoreLocatorJobState }>();

    readonly jobStatesPerJobType = computed(() => {
        const data: Record<StoreLocatorJobType, StoreLocatorJobState[]> = {
            [StoreLocatorJobType.CONTENT_GENERATION]: [],
            [StoreLocatorJobType.PUBLICATION]: [],
        };
        Object.values(this.organizationsJobs()).forEach((state) => {
            data[state.jobType].push(state);
        });
        return data;
    });

    readonly allGenerationJobs = computed(() => Object.values(this.jobStatesPerJobType()[StoreLocatorJobType.CONTENT_GENERATION]));
    readonly hasAtLeastOneGenerationState = computed(() => this.allGenerationJobs().length > 0);
    readonly allGenerationCompleted = computed(() => this.allGenerationJobs().every((state) => !state.isJobRunning && !state.jobStartDate));

    readonly allPublicationJobs = computed(() => Object.values(this.jobStatesPerJobType()[StoreLocatorJobType.PUBLICATION]));
    readonly hasAtLeastOnePublicationState = computed(() => this.allPublicationJobs().length > 0);
    readonly allPublicationCompleted = computed(() =>
        this.allPublicationJobs().every((state) => !state.isJobRunning && !state.jobStartDate)
    );
}
