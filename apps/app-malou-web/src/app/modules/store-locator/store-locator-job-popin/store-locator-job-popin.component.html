<div class="flex flex-col gap-y-4">
    @if (hasAtLeastOnePublicationState()) {
        <div>
            @if (allPublicationCompleted()) {
                <div class="malou-text-12--bold text-malou-color-text-1">
                    {{ 'store_locator.publication.all_publications_completed' | translate }}
                </div>
            } @else {
                <div class="malou-text-12--bold text-malou-color-text-1">
                    {{ 'store_locator.publication.publication_in_progress' | translate }}
                </div>
            }
            <hr class="my-3 border-malou-color-background-dark" />
            <div class="flex flex-col gap-y-2">
                @for (organizationPublicationState of allPublicationJobs(); track organizationPublicationState) {
                    <div>
                        <app-job-progress-item [organizationJobState]="organizationPublicationState"></app-job-progress-item>
                    </div>
                }
            </div>
        </div>
    }

    @if (hasAtLeastOneGenerationState()) {
        <div>
            @if (allGenerationCompleted()) {
                <div class="malou-text-12--bold text-malou-color-text-1">
                    {{ 'store_locator.generation.all_creations_completed' | translate }}
                </div>
            } @else {
                <div class="malou-text-12--bold text-malou-color-text-1">
                    {{ 'store_locator.generation.creation_in_progress' | translate }}
                </div>
            }
            <hr class="my-3 border-malou-color-background-dark" />
            <div class="flex flex-col gap-y-2">
                @for (organizationGenerationState of allGenerationJobs(); track organizationGenerationState) {
                    <div>
                        <app-job-progress-item [organizationJobState]="organizationGenerationState"></app-job-progress-item>
                    </div>
                }
            </div>
        </div>
    }
</div>
