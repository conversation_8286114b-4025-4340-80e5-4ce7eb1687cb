import { StoreLocatorJobType } from '@malou-io/package-utils';

export interface StoreLocatorJobState {
    organizationName: string;
    organizationId: string;
    jobType: StoreLocatorJobType;
    isJobRunning: boolean;
    jobStartDate: Date | null;
    jobEstimatedTime: number;
    wasLastResultSeen: boolean;
}

// TODO: change name to StoreLocatorOrganizationPagesState
export interface StoreLocatorRestaurantsPageState {
    organizationId: string;
    hasMissingRestaurantPages: boolean;
    hasUpdatedPages: boolean;
    hasAtLeastOnePageGenerated: boolean;
}
