import { StoreLocatorOrganizationRestaurantDto } from '@malou-io/package-dto';
import { BusinessCategory, PlatformKey } from '@malou-io/package-utils';

import { Address } from ':shared/models/address';

export interface StoreLocatorAttribute {
    id: string;
    priority: number;
    attributeId: string;
    platformKey: PlatformKey;
    attributeName: {
        fr: string;
        en?: string;
        es?: string;
        it?: string;
    };
}

export class StoreLocatorOrganizationRestaurant {
    id: string;
    name: string;
    internalName?: string;
    address?: Address;
    type: BusinessCategory;
    attributeList?: StoreLocatorAttribute[];

    constructor(init: Partial<StoreLocatorOrganizationRestaurant> = {}) {
        this.id = init.id || '';
        this.name = init.name || '';
        this.internalName = init.internalName;
        this.address = init.address ? new Address(init.address) : undefined;
        this.type = init.type ?? BusinessCategory.LOCAL_BUSINESS;
        this.attributeList = init.attributeList;
    }

    static fromDto(dto: StoreLocatorOrganizationRestaurantDto): StoreLocatorOrganizationRestaurant {
        return new StoreLocatorOrganizationRestaurant({
            id: dto.id,
            name: dto.name,
            internalName: dto.internalName,
            address: dto.address ? new Address(dto.address) : undefined,
            attributeList: dto.attributeList,
            type: dto.type,
        });
    }

    static getSelectableAttributes(restaurants: StoreLocatorOrganizationRestaurant[], limit: number): StoreLocatorAttribute[] {
        const attributeCountMap = new Map<string, { attribute: StoreLocatorAttribute; count: number }>();

        restaurants.forEach((restaurant) => {
            if (restaurant.attributeList) {
                restaurant.attributeList.forEach((attribute) => {
                    const existing = attributeCountMap.get(attribute.attributeId);
                    if (existing) {
                        existing.count++;
                    } else {
                        attributeCountMap.set(attribute.attributeId, {
                            attribute,
                            count: 1,
                        });
                    }
                });
            }
        });

        return Array.from(attributeCountMap.values())
            .sort((a, b) => b.count - a.count)
            .map((item) => item.attribute)
            .slice(0, limit)
            .sort((a, b) => a.priority - b.priority);
    }

    getAttributeNameForLang(attributeId: string, lang: string): string {
        const resturantAttribute = this.attributeList?.find((att) => att.attributeId === attributeId);
        if (!resturantAttribute) {
            return '';
        }

        return resturantAttribute.attributeName[lang] || resturantAttribute.attributeName.fr || '';
    }

    getDisplayName(): string {
        return this.internalName || this.name;
    }

    hasCategories(): boolean {
        return Boolean(this.attributeList && this.attributeList.length > 0);
    }

    isBrandBusiness(): boolean {
        return this.type === BusinessCategory.BRAND;
    }

    getFullFormattedAddress(): string {
        return this.address ? this.address.getFullFormattedAddress() : '';
    }
}
