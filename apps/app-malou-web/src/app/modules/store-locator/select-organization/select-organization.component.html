@if (organizations().length > 1) {
    <div class="malou-card max-h-fit bg-malou-color-background-dark p-4">
        <div class="flex items-center justify-between">
            <div class="malou-text-15--semibold malou-color-text-1">
                {{ 'store_locator.select_organization.title' | translate }}
            </div>
            <app-select
                class="w-[400px]"
                [values]="organizations()"
                [formControl]="organizationControl"
                [theme]="SelectBaseDisplayStyle.WITH_BACKGROUND"
                [displayWith]="organizationDisplayWith"
                (selectChange)="onOrganizationChange($event)">
            </app-select>
        </div>
    </div>
}
