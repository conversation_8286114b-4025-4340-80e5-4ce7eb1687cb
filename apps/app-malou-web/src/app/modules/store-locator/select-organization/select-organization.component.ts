import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

import { StoreLocatorOrganizationsContext } from ':modules/store-locator/contexts/store-locator-organizations.context';
import { SelectBaseDisplayStyle } from ':shared/components/select-abstract/select-base.component';
import { SelectComponent } from ':shared/components/select/select.component';
import { Organization } from ':shared/models/organization';

@Component({
    selector: 'app-select-organization',
    imports: [SelectComponent, TranslateModule],
    templateUrl: './select-organization.component.html',
    styleUrl: './select-organization.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SelectOrganizationComponent {
    readonly storeLocatorOrganizationsContext = inject(StoreLocatorOrganizationsContext);

    private readonly _activatedRoute = inject(ActivatedRoute);
    private readonly _destroyRef = inject(DestroyRef);

    readonly SelectBaseDisplayStyle = SelectBaseDisplayStyle;

    readonly organizations = computed(() => this.storeLocatorOrganizationsContext.userOrganizations());
    readonly organizationControl = new FormControl<Organization | null>(null, { nonNullable: false });

    constructor() {
        this._activatedRoute.queryParams.pipe(takeUntilDestroyed(this._destroyRef)).subscribe((params) => {
            const organizationId: string | undefined = params['organizationId'];
            this._initializeSelectedOrganization(organizationId);
        });
    }

    organizationDisplayWith = (organization: Organization): string => organization.name;

    onOrganizationChange(organization: Organization | null): void {
        this.storeLocatorOrganizationsContext.selectedOrganization$.next(organization);
    }

    // TODO: Change this function to select last selected organization
    private _initializeSelectedOrganization(organizationId: string | undefined): void {
        if (organizationId) {
            const organization = this.organizations().find((org) => org._id === organizationId);
            if (organization) {
                this.storeLocatorOrganizationsContext.selectedOrganization$.next(organization);
                this.organizationControl.setValue(organization);
                return;
            }
        }

        const selectedOrganization = this.storeLocatorOrganizationsContext.selectedOrganization$.getValue();
        if (selectedOrganization) {
            this.storeLocatorOrganizationsContext.selectedOrganization$.next(selectedOrganization);
            this.organizationControl.setValue(selectedOrganization);
            return;
        }

        const firstOrganization = this.organizations()[0];
        this.storeLocatorOrganizationsContext.selectedOrganization$.next(firstOrganization);
        this.organizationControl.setValue(firstOrganization);
    }
}
