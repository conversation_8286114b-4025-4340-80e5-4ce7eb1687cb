import { NgTemplateOutlet } from '@angular/common';
import { Component, inject, input, output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { ToastService } from ':core/services/toast.service';
import { EditAiSettingsModalComponent } from ':modules/store-locator/edit-ai-settings-modal/edit-ai-settings-modal.component';
import { EditAiSettingsModalInputData } from ':modules/store-locator/edit-ai-settings-modal/edit-ai-settings-modal.interface';
import { EditLanguagesModalComponent } from ':modules/store-locator/edit-languages-modal/edit-languages-modal.component';
import { EditLanguagesModalInputData } from ':modules/store-locator/edit-languages-modal/edit-languages-modal.interface';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/models/store-locator-organization-config';
import { StoreLocatorOrganizationKeyword } from ':modules/store-locator/models/store-locator-organization-keyword';
import { StoreLocatorOrganizationRestaurant } from ':modules/store-locator/models/store-locator-organization-restaurant';
import { StoreLocatorContext } from ':modules/store-locator/store-locator.context';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

@Component({
    selector: 'app-organization-configuration',
    imports: [MatButtonModule, NgTemplateOutlet, IllustrationPathResolverPipe, MatIconModule, MatProgressSpinnerModule, TranslateModule],
    templateUrl: './organization-configuration.component.html',
    styleUrl: './organization-configuration.component.scss',
})
export class OrganizationConfigurationComponent {
    readonly storeLocatorOrganizationConfiguration = input.required<StoreLocatorOrganizationConfiguration>();
    readonly storeLocatorOrganizationRestaurants = input.required<StoreLocatorOrganizationRestaurant[]>();
    readonly organizationRestaurantKeywords = input.required<StoreLocatorOrganizationKeyword[]>();
    readonly startPagesGeneration = output<void>();

    private readonly _storeLocatorContext = inject(StoreLocatorContext);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _toastService = inject(ToastService);
    private readonly _translate = inject(TranslateService);

    readonly Illustration = Illustration;
    readonly SvgIcon = SvgIcon;

    openEditAiSettingsModal(): void {
        const data: EditAiSettingsModalInputData = {
            organizationId: this.storeLocatorOrganizationConfiguration().organizationId,
            aiSettings: this.storeLocatorOrganizationConfiguration().aiSettings,
            organizationRestaurants: this.storeLocatorOrganizationRestaurants(),
            organizationRestaurantKeywords: this.organizationRestaurantKeywords(),
        };

        this._customDialogService
            .open<EditAiSettingsModalComponent, EditAiSettingsModalInputData>(EditAiSettingsModalComponent, {
                height: 'unset',
                maxHeight: '90vh',
                width: '90vw',
                maxWidth: '80vw',
                data,
            })
            .afterClosed()
            .subscribe({
                next: (result: StoreLocatorOrganizationConfiguration | undefined) => {
                    if (result) {
                        this._storeLocatorContext.updateStoreLocatorOrganizationConfiguration(result);
                        this._toastService.openSuccessToast(
                            this._translate.instant('store_locator.edit_ai_settings_modal.success_message')
                        );
                    }
                },
                error: (error) => {
                    console.error('Error opening EditAiSettingsModal:', error);
                    this._toastService.openErrorToast(this._translate.instant('common.unknown_error'));
                },
            });
    }

    openEditLanguagesModal(): void {
        const storeLocatorOrganizationConfiguration: StoreLocatorOrganizationConfiguration | null =
            this._storeLocatorContext.storeLocatorOrganizationConfiguration();

        if (!storeLocatorOrganizationConfiguration) {
            return;
        }

        this._customDialogService
            .open<EditLanguagesModalComponent, EditLanguagesModalInputData>(EditLanguagesModalComponent, {
                height: 'unset',
                maxHeight: '90vh',
                width: '90vw',
                maxWidth: '80vw',
                data: {
                    organizationId: storeLocatorOrganizationConfiguration.organizationId,
                    languages: storeLocatorOrganizationConfiguration.languages,
                },
            })
            .afterClosed()
            .subscribe({
                next: (result: StoreLocatorOrganizationConfiguration | undefined) => {
                    if (result) {
                        this._storeLocatorContext.updateStoreLocatorOrganizationConfiguration(result);
                        this._toastService.openSuccessToast(
                            this._translate.instant('store_locator.configuration.translation.languages_updated')
                        );
                    }
                },
                error: (error) => {
                    console.error('Error opening EditLanguagesModal:', error);
                    this._toastService.openErrorToast(this._translate.instant('common.unknown_error'));
                },
            });
    }

    finalizeConfiguration(): void {
        const isAiSettingsConfigured = this.storeLocatorOrganizationConfiguration().isAiSettingsConfigured();
        const isStylesConfigured = this.storeLocatorOrganizationConfiguration().isStylesConfigured();
        const isLanguagesConfigured = this.storeLocatorOrganizationConfiguration().isLanguagesConfigured();

        if (!isStylesConfigured) {
            // TODO: open styles configuration modal
        }

        if (!isAiSettingsConfigured) {
            this.openEditAiSettingsModal();
        }

        if (!isLanguagesConfigured) {
            this.openEditLanguagesModal();
        }
    }

    startStoreLocatorPagesGeneration(): void {
        this.startPagesGeneration.emit();
    }
}
