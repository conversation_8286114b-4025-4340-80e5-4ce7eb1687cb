import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, HostBinding, OnInit, signal, WritableSignal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateModule } from '@ngx-translate/core';
import { compact, uniq, uniqBy } from 'lodash';
import { combineLatest, Observable, switchMap } from 'rxjs';
import { filter, map, tap } from 'rxjs/operators';

import {
    GetMergedInformationUpdateBodyDto,
    GetMergedInformationUpdateResponseDto,
    MergedInformationUpdateDto,
} from '@malou-io/package-dto';
import {
    InformationUpdatePlatformStateStatus,
    isNotNil,
    PlatformAccessStatus,
    PlatformAccessType,
    PlatformKey,
} from '@malou-io/package-utils';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { InformationUpdatesService } from ':core/services/information-update.service';
import { PlatformsService } from ':core/services/platforms.service';
import { PlatformsManagementViewType } from ':modules/admin/platforms-management/platforms-management-actions-header/platforms-management-action-header.interface';
import { PlatformsManagementActionsHeaderComponent } from ':modules/admin/platforms-management/platforms-management-actions-header/platforms-management-actions-header.component';
import { UpdatesAndAccessesPanelContentComponent } from ':modules/admin/platforms-management/updates-and-accesses-panel-content/updates-and-accesses-panel-content.component';
import {
    PlatformManagementUpdateData,
    PlatformManagementUpdateDoneData,
} from ':modules/admin/platforms-management/updates/updates.component';
import { selectSelectedOption, selectShowTreated, selectUserRestaurants, selectViewType } from ':modules/admin/store/admin.reducer';
import { InformationUpdateOptions, PlatformAccess, Restaurant } from ':shared/models';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { PlatformLogoPathResolverPipe } from ':shared/pipes/platform-logo-path-resolver.pipe';

export interface PlatformManagementPanelContent {
    title: string;
    updatesData: PlatformManagementUpdateData[];
    accessList: PlatformAccess[];
    badge: number;
    openPanelId: string;
    imageSrc?: string;
}

interface RestaurantPlatformManagementData {
    restaurant: Restaurant;
    platformKey: PlatformKey;
    access?: PlatformAccess;
    updateData?: PlatformManagementUpdateData;
}

@Component({
    selector: 'app-platforms-management',
    templateUrl: './platforms-management.component.html',
    styleUrls: ['./platforms-management.component.scss'],
    imports: [
        FormsModule,
        IllustrationPathResolverPipe,
        MalouSpinnerComponent,
        MatButtonModule,
        MatCheckboxModule,
        MatDividerModule,
        MatExpansionModule,
        MatIconModule,
        NgTemplateOutlet,
        PlatformsManagementActionsHeaderComponent,
        ReactiveFormsModule,
        TranslateModule,
        UpdatesAndAccessesPanelContentComponent,
    ],
    providers: [PlatformLogoPathResolverPipe],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PlatformsManagementComponent implements OnInit {
    @HostBinding('class') classes = 'h-full';

    readonly searchText: WritableSignal<string> = signal('');

    readonly viewType = this._store.selectSignal(selectViewType);
    readonly isBusinessView = computed(() => this.viewType() === PlatformsManagementViewType.BUSINESS);
    readonly selectedOption = this._store.selectSignal(selectSelectedOption);

    readonly FAILED_ACCESS_STATUSES = [
        PlatformAccessStatus.UNCLAIMED_PAGE,
        PlatformAccessStatus.INVALID_PAGE,
        PlatformAccessStatus.BAD_ACCESS,
        PlatformAccessStatus.FAILED,
    ];

    readonly showProcessed$ = this._store.select(selectShowTreated);
    readonly showProcessed = this._store.selectSignal(selectShowTreated);

    readonly myRestaurants$: Observable<Restaurant[]> = this._store.select(selectUserRestaurants);

    readonly allAccessList = signal<PlatformAccess[]>([]);
    readonly restaurantPlatformManagementData = toSignal(this._getRestaurantPlatformManagementData$());

    readonly panelContents = computed(() => {
        const restaurantPlatformManagementData = this.restaurantPlatformManagementData();
        if (!restaurantPlatformManagementData) {
            return;
        }

        const selectedOption = this.selectedOption();

        let panelContents: PlatformManagementPanelContent[] = [];
        if (this.isBusinessView()) {
            const restaurants = uniqBy(
                restaurantPlatformManagementData.map((d) => d.restaurant),
                '_id'
            );
            panelContents = restaurants
                .map((restaurant) => {
                    const accessList: PlatformAccess[] = compact(
                        restaurantPlatformManagementData
                            .filter((d) => d.restaurant._id === restaurant._id)
                            .map((d) => (d.access ? { ...d.access, restaurantAddress: d.restaurant.address } : undefined))
                    );
                    const updatesData = compact(
                        restaurantPlatformManagementData.filter((d) => d.restaurant._id === restaurant._id).map((d) => d.updateData)
                    );
                    if (this._isAccessListEmpty(accessList) && updatesData.length === 0) {
                        return null;
                    }
                    return {
                        title: restaurant.internalName || restaurant.name,
                        updatesData,
                        accessList,
                        badge: this._getUnprocessedUpdatesAndAccessCount(updatesData, accessList, selectedOption),
                        openPanelId: restaurant._id,
                        imageSrc: restaurant.logo?.urls?.original,
                    };
                })
                .filter(isNotNil)
                .sort(this._getSortPanelListContentMethod());
        } else {
            const platformKeys = uniq(restaurantPlatformManagementData.map((d) => d.platformKey));
            panelContents = platformKeys
                .map((platformKey) => {
                    const accessList = compact(
                        restaurantPlatformManagementData
                            .filter((d) => d.platformKey === platformKey)
                            .map((d) => (d.access ? { ...d.access, restaurantAddress: d.restaurant.address } : undefined))
                    );
                    const updatesData = compact(
                        restaurantPlatformManagementData.filter((d) => d.platformKey === platformKey).map((d) => d.updateData)
                    );
                    if (this._isAccessListEmpty(accessList) && this._isUpdatesDataEmpty(updatesData)) {
                        return null;
                    }
                    return {
                        title: this._enumTranslatePipe.transform(platformKey, 'platform_key'),
                        updatesData,
                        accessList,
                        badge: this._getUnprocessedUpdatesAndAccessCount(updatesData, accessList, selectedOption),
                        openPanelId: platformKey,
                        imageSrc: this._platformLogoPathResolverPipe.transform(platformKey),
                    };
                })
                .filter(isNotNil)
                .sort(this._getSortPanelListContentMethod());
        }

        if (!this.showProcessed()) {
            panelContents = panelContents.filter((panelContent) => panelContent.badge > 0);
        }
        return this.filterByText(this.searchText(), panelContents);
    });

    currentOpenPanelId: string | null;
    readonly accessUpdate: WritableSignal<PlatformManagementUpdateDoneData | null> = signal(null);

    readonly InformationUpdateOptions = InformationUpdateOptions;

    constructor(
        private readonly _store: Store,
        private readonly _route: ActivatedRoute,
        private readonly _enumTranslatePipe: EnumTranslatePipe,
        private readonly _informationUpdatesService: InformationUpdatesService,
        private readonly _platformLogoPathResolverPipe: PlatformLogoPathResolverPipe,
        private readonly _platformsService: PlatformsService
    ) {}

    ngOnInit(): void {
        this._route.queryParams.subscribe((queryParams) => {
            if (queryParams.restaurant_id) {
                this.currentOpenPanelId = queryParams.restaurant_id;
            }
        });
    }

    filterByText(searchText: string, elements: PlatformManagementPanelContent[]): PlatformManagementPanelContent[] {
        return searchText
            ? elements.filter((element) => element.title.toLocaleLowerCase()?.includes(searchText.toLocaleLowerCase()))
            : elements;
    }

    onSearchChange(searchText: string): void {
        this.searchText.set(searchText);
    }

    openPanel(panelContent: PlatformManagementPanelContent): void {
        this.currentOpenPanelId = panelContent.openPanelId;
    }

    closePanel(): void {
        this.currentOpenPanelId = null;
    }

    onPlatformUpdate(data: PlatformManagementUpdateDoneData): void {
        if (
            ![
                InformationUpdatePlatformStateStatus.MANUAL_UPDATE_ERROR,
                InformationUpdatePlatformStateStatus.BAD_ACCESS,
                InformationUpdatePlatformStateStatus.INVALID_PAGE,
                InformationUpdatePlatformStateStatus.UNCLAIMED_PAGE,
            ].includes(data.status)
        ) {
            return;
        }
        this.accessUpdate.set(data);
    }

    private _getRestaurantPlatformManagementData$(): Observable<RestaurantPlatformManagementData[]> {
        const platforms$ = this.myRestaurants$.pipe(
            filter((myRestaurants) => !!myRestaurants.length),

            tap((myRestaurants) => {
                this.allAccessList.set(myRestaurants.map((restaurant) => this._getAccessListFromRestaurantAccess(restaurant)).flat());
            }),

            switchMap((myRestaurants) =>
                this._platformsService
                    .getPlatformsForRestaurants({ restaurantIds: myRestaurants.map((restaurant) => restaurant._id) })
                    .pipe(map((res) => ({ platforms: res.data, myRestaurants })))
            ),

            switchMap(({ myRestaurants, platforms }) =>
                this._getMergedInformationUpdateData$(myRestaurants).pipe(
                    map((mergedInfoUpdates) => ({ mergedInfoUpdates, myRestaurants, platforms }))
                )
            )
        );

        return combineLatest([platforms$, this.showProcessed$]).pipe(
            map(([{ mergedInfoUpdates, myRestaurants }, showProcessed]) => {
                const data: RestaurantPlatformManagementData[][] = myRestaurants.map((restaurant): RestaurantPlatformManagementData[] => {
                    const mergedInformationUpdates = mergedInfoUpdates.find(
                        (mergedInformationUpdateForRestaurantId) => mergedInformationUpdateForRestaurantId.restaurantId === restaurant._id
                    );
                    const updatesData = this._buildUpdatesData(
                        mergedInformationUpdates?.mergedInformationUpdatesByPlatform ?? [],
                        restaurant,
                        showProcessed
                    );
                    const accessList = this._buildAccessList(restaurant, showProcessed);

                    const platformKeyToShow = uniq([
                        ...updatesData.map((updateData) => updateData.platformKey),
                        ...accessList.map((access) => access.platformKey),
                    ]);
                    return platformKeyToShow.map(
                        (platformKey): RestaurantPlatformManagementData => ({
                            restaurant,
                            platformKey,
                            access: accessList.find((access) => access.platformKey === platformKey),
                            updateData: updatesData.find((updateData) => updateData.platformKey === platformKey),
                        })
                    );
                });
                return data.flat();
            })
        );
    }

    private _getMergedInformationUpdateData$(restaurants: Restaurant[]): Observable<GetMergedInformationUpdateResponseDto> {
        const body: GetMergedInformationUpdateBodyDto = restaurants
            .map((restaurant) => ({
                restaurantId: restaurant._id,
                platformKeys: restaurant.access
                    .filter(
                        (access) =>
                            access.active &&
                            access.accessType !== PlatformAccessType.AUTO &&
                            access.platformKey !== ('pagesjaunes' as PlatformKey)
                    )
                    .map((access) => access.platformKey),
            }))
            .filter((restaurantAndPlatforms) => restaurantAndPlatforms.platformKeys.length > 0);
        return this._informationUpdatesService.getMergedInformationUpdateData(body).pipe(map((res) => res.data));
    }

    private _buildUpdatesData(
        mergedInformationUpdates: MergedInformationUpdateDto[],
        restaurant: Restaurant,
        showProcessed: boolean = false
    ): PlatformManagementUpdateData[] {
        if (!mergedInformationUpdates.length) {
            return [];
        }

        const accessesOfConnectedNonAutoPlatform = restaurant.access
            .filter((access) => access.active)
            .filter((access) => access.accessType !== PlatformAccessType.AUTO);

        return accessesOfConnectedNonAutoPlatform
            .map((access: PlatformAccess) => ({
                platformKey: access.platformKey,
                platformAccess: access,
                mergedInformationUpdate: mergedInformationUpdates.find(
                    (mergedInformationUpdate) => mergedInformationUpdate.platformState.key === access.platformKey
                ),
                restaurant: restaurant,
            }))
            .filter((update): update is PlatformManagementUpdateData => !!update.mergedInformationUpdate)
            .filter((update) =>
                showProcessed ? true : update.mergedInformationUpdate.platformState.status === InformationUpdatePlatformStateStatus.PENDING
            );
    }

    private _buildAccessList(restaurant: Restaurant, processed: boolean = false): PlatformAccess[] {
        const accessList = this._getAccessListFromRestaurantAccess(restaurant);
        return processed
            ? accessList.filter(
                  (access) =>
                      (access.active && access.accessType !== PlatformAccessType.AUTO) ||
                      this.FAILED_ACCESS_STATUSES.includes(access.status)
              )
            : accessList.filter(
                  (access) =>
                      access.active && access.accessType !== PlatformAccessType.AUTO && access.status === PlatformAccessStatus.NEED_REVIEW
              );
    }

    private _getAccessListFromRestaurantAccess(restaurant: Restaurant): PlatformAccess[] {
        return restaurant.access.map((access) => ({
            ...access,
            restaurantId: restaurant._id,
            restaurantName: restaurant.name,
            restaurantLogo: restaurant.logo ?? undefined,
        }));
    }

    private _isAccessListEmpty(accessList: PlatformAccess[]): boolean {
        return (
            accessList.filter(
                (access) =>
                    (access.active && access.accessType !== PlatformAccessType.AUTO) || this.FAILED_ACCESS_STATUSES.includes(access.status)
            ).length === 0
        );
    }

    private _isUpdatesDataEmpty(updatesData: PlatformManagementUpdateData[]): boolean {
        return updatesData.length === 0;
    }

    private _getUnprocessedUpdatesAndAccessCount(
        updatesData: PlatformManagementUpdateData[],
        accessList: PlatformAccess[],
        selection: InformationUpdateOptions
    ): number {
        const unprocessedUpdatesCount = updatesData.filter((update) =>
            [
                InformationUpdatePlatformStateStatus.ERROR,
                InformationUpdatePlatformStateStatus.MANUAL_UPDATE_ERROR,
                InformationUpdatePlatformStateStatus.BAD_ACCESS,
                InformationUpdatePlatformStateStatus.UNCLAIMED_PAGE,
                InformationUpdatePlatformStateStatus.INVALID_PAGE,
                InformationUpdatePlatformStateStatus.PENDING,
            ].includes(update.mergedInformationUpdate.platformState.status)
        ).length;

        const unprocessedAccessesCount = accessList.filter((al) => al.status === 'need_review')?.length;

        switch (selection) {
            case InformationUpdateOptions.BOTH:
                return unprocessedUpdatesCount + unprocessedAccessesCount;
            case InformationUpdateOptions.ACCESS:
                return unprocessedAccessesCount;
            case InformationUpdateOptions.UPDATE:
                return unprocessedUpdatesCount;
            default:
                return 0;
        }
    }

    private _getSortPanelListContentMethod(): (a: PlatformManagementPanelContent, b: PlatformManagementPanelContent) => number {
        return (panelContentA, panelContentB) => {
            const aDates = compact([
                ...panelContentA.updatesData.map((updateData) => updateData.platformAccess.lastUpdated),
                ...panelContentA.accessList.map((access) => access.lastUpdated),
            ]).map((date) => new Date(date));
            const bDates = compact([
                ...panelContentB.updatesData.map((updateData) => updateData.platformAccess.lastUpdated),
                ...panelContentB.accessList.map((access) => access.lastUpdated),
            ]).map((date) => new Date(date));

            const endDateA = aDates.sort((a, b) => a.getTime() - b.getTime())[0];
            const endDateB = bDates.sort((a, b) => a.getTime() - b.getTime())[0];
            if (endDateA === endDateB) {
                return 0;
            } else if (endDateA === undefined) {
                return 1;
            } else if (endDateB === undefined) {
                return -1;
            } else {
                return endDateA > endDateB ? 1 : -1;
            }
        };
    }
}
