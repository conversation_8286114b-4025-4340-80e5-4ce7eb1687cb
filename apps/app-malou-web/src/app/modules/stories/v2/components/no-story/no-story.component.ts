import { ChangeDetectionStrategy, Component, inject, input, output } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

import { ButtonComponent } from ':shared/components/button/button.component';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';

@Component({
    selector: 'app-no-story',
    templateUrl: './no-story.component.html',
    styleUrls: ['./no-story.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [TranslateModule, IllustrationPathResolverPipe, ButtonComponent],
})
export class NoStoryComponent {
    readonly isInstagramConnected = input.required<boolean>();
    readonly isFacebookConnected = input.required<boolean>();
    readonly restaurantId = input.required<string>();
    readonly createPost = output<void>();

    private readonly _router = inject(Router);

    readonly Illustration = Illustration;

    onCreatePost(): void {
        this.createPost.emit();
    }

    onConnectInstagram(): void {
        this._router.navigate(['restaurants', this.restaurantId(), 'settings', 'platforms', 'connection']);
    }
}
