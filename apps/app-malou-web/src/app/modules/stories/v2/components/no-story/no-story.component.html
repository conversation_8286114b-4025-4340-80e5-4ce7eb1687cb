<div class="flex h-full w-full flex-col items-center justify-center gap-2">
    @if (isInstagramConnected() || isFacebookConnected()) {
        <img class="mb-3 w-56 rotate-[12deg]" [src]="Illustration.BestFoodInTown | illustrationPathResolver" />
        <div class="malou-text-14--bold text-malou-color-text-1">{{ 'stories.no_stories.title' | translate }}</div>
        <div class="malou-text-10--regular text-malou-color-text-2">{{ 'stories.no_stories.description' | translate }}</div>
        <app-button
            [text]="'stories.create_story' | translate"
            [size]="'large'"
            [isSquareButton]="true"
            (buttonClick)="onCreatePost()"></app-button>
    } @else {
        <img class="mb-3 w-44" [src]="Illustration.OkHand | illustrationPathResolver" />
        <div class="malou-text-14--bold text-malou-color-text-1">
            {{ 'stories.no_stories.instagram_and_facebook_not_connected' | translate }}
        </div>
        <div class="malou-text-10--regular text-malou-color-text-2">{{ 'stories.no_stories.you_can_still_create_story' | translate }}</div>
        <div class="flex gap-4">
            <app-button
                [text]="'stories.create_draft_story' | translate"
                [size]="'large'"
                [isSquareButton]="true"
                [theme]="'secondary--alt'"
                (buttonClick)="onCreatePost()"></app-button>
            <app-button
                [text]="'stories.connect_instagram_or_facebook' | translate"
                [size]="'large'"
                [isSquareButton]="true"
                (buttonClick)="onConnectInstagram()"></app-button>
        </div>
    }
</div>
