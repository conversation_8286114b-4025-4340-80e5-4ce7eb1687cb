@let userCanManagePost = CaslAction.MANAGE | caslAble: CaslSubject.SOCIAL_POST;

<div
    class="flex overflow-hidden rounded-[5px] border border-malou-color-border-primary bg-malou-color-background-white transition-all duration-500"
    [ngClass]="{ 'border-[4px] border-malou-color-purple--light': isHighlighted() }">
    <div class="min-h-[105px] w-[105px] shrink-0 self-stretch">
        <app-social-post-media-item
            [ngClass]="{ 'cursor-pointer': story() | applySelfPure: 'canEdit' }"
            [media]="story().media"
            [link]="story().socialLink"
            [tag]="feedbackCountTag()"
            [isReadonly]="isReadonly()"
            (tagClick)="onFeedbackCountTagClick($event)"
            (click)="onUpdateStory()"></app-social-post-media-item>
    </div>

    <div class="flex min-w-0 grow flex-col gap-3 px-3 pb-1 pt-2">
        <app-post-item-header
            [sortedPlatformKeys]="sortedPlatformKeys()"
            [published]="story().published"
            [isPublishing]="story().isPublishing"
            [isActive]="story() | applySelfPure: 'isActive'"
            [postDate]="story() | applySelfPure: 'getPostDate'"
            [authorInitials]="authorInitials()"
            [authorName]="authorName()"
            [canSchedule]="story() | applySelfPure: 'canSchedule'"
            [showActionsButton]="
                (story() | applySelfPure: 'canOpenSocialLink') ||
                (story() | applySelfPure: 'canEdit') ||
                (story() | applySelfPure: 'canDelete') ||
                (story() | applySelfPure: 'canDuplicate')
            "
            [isReadonly]="isReadonly()"
            [userCanManagePost]="userCanManagePost"
            [actions]="actions()"></app-post-item-header>

        @if (story().published === PostPublicationStatus.ERROR) {
            <ng-container [ngTemplateOutlet]="errorTemplate"></ng-container>
        } @else {
            <div class="flex flex-col gap-1">
                @if (story().media; as media) {
                    @let duration = story().getMediaDuration();
                    <div class="malou-text-10--regular italic text-malou-color-text-2">
                        {{ media.type | enumTranslate: 'media_type' }} - {{ duration }}
                        {{ 'common.seconds' | pluralTranslate: duration | lowercase }}
                    </div>
                }
            </div>
        }
    </div>
</div>

<ng-template #errorTemplate>
    @let mostRecentPublicationErrorCode = story().mostRecentPublicationErrorCode ?? PublicationErrorCode.UNKNOWN_ERROR;
    <div class="w-fit rounded-md bg-malou-color-background-error px-3 py-1 italic">
        <span class="malou-text-10--regular text-malou-color-state-error">{{
            mostRecentPublicationErrorCode
                | enumTranslate: 'publication_error_code' : undefined : { platformKey: story().platformKeys[0].toString() }
        }}</span>
        <span
            class="malou-text-10--semibold ml-1 text-malou-color-primary"
            [ngClass]="{ 'cursor-pointer': !isReadonly() }"
            [id]="'tracking_stories_error_cta_' + (mostRecentPublicationErrorCode | lowercase)"
            (click)="!isReadonly() && onErrorCtaClick(mostRecentPublicationErrorCode)">
            {{
                mostRecentPublicationErrorCode
                    | enumTranslate: 'publication_error_code_cta' : undefined : { platformKey: story().platformKeys[0].toString() }
            }}
        </span>
    </div>
</ng-template>
