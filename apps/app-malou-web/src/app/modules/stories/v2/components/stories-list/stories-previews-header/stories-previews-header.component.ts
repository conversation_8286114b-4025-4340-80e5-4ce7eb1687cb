import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, model, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatMenuModule } from '@angular/material/menu';
import { filter, forkJoin, switchMap } from 'rxjs';

import { isNotNil, PlatformKey } from '@malou-io/package-utils';

import { PlatformsService } from ':core/services/platforms.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { PlatformLogoComponent } from ':shared/components/platform-logo/platform-logo.component';

@Component({
    selector: 'app-stories-previews-header',
    templateUrl: './stories-previews-header.component.html',
    styleUrls: ['./stories-previews-header.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [NgClass, MatMenuModule, PlatformLogoComponent],
})
export class StoriesPreviewsHeaderComponent {
    readonly selectedPreviewPlatform = model.required<PlatformKey.INSTAGRAM | PlatformKey.FACEBOOK>();

    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _platformsService = inject(PlatformsService);

    readonly PlatformKey = PlatformKey;

    readonly DEFAULT_USERNAME = '--';

    readonly igUsername = signal<string>('');
    readonly fbUsername = signal<string>('');

    constructor() {
        this._restaurantsService.restaurantSelected$
            .pipe(
                filter(isNotNil),
                switchMap((restaurant) =>
                    forkJoin([
                        this._platformsService.getPlatformSocialLink(restaurant._id, PlatformKey.INSTAGRAM),
                        this._platformsService.getPlatformSocialLink(restaurant._id, PlatformKey.FACEBOOK),
                    ])
                ),
                takeUntilDestroyed()
            )
            .subscribe(([instagramRes, facebookRes]) => {
                this.igUsername.set(instagramRes.data?.socialLink?.match(/^https:\/\/www.instagram.com\/(.*)/)?.[1] ?? '');
                this.fbUsername.set(facebookRes.data?.socialLink?.match(/^https:\/\/www.facebook.com\/(.*)/)?.[1] ?? '');
            });
    }

    onHeaderClicked(_platformKey: PlatformKey): void {
        // TODO: open platform preview
    }
}
