<div
    class="flex h-[67px] items-center gap-x-3 border-b border-l border-malou-color-border-primary bg-malou-color-background-white px-6 py-2"
    [ngClass]="{ 'cursor-pointer': igUsername() && fbUsername() }"
    [matMenuTriggerFor]="menu">
    @if (!igUsername() && !fbUsername()) {
        <app-platform-logo imgClasses="h-8 w-8" [logo]="PlatformKey.INSTAGRAM"></app-platform-logo>
        <span class="malou-text-12--bold text-malou-color-text-1"> &#64;{{ DEFAULT_USERNAME }}</span>
    } @else {
        <app-platform-logo imgClasses="h-8 w-8" [logo]="selectedPreviewPlatform()"></app-platform-logo>
        <span class="malou-text-12--bold text-malou-color-text-1">
            &#64;{{ selectedPreviewPlatform() === PlatformKey.INSTAGRAM ? igUsername() : fbUsername() }}</span
        >
    }
</div>

<mat-menu class="malou-mat-menu malou-box-shadow !rounded-b-[10px]" #menu="matMenu">
    @if (igUsername()) {
        <button mat-menu-item (click)="onHeaderClicked(PlatformKey.INSTAGRAM)">
            <app-platform-logo imgClasses="h-8 w-8" [logo]="PlatformKey.INSTAGRAM"></app-platform-logo>
            <span class="malou-text-12--bold text-malou-color-text-1"> &#64;{{ igUsername() }}</span>
        </button>
    }

    @if (fbUsername()) {
        <button mat-menu-item (click)="onHeaderClicked(PlatformKey.FACEBOOK)">
            <app-platform-logo imgClasses="h-8 w-8" [logo]="PlatformKey.FACEBOOK"></app-platform-logo>
            <span class="malou-text-12--bold text-malou-color-text-1"> &#64;{{ fbUsername() }}</span>
        </button>
    }
</mat-menu>
