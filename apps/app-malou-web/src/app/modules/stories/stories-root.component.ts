import { ChangeDetectionStrategy, Component, computed, inject, OnDestroy } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';

import { ExperimentationService } from ':core/services/experimentation.service';
import { StoriesComponent } from ':modules/stories/stories.component';
import { StoriesListComponent } from ':modules/stories/v2/components/stories-list/stories-list.component';
import { StoriesContext } from ':modules/stories/v2/stories.context';
import { PostSkeletonComponent } from ':shared/components/skeleton/templates/post-skeleton/post-skeleton.component';

@Component({
    selector: 'app-stories-root',
    templateUrl: './stories-root.component.html',
    styleUrls: ['./stories-root.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [StoriesComponent, StoriesListComponent, PostSkeletonComponent],
})
export class StoriesRootComponent implements OnDestroy {
    private readonly _storiesContext = inject(StoriesContext);

    private readonly _experimentationService = inject(ExperimentationService);
    private readonly _isReleaseStoriesV2Enabled = toSignal(this._experimentationService.isFeatureEnabled$('release-stories-v2'), {
        initialValue: false,
    });

    readonly isGrowthbokLoaded = toSignal(this._experimentationService.isLoaded$, { initialValue: false });
    readonly showStoriesListV2 = computed(() => this.isGrowthbokLoaded() && this._isReleaseStoriesV2Enabled());

    ngOnDestroy(): void {
        this._storiesContext.stopPollingStoriesStatus$.next();
    }
}
