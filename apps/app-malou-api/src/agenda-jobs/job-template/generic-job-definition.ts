import * as Sentry from '@sentry/node';
import { Job } from 'agenda';
import lodash from 'lodash';
import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { errorReplacer, isNotNil } from '@malou-io/package-utils';

import { JOBS_STARTUP_COLOR } from ':agenda-jobs/logs-colors';
import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AsyncLocalStorageService } from ':helpers/classes/async-local-storage-service';
import { AsyncLocalStorageContextType } from ':helpers/classes/async-local-storage-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { getColoredString } from ':helpers/get-colored-string';
import { logger } from ':helpers/logger';
import { AgendaJobData } from ':helpers/validators/jobs/job-data.types';
import { metricsService } from ':services/metrics.service';

type RetryStrategy = {
    maxAttemptsCount: number;
    computeRetryDelayInMinutes(numberOfRetries: number): number;
    executeAfterMaxAttemptsCount(err: Error, job: Job): Promise<void>;
};

interface GenericJobDefinitionParams {
    agendaJobName: AgendaJobName;
    shouldAwaitExecution?: boolean;
    retryStrategy?: RetryStrategy;
    shouldDeleteJobOnSuccess?: boolean;
    lockLifetimeMs?: number;
    getLogMetadata?: <T extends AgendaJobName>(job: Job<AgendaJobData[T]>) => Promise<Partial<AsyncLocalStorageContextType> | undefined>;
}

enum JobResponseStatus {
    SUCCEEDED = 'succeeded',
    FAILED = 'failed',
}

const callCounter = metricsService.getMeter().createCounter<{
    agendaJobName: AgendaJobName;
}>(`worker.job.call.count`, {
    description: 'Number of job calls',
});
const responseCounter = metricsService.getMeter().createCounter<{
    agendaJobName: AgendaJobName;
    responseStatus: JobResponseStatus;
}>(`worker.job.response.count`, {
    description: 'Number of job responses',
});
const processingDuration = metricsService.getMeter().createHistogram<{
    agendaJobName: AgendaJobName;
    responseStatus: JobResponseStatus;
}>(`worker.job.processing.duration.seconds`, {
    description: 'Duration of job processing in seconds',
    advice: { explicitBucketBoundaries: metricsService.getDefaultBucketBoundaries() },
    unit: 'seconds',
});

/**
 * Compute retry delay for a job with a fibonacci sequence starting with 5 (5, 10, 15, 25, 40, ...)
 */
export const defaultComputeRetryDelayInMinutes = (numberOfRetries: number): number => {
    if (Number.isNaN(numberOfRetries) || numberOfRetries <= 2) {
        return 5;
    }

    return defaultComputeRetryDelayInMinutes(numberOfRetries - 1) + defaultComputeRetryDelayInMinutes(numberOfRetries - 2);
};

const defaultExecuteAfterMaxAttemptsCount = (): Promise<void> => Promise.resolve();

export abstract class GenericJobDefinition {
    protected readonly agendaJobName: AgendaJobName;
    private readonly _shouldAwaitExecution: boolean;
    private readonly _retryStrategy?: Readonly<RetryStrategy>;
    private readonly _getLogMetadata?: GenericJobDefinitionParams['getLogMetadata'];

    protected readonly _agendaSingleton = container.resolve(AgendaSingleton);
    private readonly _asyncLocalStorageService = container.resolve(AsyncLocalStorageService);

    private _receivedMessageDateTime: DateTime = DateTime.now();
    private _shouldDeleteJobOnSuccess: boolean;
    private _lockLifetimeMs?: number;

    constructor({
        agendaJobName,
        shouldAwaitExecution,
        retryStrategy,
        getLogMetadata,
        shouldDeleteJobOnSuccess,
        lockLifetimeMs,
    }: GenericJobDefinitionParams) {
        this.agendaJobName = agendaJobName;
        this._shouldAwaitExecution = shouldAwaitExecution ?? true;
        this._retryStrategy = this._initRetryStrategy(retryStrategy);
        this._getLogMetadata = getLogMetadata;
        this._shouldDeleteJobOnSuccess = shouldDeleteJobOnSuccess ?? false;
        this._lockLifetimeMs = lockLifetimeMs;
    }

    /**
     * You should always await the use case execution in executeJob.
     * Whether we call it synchronously or asynchronously is decided in the init method below
     *
     * @param {Job} job
     */
    abstract executeJob(job: Job): Promise<void>;

    async init(): Promise<void> {
        logger.info(getColoredString(`${this._getLogsSuffix()} - Initializing...`, JOBS_STARTUP_COLOR));

        const agenda = await this._agendaSingleton.getInstance();

        agenda.define(this.agendaJobName, { lockLifetime: this._lockLifetimeMs }, async (job: Job) =>
            this._asyncLocalStorageService.createStoreAndRun(
                await this._getJobMetadata(job),
                async (jobInput: Job) => {
                    this._markAsCalled();

                    if (this._shouldAwaitExecution) {
                        await this._execute(jobInput);
                    } else {
                        this._execute(jobInput).catch(logger.error);
                    }
                },
                job
            )
        );

        logger.info(getColoredString(`${this._getLogsSuffix()} - Initialized`, JOBS_STARTUP_COLOR));
    }

    private async _execute(job: Job): Promise<void> {
        try {
            await this.executeJob(job);

            this._handleSucceededExecution(job);
        } catch (err: any) {
            this._handleFailedExecution(err);

            job.fail(JSON.stringify(err, errorReplacer));
            await job.save();

            if (isNotNil(this._retryStrategy)) {
                await this._handleRetryStrategy(err, job);
            }
        }
    }

    private _getLogsSuffix(): string {
        return `[AGENDA_JOB] [${this.agendaJobName}]`;
    }

    private async _getJobMetadata(job: Job): Promise<Partial<AsyncLocalStorageContextType>> {
        const logMetadata = {
            ...(job.attrs?.data?.userId && { user: { id: job.attrs.data.userId.toString() } }),
            ...(job.attrs?.data?.restaurantId && { restaurant: { id: job.attrs.data.restaurantId.toString() } }),
            metadata: {
                agendaJobName: this.agendaJobName,
                jobId: job.attrs?._id,
                ...(job.attrs?.data && { data: job.attrs.data }),
            },
        };

        if (!this._getLogMetadata) {
            return logMetadata;
        }

        try {
            const additionalMetadata = await this._getLogMetadata(job);
            lodash.merge(logMetadata, additionalMetadata ?? {});

            return logMetadata;
        } catch (err) {
            return logMetadata;
        }
    }

    private async _handleRetryStrategy(err: Error, job: Job) {
        const attemptsCount = job.attrs.failCount ?? 0;
        if (this._retryStrategy && attemptsCount < this._retryStrategy.maxAttemptsCount) {
            const minutesToWait = this._retryStrategy.computeRetryDelayInMinutes(attemptsCount);
            const scheduledDate = DateTime.now().plus({ minutes: minutesToWait }).toJSDate();

            logger.info('Retrying job', {
                attemptsCount,
                scheduledDate,
            });

            return job.schedule(scheduledDate);
        }

        logger.error('Job has reached max attempts count', {
            attemptsCount,
        });
        await this._retryStrategy?.executeAfterMaxAttemptsCount(err, job);
    }

    private _markAsCalled() {
        logger.info('Called');
        callCounter.add(1, {
            agendaJobName: this.agendaJobName,
        });
        this._receivedMessageDateTime = DateTime.now();
    }

    private _handleSucceededExecution(job: Job) {
        responseCounter.add(1, {
            agendaJobName: this.agendaJobName,
            responseStatus: JobResponseStatus.SUCCEEDED,
        });
        this._saveProcessingDuration(JobResponseStatus.SUCCEEDED);
        if (this._shouldDeleteJobOnSuccess === true) {
            job.remove().catch((err) => {
                logger.error('Error while removing job', {
                    err: JSON.stringify(err, errorReplacer),
                });
            });
        }

        logger.info('Success');
    }

    private _handleFailedExecution(err: Error) {
        Sentry.captureException(err);

        responseCounter.add(1, {
            agendaJobName: this.agendaJobName,
            responseStatus: JobResponseStatus.FAILED,
        });
        this._saveProcessingDuration(JobResponseStatus.FAILED);

        logger.error('Failed', { err: JSON.stringify(err, errorReplacer) });
    }

    private _saveProcessingDuration(responseStatus: JobResponseStatus) {
        const executionTime = DateTime.now().diff(this._receivedMessageDateTime).milliseconds / 1000;
        processingDuration.record(executionTime, {
            agendaJobName: this.agendaJobName,
            responseStatus,
        });
    }

    private _initRetryStrategy(strategy?: RetryStrategy): Readonly<RetryStrategy> | undefined {
        if (!strategy) {
            logger.warn(`${this._getLogsSuffix()} - Undefined retry strategy`);
            return undefined;
        }
        return {
            maxAttemptsCount: strategy.maxAttemptsCount,
            computeRetryDelayInMinutes: strategy.computeRetryDelayInMinutes ?? defaultComputeRetryDelayInMinutes,
            executeAfterMaxAttemptsCount: strategy.executeAfterMaxAttemptsCount ?? defaultExecuteAfterMaxAttemptsCount,
        };
    }
}
