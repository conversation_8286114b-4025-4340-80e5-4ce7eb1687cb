import { DefineOptions, Job } from 'agenda';
import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { AgendaSingleton } from ':helpers/classes/agenda-singleton';

describe('GenericJobDefinition', () => {
    it('retries after a failure', async () => {
        let jobHandler: (job: Job) => Promise<void> | undefined = undefined!;

        const fakeAgenda = {
            define(name: string, _options: DefineOptions, handler: (job: Job) => Promise<void>) {
                expect(name).toBe('supercalifragilisticexpialidocious');
                jobHandler = handler;
            },
        };

        class FakeAgendaSingleton {
            getInstance() {
                return fakeAgenda;
            }
        }
        const fakeAgendaSingleton = new FakeAgendaSingleton();
        container.registerInstance(AgendaSingleton, fakeAgendaSingleton as any as AgendaSingleton);

        let failureCount = 0;
        let successCount = 0;

        class SupercalifragilisticexpialidociousJob extends GenericJobDefinition {
            constructor() {
                super({
                    agendaJobName: 'supercalifragilisticexpialidocious' as any,
                    retryStrategy: { maxAttemptsCount: 99, computeRetryDelayInMinutes: () => 5, executeAfterMaxAttemptsCount: jest.fn() },
                });
            }

            async executeJob(): Promise<void> {
                if (failureCount <= 2) {
                    failureCount++;
                    throw new Error('dociousaliexpisticfragicalirupus');
                }
                successCount++;
            }
        }

        const supercaliJob = new SupercalifragilisticexpialidociousJob();
        await supercaliJob.init();

        const fakeJob = {
            attrs: {
                get failCount() {
                    return failureCount;
                },
            },
            fail: jest.fn(),
            save: jest.fn(),
            schedule: jest.fn(),
        };

        expect(failureCount).toBe(0);
        const beginDate = DateTime.utc();
        await jobHandler(fakeJob as any);
        const endDate = DateTime.utc();
        {
            const { calls } = fakeJob.fail.mock;
            expect(calls.length).toBe(1);
            expect(calls[0].length).toBe(1);
            const [message] = calls[0];
            expect(JSON.parse(message).stack).toStartWith('Error: dociousaliexpisticfragicalirupus\n');
            expect(JSON.parse(message).message).toBe('dociousaliexpisticfragicalirupus');
        }
        {
            const { calls } = fakeJob.save.mock;
            expect(calls.length).toBe(1);
            expect(calls[0].length).toBe(0);
        }
        {
            const { calls } = fakeJob.schedule.mock;
            expect(calls.length).toBe(1);
            expect(calls[0].length).toBe(1);
            const [date] = calls[0];
            expect(date).toBeInstanceOf(Date);
            expect(+beginDate.plus({ minutes: 5 })).toBeLessThanOrEqual(+date);
            expect(+endDate.plus({ minutes: 5 })).toBeGreaterThanOrEqual(+date);
        }
        expect(failureCount).toBe(1);
        await jobHandler(fakeJob as any);
        expect(failureCount).toBe(2);
        expect(successCount).toBe(0);
        {
            const { calls } = fakeJob.fail.mock;
            expect(calls.length).toBe(2);
        }
        {
            const { calls } = fakeJob.schedule.mock;
            expect(calls.length).toBe(2);
        }
        await jobHandler(fakeJob as any);
        expect(failureCount).toBe(3);
        expect(successCount).toBe(0);
        {
            const { calls } = fakeJob.fail.mock;
            expect(calls.length).toBe(3);
        }
        {
            const { calls } = fakeJob.schedule.mock;
            expect(calls.length).toBe(3);
        }
        await jobHandler(fakeJob as any);
        expect(failureCount).toBe(3);
        expect(successCount).toBe(1);
    });
});
