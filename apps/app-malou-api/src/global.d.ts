import 'jest-extended';

declare global {
    namespace NodeJS {
        interface ProcessEnv {
            NODE_ENV: string;
            PASSPORT_SECRET: string;
            MAILGUN_API_KEY: string;
            MAILGUN_DOMAIN: string;
            MAILGUN_HOST: string;
            APPLE_BUSINESS_CONNECT_API_URL: string;
            APPLE_BUSINESS_CONNECT_API_VERSION: string;
            APPLE_BUSINESS_CONNECT_COMPANY_ID: string;
            APPLE_BUSINESS_CONNECT_CLIENT_ID: string;
            APPLE_BUSINESS_CONNECT_CLIENT_SECRET: string;
            AWS_KEY: string;
            AWS_SECRET: string;
            AWS_REGION: string;
            BASE_URL: string;
            GIT_COMMIT_SHA: string;
            V3_BASE_URL: string;
            ENCRYPT_SECRET: string;
            GMB_REDIRECT_URIS: string;
            GMAPS_API_KEY: string;
            FB_API_VERSION: string;
            FB_REDIRECT_URI: string;
            FB_WEBHOOK_TOKEN: string;
            YELP_CLIENT_ID: string;
            YELP_API_KEY: string;
            FOURSQUARE_CLIENT_ID: string;
            FOURSQUARE_CLIENT_SECRET: string;
            FOURSQUARE_VERSION: string;
            FOURSQUARE_REDIRECT_URI: string;
            KEYWORDS_GENERATOR_AUTHORIZATION: string;
            KEYWORDS_GENERATOR_API_KEY: string;
            NODE_CRAWLER_API_KEY: string;
            FIXED_IP_CALLER_URL: string;
            SLACK_APP_ALERTS_WEBHOOK_URL: string;
            SLACK_ALERTS_WEBHOOK_URL: string;
            ADMIN_APP_EMAIL: string;
            AGENDASH_PASSWORD: string;
            START_SQS_CONSUMER: string;
            DEBUG_MONGO: string;
            PUPPETEER_SERVICE_ARN: string;
            PUPPETEER_SERVICE_AUTHORIZATION: string;
            START_PUBSUB_SUBSCRIPTION: string;
            BN_PROJECT_ID: string;
            BN_TYPE: string;
            BN_PRIVATE_KEY_ID: string;
            BN_PRIVATE_KEY: string;
            BN_CLIENT_EMAIL: string;
            BN_CLIENT_ID: string;
            BN_AUTH_URI: string;
            BN_TOKEN_URI: string;
            BN_AUTH_PROVIDER_X509_CERT_URL: string;
            BN_CLIENT_X509_CERT_URL: string;
            BM_TYPE: string;
            BM_PROJECT_ID: string;
            BM_PRIVATE_KEY_ID: string;
            BM_PRIVATE_KEY: string;
            BM_CLIENT_EMAIL: string;
            BM_CLIENT_ID: string;
            BM_AUTH_URI: string;
            BM_TOKEN_URI: string;
            BM_AUTH_PROVIDER_X509_CERT_URL: string;
            BM_CLIENT_X509_CERT_URL: string;
            BM_PARTNER_NAME: string;
            BM_PARTNER_EMAIL_ADDRESS: string;
            BM_BRAND_CONTACT_NAME: string;
            BM_BRAND_CONTACT_EMAIL_ADDRESS: string;
            BM_BRAND_WEBSITE_URL: string;
            FIREBASE_PROJECT_ID: string;
            FIREBASE_CLIENT_EMAIL: string;
            FIREBASE_PRIVATE_KEY: string;
            SENTRY_API_PROJECT_URI: string;
            SENTRY_AUTH_TOKEN: string;
            UBEREATS_CLIENT_ID: string;
            UBEREATS_CLIENT_SECRET: string;
            UBEREATS_REDIRECT_URI: string;
            MEDIA_CONVERT_ENDPOINT: string;
            I_AM_A: string;
            SCRAPPER_PROXY_TOKEN: string;
            CLOUDINARY_CLOUD_NAME: string;
            CLOUDINARY_API_KEY: string;
            CLOUDINARY_API_SECRET: string;
            MONGODB_URI: string;
            MONGODB_AGENDA_URI: string;
            AWS_BUCKET: string;
            PLATFORMS_SCRAPPER_URL: string;
            PLATFORMS_SCRAPPER_AUTHORIZATION: string;
            SCRAPPER_API_QUEUE_URL: string;
            REVIEWS_QUEUE_URL: string;
            PUBLISH_POST_QUEUE_URL: string;
            GMB_CLIENT_ID: string;
            GMB_CLIENT_SECRET: string;
            FB_APP_ID: string;
            FB_CLIENT_SECRET: string;
            FB_APP_TOKEN: string;
            TIKTOK_CLIENT_ID: string;
            TIKTOK_CLIENT_SECRET: string;
            TIKTOK_REDIRECT_URI: string;
            SEND_EMAIL: string;
            SCRAP_PAGES_JAUNES: string;
            FETCH_RANKINGS: string;
            UPDATE_REVIEWS_CRON_RULE: string;
            FETCH_WEEKLY_GEOSAMPLES: string;
            PUSHER_APP_ID: string;
            PUSHER_KEY: string;
            PUSHER_SECRET: string;
            PUSHER_CLUSTER: string;
            KEYWORDS_GENERATOR_URL: string;
            REVIEW_BOOSTER_SNS_QUEUE_URL: string;
            KEYWORDS_GENERATOR_FUNCTION_NAME: string;
            GOOGLE_REVIEWS_SUBSCRIPTION_NAME: string;
            OPENAI_API_KEY: string;
            THUMBNAIL_GENERATOR_QUEUE_URL: string;
            BASE_API_URL: string;
            WEEKLY_REVIEWS_REPORTS_START_QUEUE_URL: string;
            WEEKLY_REVIEWS_REPORTS_SEND_QUEUE_URL: string;
            WEEKLY_PERFORMANCE_REPORTS_START_QUEUE_URL: string;
            WEEKLY_PERFORMANCE_REPORTS_SEND_QUEUE_URL: string;
            MONTHLY_PERFORMANCE_REPORTS_START_QUEUE_URL: string;
            MONTHLY_PERFORMANCE_REPORTS_SEND_QUEUE_URL: string;
            DAILY_REVIEWS_REPORTS_START_QUEUE_URL: string;
            DAILY_REVIEWS_REPORTS_SEND_QUEUE_URL: string;
            PREVIOUS_REVIEWS_ANALYSIS_FIFO_QUEUE_URL: string;
            KEYWORDS_SCORE_FUNCTION_NAME: string;
            SIMILAR_RESTAURANTS_FUNCTION_NAME: string;
            PLATFORMS_SCRAPPER_FUNCTION_NAME: string;
            REVIEWS_SEMANTIC_ANALYSIS_OVERVIEW_FUNCTION_NAME: string;
            EXPERIMENTATION_APP_URL: string;
            EXPERIMENTATION_APP_CLIENT_KEY: string;
            MALOUPE_GMAPS_API_KEY: string;
            BASE_API_MALOUPE_URL: string;
            MALOUPE_URL: string;
            AI_REVIEWS_FUNCTION_NAME: string;
            AI_SEMANTIC_ANALYSIS_FUNCTION_NAME: string;
            UPDATE_REVIEW_RELEVANT_BRICKS_QUEUE_URL: string;
            BRANCH_NAME: string;
            GITHUB_APP_CLIENT_SECRET: string;
            GITHUB_APP_PRIVATE_KEY: string;
        }
    }
}
export {};
