export enum UseCaseQueueTag {
    CREATE_MESSAGE_QUEUES_DAILY_SAVE_INSIGHTS = 'CREATE_MESSAGE_QUEUES_DAILY_SAVE_INSIGHTS',
    CREATE_MESSAGE_QUEUES_MONTHLY_SAVE_ROI_INSIGHTS = 'CREATE_MESSAGE_QUEUES_MONTHLY_SAVE_ROI_INSIGHTS',
    CREATE_MESSAGES_MONTHLY_UPDATE_SIMILAR_RESTAURANTS = 'CREATE_MESSAGES_MONTHLY_UPDATE_SIMILAR_RESTAURANTS',
    DAILY_REVIEWS_REPORTS_START = 'DAILY_REVIEWS_REPORTS_START',
    DAILY_REVIEWS_REPORTS_SEND = 'DAILY_REVIEWS_REPORTS_SEND',
    DAILY_SAVE_INSIGHTS = 'DAILY_SAVE_INSIGHTS',
    FETCH_KEYWORDS_VOLUME = 'FETCH_KEYWORDS_VOLUME',
    <PERSON><PERSON>YWORDS_FAILED_GENERATION = 'KEYWORDS_FAILED_GENERATION',
    KEYWORDS_GENERATION = 'KEYWORDS_GENERATION',
    MEDIA_CONVERT = 'MEDIA_CONVERT',
    MONTHLY_PERFORMANCE_REPORTS_START = 'MONTHLY_PERFORMANCE_REPORTS_START',
    MONTHLY_PERFORMANCE_REPORTS_SEND = 'MONTHLY_PERFORMANCE_REPORTS_SEND',
    MONTHLY_SAVE_ROI_INSIGHTS = 'MONTHLY_SAVE_ROI_INSIGHTS',
    MONTHLY_UPDATE_SIMILAR_RESTAURANTS = 'MONTHLY_UPDATE_SIMILAR_RESTAURANTS',
    PUBLISH_POST = 'PUBLISH_POST',
    MONTHLY_CHECK_RESTAURANTS_ELIGIBILITY_TO_ACTIVATE_ROI = 'MONTHLY_CHECK_RESTAURANTS_ELIGIBILITY_TO_ACTIVATE_ROI',
    RETRY_EMPTY_TRANSLATIONS = 'RETRY_EMPTY_TRANSLATIONS',
    REVIEW_BOOSTER = 'REVIEW_BOOSTER',
    REVIEWS = 'REVIEWS',
    REVIEWS_CATCH_UP = 'REVIEWS_CATCH_UP',
    RATINGS_CATCH_UP = 'RATINGS_CATCH_UP',
    SCRAPPER = 'SCRAPPER',
    SYNCHRONIZE_RECENT_POSTS = 'SYNCHRONIZE_RECENT_POSTS',
    THUMBNAIL_GENERATION = 'THUMBNAIL_GENERATION',
    WEEKLY_PERFORMANCE_REPORTS_START = 'WEEKLY_PERFORMANCE_REPORTS_START',
    WEEKLY_PERFORMANCE_REPORTS_SEND = 'WEEKLY_PERFORMANCE_REPORTS_SEND',
    WEEKLY_REVIEWS_REPORTS_START = 'WEEKLY_REVIEWS_REPORTS_START',
    WEEKLY_REVIEWS_REPORTS_SEND = 'WEEKLY_REVIEWS_REPORTS_SEND',
    DAILY_NEGATIVE_REVIEWS_NOTIFICATIONS = 'DAILY_NEGATIVE_REVIEWS_NOTIFICATIONS',
    CREATE_NEW_REVIEWS_NOTIFICATION = 'CREATE_NEW_REVIEWS_NOTIFICATION',
    CREATE_POST_ERROR_NOTIFICATION = 'CREATE_POST_ERROR_NOTIFICATION',
    CREATE_COMMENTS_NOTIFICATION = 'CREATE_COMMENTS_NOTIFICATION',
    CREATE_MESSAGE_NOTIFICATION = 'CREATE_MESSAGE_NOTIFICATION',
    CREATE_MENTIONS_NOTIFICATION = 'CREATE_MENTIONS_NOTIFICATION',
    CREATE_PLATFORM_DISCONNECTED_NOTIFICATION = 'CREATE_PLATFORM_DISCONNECTED_NOTIFICATION',
    UPDATE_REVIEW_RELEVANT_BRICKS = 'UPDATE_REVIEW_RELEVANT_BRICKS',
    CREATE_INFO_UPDATE_NOTIFICATION = 'CREATE_INFO_UPDATE_NOTIFICATION',
    FETCH_REVIEW_SEMANTIC_ANALYSIS = 'FETCH_REVIEW_SEMANTIC_ANALYSIS',
    MONTHLY_SAVE_KEYWORD_SEARCH_IMPRESSIONS = 'MONTHLY_SAVE_KEYWORD_SEARCH_IMPRESSIONS',
    CREATE_MESSAGES_MONTHLY_SAVE_KEYWORD_SEARCH_IMPRESSIONS = 'CREATE_MESSAGES_MONTHLY_SAVE_KEYWORD_SEARCH_IMPRESSIONS',
    SCHEDULER_CREATE_MESSAGE_MONTHLY_SAVE_SEARCH_KEYWORD_IMPRESSIONS = 'SCHEDULER_CREATE_MESSAGE_MONTHLY_SAVE_SEARCH_KEYWORD_IMPRESSIONS',
    ACTUALIZE_MERGE_INFORMATION_UPDATES = 'ACTUALIZE_MERGE_INFORMATION_UPDATES',
    PREVIOUS_REVIEWS_ANALYSIS = 'PREVIOUS_REVIEWS_ANALYSIS',
}

export enum SqsType {
    CONSUMER = 'SQS_CONSUMER',
    PRODUCER = 'SQS_PRODUCER',
}
