import { container } from 'tsyringe';

import { Config } from ':config';
import { ActualizeMergeInformationUpdatesConsumer } from ':modules/information-updates/queues/actualize-merge-information-updates/actualize-merge-information-updates.consumer';
import { SchedulerCreateMessageMonthlySaveSearchKeywordImpressionsConsumer } from ':modules/keyword-search-impressions/queues/create-message-queues/scheduler-create-message-monthly-save-search-keyword-impressions.consumer';
import { MonthlySaveKeywordSearchImpressionsConsumer } from ':modules/keyword-search-impressions/queues/monthly-save-keyword-search-impressions/monthly-save-keyword-search-impressions.consumer';
import { CreateMessageQueuesToPerformMonthlySaveKeywordSearchUseCase } from ':modules/keyword-search-impressions/use-cases/create-message-queues-to-perform-monthly-save/create-message-queues-to-perform-monthly-save.use-case';
import { KeywordsFailedGenerationConsumer } from ':modules/keywords/queues/keywords-generation/keywords-generation-error.consumer';
import { KeywordsGenerationConsumer } from ':modules/keywords/queues/keywords-generation/keywords-generation-success.consumer';
import { FetchKeywordsVolumeConsumer } from ':modules/keywords/queues/keywords-volume/receive';
import { AwsMediaConvertVideoProgressConsumer } from ':modules/media/queues/aws-media-convert-video-progress.consumer';
import { ThumbnailGeneratorConsumer } from ':modules/media/queues/thumbnail-generation/thumbnail-generation.consumer';
import { CreateCommentsNotificationConsumer } from ':modules/notifications/queues/create-comment-notification/create-comment-notification.consumer';
import { CreateCommentNotificationProducer } from ':modules/notifications/queues/create-comment-notification/create-comment-notification.producer';
import { CreateInfoUpdateErrorNotificationConsumer } from ':modules/notifications/queues/create-info-update-error-notification/create-info-update-error-notification.consumer';
import { CreateInfoUpdateErrorNotificationProducer } from ':modules/notifications/queues/create-info-update-error-notification/create-info-update-error-notification.producer';
import { CreateMentionsNotificationConsumer } from ':modules/notifications/queues/create-mention-notification/create-mention-notification.consumer';
import { CreateMessageNotificationConsumer } from ':modules/notifications/queues/create-message-notification/create-message-notification.consumer';
import { CreateMessageNotificationProducer } from ':modules/notifications/queues/create-message-notification/create-message-notification.producer';
import { CreateNewReviewsNotificationConsumer } from ':modules/notifications/queues/create-new-reviews-notification/create-new-reviews-notification.consumer';
import { CreateNewReviewsNotificationProducer } from ':modules/notifications/queues/create-new-reviews-notification/create-new-reviews-notification.producer';
import { CreatePlatformDisconnectedNotificationConsumer } from ':modules/notifications/queues/create-platform-disconnected-notification/create-platform-disconnected-notification.consumer';
import { CreatePlatformDisconnectedNotificationProducer } from ':modules/notifications/queues/create-platform-disconnected-notification/create-platform-disconnected-notification.producer';
import { CreatePostErrorNotificationConsumer } from ':modules/notifications/queues/create-post-error-notification/create-post-error-notification.consumer';
import { CreatePostErrorNotificationProducer } from ':modules/notifications/queues/create-post-error-notification/create-post-error-notification.producer';
import { NegativeReviewsNotificationsConsumer } from ':modules/notifications/queues/negative-reviews-notifications/negative-reviews-notifications.consumer';
import { CreateMessageQueuesToPerformDailySaveConsumer } from ':modules/platform-insights/queues/create-message-queues/platform-insights-create-message-queues.consumer';
import { DailySaveInsightsConsumer } from ':modules/platform-insights/queues/daily-save-insights/daily-save-insights.consumer';
import { CreateMessageQueuesToPerformDailySaveInsightsUseCase } from ':modules/platform-insights/use-cases/create-message-queues-to-perform-daily-save-insights/create-message-queues-to-perform-daily-save-insights.use-case';
import { PublishPostsConsumer } from ':modules/posts/queues/publish-post/publish-post.consumer.receive';
import { SynchronizeRecentPostsConsumer } from ':modules/posts/queues/synchronize-recent-posts/synchronize-recent-posts.consumer';
import { MonthlyPerformancePrepareReportsConsumer } from ':modules/reports/queues/performance-reports/monthly/monthly-performance-prepare-reports/monthly-performance-prepare-reports.consumer';
import { MonthlyPerformanceSendReportConsumer } from ':modules/reports/queues/performance-reports/monthly/monthly-performance-send-report/monthly-performance-send-report.consumer';
import { SendMonthlyPerformanceReportsIntoQueue } from ':modules/reports/queues/performance-reports/monthly/monthly-performance-send-report/monthly-performance-send-report.producer';
import { WeeklyPerformancePrepareReportsConsumer } from ':modules/reports/queues/performance-reports/weekly/weekly-performance-prepare-reports/weekly-performance-prepare-reports.consumer';
import { WeeklyPerformanceSendReportConsumer } from ':modules/reports/queues/performance-reports/weekly/weekly-performance-send-report/weekly-performance-send-report.consumer';
import { SendWeeklyPerformanceReportsIntoQueue } from ':modules/reports/queues/performance-reports/weekly/weekly-performance-send-report/weekly-performance-send-report.producer';
import { DailyReviewsPrepareReportsConsumer } from ':modules/reports/queues/reviews-reports/daily/daily-reviews-prepare-reports/daily-reviews-prepare-reports.consumer';
import { DailyReviewsSendReportConsumer } from ':modules/reports/queues/reviews-reports/daily/daily-reviews-send-reports/daily-reviews-send-reports.consumer';
import { SendDailyReviewsReportsIntoQueue } from ':modules/reports/queues/reviews-reports/daily/daily-reviews-send-reports/daily-reviews-send-reports.producer';
import { WeeklyReviewsPrepareReportsConsumer } from ':modules/reports/queues/reviews-reports/weekly/weekly-reviews-prepare-reports/weekly-reviews-prepare-reports.consumer';
import { WeeklyReviewsSendReportConsumer } from ':modules/reports/queues/reviews-reports/weekly/weekly-reviews-send-reports/weekly-reviews-send-reports.consumer';
import { SendWeeklyReviewsReportsIntoQueue } from ':modules/reports/queues/reviews-reports/weekly/weekly-reviews-send-reports/weekly-reviews-send-reports.producer';
import { PreviousReviewsAnalysisConsumer } from ':modules/reviews/queues/previous-reviews-analysis/previous-reviews-analysis.consumer';
import { PreviousReviewsAnalysisProducer } from ':modules/reviews/queues/previous-reviews-analysis/previous-reviews-analysis.producer';
import { RatingsCatchUpConsumer } from ':modules/reviews/queues/ratings-catch-up/ratings-catch-up.consumer';
import { ReviewBoosterSnsConsumer } from ':modules/reviews/queues/review-booster/review-booster.consumer';
import { ReviewsCatchUpConsumer } from ':modules/reviews/queues/reviews-catch-up/reviews-catch-up.consumer';
import { ScrapperConsumer } from ':modules/reviews/queues/reviews-scrapper/reviews-scrapper.consumer';
import { ReviewsConsumer } from ':modules/reviews/queues/update-reviews/update-reviews.consumer';
import { CreateMessageQueuesToPerformMonthlyRoiSaveInsightsConsumer } from ':modules/roi-insights/queues/create-message-queues/create-message-queues.producer';
import { MonthlySaveRoiInsightsConsumer } from ':modules/roi-insights/queues/monthly-save-roi-insights/monthly-save-roi-insights.consumer';
import { CreateMessageQueuesToPerformMonthlySaveRoiInsightsUseCase } from ':modules/roi-insights/use-cases/create-message-queues-to-perform-monthly-save-roi-insights/create-message-queues-to-perform-monthly-save-roi-insights.use-case';
import { MonthlyCheckRestaurantsEligibilityToActivateRoiConsumer } from ':modules/roi/queues/monthly-check-roi-eligibility-for-restaurants/monthly-check-roi-eligibility-for-restaurants.consumer';
import { ReviewSemanticAnalysisConsumer } from ':modules/segment-analyses/queues/review-semantic-analysis/review-semantic-analysis.consumer';
import { ReviewSemanticAnalysisProducer } from ':modules/segment-analyses/queues/review-semantic-analysis/review-semantic-analysis.producer';
import { SendMonthlyUpdateSimilarRestaurantMessage } from ':modules/similar-restaurants/queues/create-messages/send-monthly-update-similar-restaurant.producer';
import { CreateMessagesToPerformMonthlyUpdateSimilarRestaurantsConsumer } from ':modules/similar-restaurants/queues/create-messages/similar-restaurants-create-messages.consumer';
import { MonthlyUpdateSimilarRestaurantsConsumer } from ':modules/similar-restaurants/queues/monthly-update-similar-restaurants/monthly-update-similar-restaurants.consumer';
import { RetryEmptyTranslationsConsumer } from ':modules/translations/queues/retry-empty-breakdowns-translations/retry-empty-breakdowns-translations.consumer';

const { startSqsConsumer } = Config.services.sqs;

export const initQueueMessageConsumers = () => {
    if (startSqsConsumer && process.env.NODE_ENV !== 'tests') {
        container.resolve(AwsMediaConvertVideoProgressConsumer).initialize();
        container.resolve(RetryEmptyTranslationsConsumer).initialize();
        container.resolve(KeywordsGenerationConsumer).initialize();
        container.resolve(KeywordsFailedGenerationConsumer).initialize();
        container.resolve(FetchKeywordsVolumeConsumer).initialize();
        container.resolve(ReviewsConsumer).initialize();
        container.resolve(PublishPostsConsumer).initialize();
        container.resolve(ScrapperConsumer).initialize();
        container.resolve(ReviewsCatchUpConsumer).initialize();
        container.resolve(RatingsCatchUpConsumer).initialize();
        container.resolve(ReviewBoosterSnsConsumer).initialize();
        container.resolve(ThumbnailGeneratorConsumer).initialize();
        container.resolve(CreateMessageQueuesToPerformDailySaveConsumer).initialize();
        container.resolve(DailySaveInsightsConsumer).initialize();
        container.resolve(CreateMessageQueuesToPerformMonthlyRoiSaveInsightsConsumer).initialize();
        container.resolve(MonthlySaveRoiInsightsConsumer).initialize();
        container.resolve(CreateMessagesToPerformMonthlyUpdateSimilarRestaurantsConsumer).initialize();
        container.resolve(MonthlyUpdateSimilarRestaurantsConsumer).initialize();
        container.resolve(MonthlyCheckRestaurantsEligibilityToActivateRoiConsumer).initialize();
        container.resolve(NegativeReviewsNotificationsConsumer).initialize();
        container.resolve(CreateCommentsNotificationConsumer).initialize();
        container.resolve(CreatePostErrorNotificationConsumer).initialize();
        container.resolve(CreateMessageNotificationConsumer).initialize();
        container.resolve(CreateMentionsNotificationConsumer).initialize();
        container.resolve(CreatePlatformDisconnectedNotificationConsumer).initialize();
        container.resolve(ActualizeMergeInformationUpdatesConsumer).initialize();
        container.resolve(SynchronizeRecentPostsConsumer).initialize();
        container.resolve(CreateInfoUpdateErrorNotificationConsumer).initialize();
        container.resolve(ReviewSemanticAnalysisConsumer).initialize();
        container.resolve(SchedulerCreateMessageMonthlySaveSearchKeywordImpressionsConsumer).initialize();
        container.resolve(MonthlySaveKeywordSearchImpressionsConsumer).initialize();
        container.resolve(PreviousReviewsAnalysisConsumer).initialize();

        // Reports
        container.resolve(MonthlyPerformancePrepareReportsConsumer).initialize();
        container.resolve(MonthlyPerformanceSendReportConsumer).initialize();
        container.resolve(WeeklyPerformancePrepareReportsConsumer).initialize();
        container.resolve(WeeklyPerformanceSendReportConsumer).initialize();
        container.resolve(DailyReviewsPrepareReportsConsumer).initialize();
        container.resolve(DailyReviewsSendReportConsumer).initialize();
        container.resolve(WeeklyReviewsPrepareReportsConsumer).initialize();
        container.resolve(WeeklyReviewsSendReportConsumer).initialize();
        container.resolve(CreateNewReviewsNotificationConsumer).initialize();
    }
};

export const initQueueMessageProducers = () => {
    container.resolve(SendMonthlyUpdateSimilarRestaurantMessage).initialize();
    container.resolve(CreateMessageQueuesToPerformDailySaveInsightsUseCase).initialize();
    container.resolve(CreateMessageQueuesToPerformMonthlySaveRoiInsightsUseCase).initialize();
    container.resolve(SendMonthlyPerformanceReportsIntoQueue).initialize();
    container.resolve(SendWeeklyReviewsReportsIntoQueue).initialize();
    container.resolve(SendWeeklyPerformanceReportsIntoQueue).initialize();
    container.resolve(SendDailyReviewsReportsIntoQueue).initialize();
    container.resolve(CreateNewReviewsNotificationProducer).initialize();
    container.resolve(CreateCommentNotificationProducer).initialize();
    container.resolve(CreatePostErrorNotificationProducer).initialize();
    container.resolve(CreateMessageNotificationProducer).initialize();
    container.resolve(CreatePlatformDisconnectedNotificationProducer).initialize();
    container.resolve(CreateInfoUpdateErrorNotificationProducer).initialize();
    container.resolve(ReviewSemanticAnalysisProducer).initialize();
    container.resolve(CreateMessageQueuesToPerformMonthlySaveKeywordSearchUseCase).initialize();
    container.resolve(PreviousReviewsAnalysisProducer).initialize();
};
