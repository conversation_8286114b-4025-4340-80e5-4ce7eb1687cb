import * as lodash from 'lodash';
import { DateTime } from 'luxon';

import { toDbIds } from '@malou-io/package-models';
import {
    createDate,
    DELIVEROO_DAYS_UNTIL_CANT_BE_ANSWERED,
    MalouErrorCode,
    PlatformKey,
    PlatformPresenceStatus,
    PostedStatus,
    PrivatePlatforms,
    UBEREATS_DAYS_UNTIL_CANT_BE_ANSWERED,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { toDiacriticInsensitiveRegexString } from ':helpers/utils';
import { ReviewFiltersInput } from ':modules/reviews/reviews.interfaces';

/** Should match the collection targeted by AdvancedReviewFilters (`reviews` or `privatereviews`) */
export enum ReviewFiltersMode {
    PRIVATE_REVIEWS,
    PUBLIC_REVIEWS,
}

export class AdvancedReviewFilters {
    answerable: boolean;
    answered: boolean;
    archived: boolean;
    endDate?: Date;
    notAnswered: boolean;
    pending: boolean;
    platforms?: string[];
    privatePlatforms: PrivatePlatforms[];
    /** Zero is a special value and matches reviews without rating. */
    ratings?: (0 | 1 | 2 | 3 | 4 | 5)[];
    restaurantIds: string[];
    searchText?: string;
    startDate?: Date;
    text?: string;
    unarchived: boolean;
    withoutText: boolean;
    withText: boolean;

    /**
     * Important: `mode` must be ReviewFiltersMode.PRIVATE_REVIEWS if this object is used
     * to filter privatereviews, and ReviewFiltersMode.PUBLIC_REVIEWS for “public” reviews.
     */
    constructor(
        data: ReviewFiltersInput,
        private readonly mode: ReviewFiltersMode
    ) {
        this.answerable = !!data.answerable;
        this.answered = !!data.answered;
        this.archived = !!data.archived;
        this.startDate = data.startDate ? (createDate(data.startDate) ?? undefined) : undefined;
        this.endDate = data.endDate ? (createDate(data.endDate) ?? undefined) : undefined;
        this.notAnswered = !!data.notAnswered;
        this.pending = !!data.pending;
        this.platforms = data.platforms ?? undefined;
        this.privatePlatforms = data.privatePlatforms ?? [PrivatePlatforms.CAMPAIGN, PrivatePlatforms.TOTEM];
        this.ratings = data.ratings ?? undefined;
        this.restaurantIds = data.restaurantIds ?? [];
        this.searchText = data.searchText ?? undefined;
        this.text = data.text ?? undefined;
        this.unarchived = data.unarchived !== false;
        this.withoutText = !!data.withoutText;
        this.withText = !!data.withText;

        if (this.startDate && this.endDate) {
            if (+this.startDate > +this.endDate) {
                throw new MalouError(MalouErrorCode.FILTER_START_DATE_AFTER_END_DATE);
            }
        }
    }

    _buildQueryDate(): any {
        return {
            $ne: null,
            ...(this.startDate && { $gte: this.startDate }),
            ...(this.endDate && { $lte: this.endDate }),
        };
    }

    /**
     * Returns a MongoDB filter object.
     */
    buildQuery(): { $and: any[] } {
        let $and: any[] = [];

        $and.push({
            platformPresenceStatus: { $ne: PlatformPresenceStatus.NOT_FOUND },
        });

        if (this.restaurantIds) {
            $and.push({
                restaurantId: {
                    $in: toDbIds(this.restaurantIds),
                },
            });
        }

        if (this.startDate || this.endDate) {
            $and.push({ socialSortDate: this._buildQueryDate() });
        }

        if (this.mode === ReviewFiltersMode.PRIVATE_REVIEWS) {
            $and.push({
                $or: [
                    { scanId: { $exists: this.privatePlatforms.includes(PrivatePlatforms.TOTEM) } },
                    { campaignId: { $exists: this.privatePlatforms.includes(PrivatePlatforms.CAMPAIGN) } },
                ],
            });
        } else if (this.platforms && Array.isArray(this.platforms) && this.platforms?.[0] !== 'all') {
            $and.push({
                key: {
                    $in: this.platforms,
                },
            });
        }

        if (this.text) {
            $and.push({
                text: {
                    $regex: toDiacriticInsensitiveRegexString(lodash.escapeRegExp(this.text)),
                    $options: 'i',
                    $exists: true,
                    $ne: null,
                },
            });
        }

        if (!this.ratings || lodash.isEqual(lodash.sortBy(this.ratings), [0, 1, 2, 3, 4, 5])) {
            // All the rating values are selected. As an optimization we omit the
            // `rating: {$in: […]}` filter.
            // XXX We have some old reviews that have a rating set to a floating point
            // number like 4.75… These will only appear with all the rating options
            // selected. I think we should run a migration script someday to “normalize”
            // the `rating` field.
        } else {
            $and.push({
                rating: {
                    $in: lodash.uniq(
                        // zero is a special value and means “no rating”
                        this.ratings.map((r) => (r === 0 ? null : r))
                    ),
                },
            });
        }

        switch (`${this.archived} | ${this.unarchived}`) {
            case 'true | false':
                $and.push({ archived: { $eq: true } });
                break;
            case 'false | true':
                $and.push({ archived: { $eq: false } });
                break;
            default:
                break;
        }
        switch (`${this.answered} | ${this.notAnswered} | ${this.pending}`) {
            case 'true | true | true':
                $and.push({ comments: { $exists: true } });
                break;
            case 'false | false | false':
                $and.push({ comments: { $exists: false } });
                break;
            case 'false | true | false':
                $and.push({ comments: { $in: [[], null] } });
                break;
            case 'true | false | false':
                $and.push({ 'comments.0.posted': PostedStatus.POSTED });
                break;
            case 'false | false | true':
                $and.push({
                    'comments.0.posted': { $in: [PostedStatus.PENDING, PostedStatus.RETRY] },
                });
                break;
            case 'true | false | true':
                $and.push({ comments: { $nin: [[], null] } });
                break;
            case 'true | true | false':
                $and.push({
                    $or: [{ 'comments.0.posted': PostedStatus.POSTED }, { comments: { $in: [[], null] } }],
                });
                break;
            case 'false | true | true':
                $and.push({
                    $or: [{ 'comments.0.posted': { $in: [PostedStatus.PENDING, PostedStatus.RETRY] } }, { comments: { $in: [[], null] } }],
                });
                break;
            default:
                break;
        }
        switch (`${this.withText} | ${this.withoutText}`) {
            case 'true | true':
            case 'false | false':
                break;
            case 'true | false':
                $and.push({
                    $or: [
                        { text: { $ne: null } },
                        { ratingTags: { $exists: true, $not: { $size: 0 } } },
                        {
                            menuItemReviews: {
                                $exists: true,
                                $not: { $size: 0 },
                            },
                        },
                    ],
                });
                break;
            case 'false | true':
                $and.push({
                    $or: [
                        {
                            $and: [{ text: null }, { ratingTags: { $exists: false } }, { menuItemReviews: { $exists: false } }],
                        },
                        {
                            $and: [{ text: null }, { ratingTags: { $size: 0 } }, { menuItemReviews: { $size: 0 } }],
                        },
                    ],
                });
                break;
            default:
                break;
        }
        if (this.answerable) {
            $and.push({
                $or: [
                    {
                        key: PlatformKey.LAFOURCHETTE,
                        text: { $ne: null },
                    },
                    {
                        key: PlatformKey.DELIVEROO,
                        socialCreatedAt: {
                            $gt: DateTime.now().minus({ days: DELIVEROO_DAYS_UNTIL_CANT_BE_ANSWERED }).toJSDate(),
                        },
                        text: { $ne: null },
                    },
                    {
                        key: PlatformKey.UBEREATS,
                        socialCreatedAt: {
                            $gt: DateTime.now().minus({ days: UBEREATS_DAYS_UNTIL_CANT_BE_ANSWERED }).toJSDate(),
                        },
                    },
                    // TODO: handle DoorDash answer reply if we manage to get something else than a 403
                    // {
                    //     key: PlatformKey.DOORDASH,
                    //     socialCreatedAt: {
                    //         $gt: DateTime.now().minus({ days: DOORDASH_DAYS_UNTIL_CANT_BE_ANSWERED }).toJSDate(),
                    //     },
                    // },
                    {
                        key: {
                            $nin: [
                                PlatformKey.LAFOURCHETTE,
                                PlatformKey.FOURSQUARE,
                                PlatformKey.DELIVEROO,
                                PlatformKey.UBEREATS,
                                // PlatformKey.DOORDASH,
                            ],
                        },
                    },
                ],
            });
        }

        // return only no answerable reviews
        if (!this.answerable && !this.pending && !this.notAnswered && !this.answered) {
            $and = $and.filter((field) => !['comments'].includes(Object.keys(field)[0]));
            $and.push({
                $or: [
                    {
                        key: PlatformKey.LAFOURCHETTE,
                        text: null,
                    },
                    {
                        key: PlatformKey.DELIVEROO,
                        socialCreatedAt: {
                            $lte: DateTime.now().minus({ days: DELIVEROO_DAYS_UNTIL_CANT_BE_ANSWERED }).toJSDate(),
                        },
                    },
                    {
                        key: PlatformKey.UBEREATS,
                        socialCreatedAt: {
                            $lte: DateTime.now().minus({ days: UBEREATS_DAYS_UNTIL_CANT_BE_ANSWERED }).toJSDate(),
                        },
                    },
                    // TODO: handle DoorDash answer reply if we manage to get something else than a 403
                    // {
                    //     key: PlatformKey.DOORDASH,
                    //     socialCreatedAt: {
                    //         $lte: DateTime.now().minus({ days: DOORDASH_DAYS_UNTIL_CANT_BE_ANSWERED }).toJSDate(),
                    //     },
                    // },
                ],
            });
        }

        if (this.searchText) {
            $and.push({
                $or: [
                    { text: { $regex: this.searchText, $options: 'i', $exists: true, $ne: null } },
                    ...// we don’t have any information about the reviewer for private reviews
                    (this.mode === ReviewFiltersMode.PRIVATE_REVIEWS
                        ? []
                        : [
                              {
                                  'reviewer.displayName': {
                                      $regex: this.searchText,
                                      $options: 'i',
                                      $exists: true,
                                      $ne: null,
                                  },
                              },
                          ]),
                    {
                        comments: {
                            $elemMatch: {
                                text: { $regex: this.searchText, $options: 'i', $exists: true, $ne: null },
                            },
                        },
                    },
                ],
            });
        }
        return { $and };
    }
}
