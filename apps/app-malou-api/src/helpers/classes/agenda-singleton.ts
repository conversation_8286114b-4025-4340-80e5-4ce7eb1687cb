import { Agenda, Job, JobAttributesData } from 'agenda';
import { singleton } from 'tsyringe';

import { isNotNil, MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { AgendaJobData } from ':helpers/validators/jobs/job-data.types';

@singleton()
export class AgendaSingleton {
    private static _instance: Agenda | undefined;

    static isInitiated(): boolean {
        return isNotNil(AgendaSingleton._instance);
    }

    public async getInstance(): Promise<Agenda> {
        if (!AgendaSingleton._instance) {
            AgendaSingleton._instance = await this.init();
        }
        return AgendaSingleton._instance;
    }

    public async now<T extends AgendaJobName>(name: T, data: AgendaJobData[T]): Promise<Job<AgendaJobData[T]>> {
        return this.getInstance().then((agenda) => agenda.now(name, data));
    }

    public async schedule<T extends AgendaJobName>(date: Date, name: T, data: AgendaJobData[T]): Promise<Job<AgendaJobData[T]>> {
        return this.getInstance().then((agenda) => agenda.schedule(date, name, data));
    }

    public async jobs<T extends AgendaJobName>(
        query: { name?: T } & JobAttributesData,
        sort?: {},
        limit?: number,
        skip?: number
    ): Promise<Job<AgendaJobData[T]>[]> {
        return this.getInstance().then((agenda) => agenda.jobs(query, sort, limit, skip, true));
    }

    public async deleteJobs(query: {}): Promise<number | undefined> {
        return this.getInstance().then((agenda) => agenda.cancel(query));
    }

    public async closeConnection(): Promise<void> {
        if (!AgendaSingleton.isInitiated()) {
            return;
        }
        await this.getInstance().then((agenda) => agenda.close());
        AgendaSingleton._instance = undefined;
    }

    private async init(): Promise<Agenda> {
        return new Promise((resolve, reject) => {
            const connectionOpts = { db: { address: process.env.MONGODB_AGENDA_URI, collection: 'agendaJobs' } };
            const agenda = new Agenda(connectionOpts);
            agenda.on('ready', async function () {
                resolve(agenda);
            });
            agenda.on('error', async function (err) {
                reject(new MalouError(MalouErrorCode.AGENDA_DATABASE_CONNECTION_FAILED, { message: err.message }));
            });
        });
    }
}
