import { singleton } from 'tsyringe';

import {
    AiInteractionRelatedEntityCollection,
    GenerateStoreLocatorContentType,
    MalouErrorCode,
    StoreLocatorLanguage,
} from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { GenericAiService, GenericAiServiceResponseType } from ':microservices/ai-lambda-template/generic-ai-service';

export interface GenerateStoreLocatorContentPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.STORE_LOCATOR_RESTAURANT_PAGE;
    type: GenerateStoreLocatorContentType;
    restaurantData: {
        restaurantName: string;
        organizationName: string;
        address: {
            locality?: string;
            postalCode?: string;
        };
        language: StoreLocatorLanguage;
        keywords: string[];
        brandTone?: string[];
        targetAudience?: string[];
        specificsDirectives?: string[];
        restaurantOffers?: string[];
        restaurantContext?: string[];
        context?: Partial<Record<GenerateStoreLocatorContentType, string>>[];
        previousGeneration?: string;
    };
}

export interface AiStoreLocatorContent {
    text: string;
}

export interface AiStoreLocatorDescriptionsContent {
    blocks: {
        title: string;
        sections: {
            subtitle: string;
            text: string;
        }[];
    }[];
}

export type AiStoreLocatorContentType<T> = T extends GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_GENERATION
    ? AiStoreLocatorDescriptionsContent
    : AiStoreLocatorContent;

@singleton()
export class AiStoreLocatorContentService {
    private readonly _MAX_RETRIES_COUNT = 3;

    async generateStoreLocatorContent<T extends GenerateStoreLocatorContentType>(
        type: T,
        payload: GenerateStoreLocatorContentPayload['restaurantData'],
        retryCount?: number
    ): Promise<GenericAiServiceResponseType<AiStoreLocatorContentType<T>>> {
        try {
            const AiService = new GenericAiService<GenerateStoreLocatorContentPayload, AiStoreLocatorContentType<T>>({
                lambdaUrl: Config.services.aiStoreLocatorContentGenerationService.functionName,
            });
            return await AiService.generateCompletion({
                restaurantData: payload,
                relatedEntityCollection: AiInteractionRelatedEntityCollection.STORE_LOCATOR_RESTAURANT_PAGE,
                type,
            });
        } catch (error) {
            const retriesCount = retryCount ?? 0;
            const shouldRetry =
                retriesCount < this._MAX_RETRIES_COUNT &&
                error instanceof MalouError &&
                error.malouErrorCode === MalouErrorCode.AI_REQUEST_FAILED &&
                // todo store-locator check with Aymen a better retry mechanism
                error.metadata?.errorMessage?.includes('validation error');

            if (shouldRetry) {
                return await this.generateStoreLocatorContent(type, payload, retriesCount + 1);
            }

            throw error;
        }
    }
}
