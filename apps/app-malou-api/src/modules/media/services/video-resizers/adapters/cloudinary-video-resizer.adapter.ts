import axios from 'axios';
import { VideoTransformationOptions } from 'cloudinary';
import fs from 'fs';
import { singleton } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';

import { FileFormat, MalouErrorCode, MediaType } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { getMimetypeFromExtension } from ':helpers/utils';
import { CropMode, FileMetadata, VideoResizerCropOptions } from ':modules/media/services/image-resizers/image-resizer.port.interface';
import { VideoResizerPort } from ':modules/media/services/video-resizers/video-resizer.port.interface';
import { Cloudinary } from ':plugins/cloudinary';

@singleton()
export class CloudinaryVideoResizerAdapter implements VideoResizerPort {
    constructor(private readonly _cloudinary: Cloudinary) {}
    async upload(filePath: string, outputPath: string, options: VideoResizerCropOptions): Promise<FileMetadata> {
        logger.info('[DEBUG][upload] start', { filePath, outputPath, options });
        const public_id = uuidv4();
        logger.info('[DEBUG][upload] public_id', { public_id });
        const transformation: Array<VideoTransformationOptions> = [
            {
                ...options,
                gravity: 'center',
                crop: options.cropMode,
            },
            {
                bit_rate: '20m',
                video_codec: 'h264',
                audio_codec: 'aac',
                audio_frequency: 128_000,
            },
        ];

        /*
            Explanation:
            When we crop a media from a different aspect ratio, we first have to scale it to the maxDimension width and height (of the target platform) of the original media before cropping it.

            for example if we have
            originalMedia: { aspectRatio: 9/16, width: 2160, height: 3840, maxDimension: { width: 1080, height: 1920 } } the maximum dimension of the target platform is 1080x1920
            let's say our target resize is { width: 1080, height: 1350, aspectRatio: 4/5 }

            if we crop directly on the original media it will be zoomed because we are cropping a small part of the media with a different aspect ratio

            so we first scale it down to the maxDimension width and height of the original media before cropping it
        */
        const maxWidth = options.originalMedia?.maxDimension.width ?? 0;
        const maxHeight = options.originalMedia?.maxDimension.height ?? 0;
        if (
            options.cropMode === CropMode.CROP &&
            ((options.originalMedia?.width ?? 0) > maxWidth || (options.originalMedia?.height ?? 0) > maxHeight)
        ) {
            transformation.unshift({
                width: maxWidth,
                height: maxHeight,
                crop: 'scale',
            });
        }

        const result = await this._cloudinary.upload(filePath, {
            public_id,
            resource_type: 'video',
            format: FileFormat.MP4,
            transformation,
            timeout: 240_000,
        });
        if (!result) {
            throw new MalouError(MalouErrorCode.FAILED_VIDEO_RESIZE, {
                message: 'Cloudinary video upload failed',
                metadata: { filePath, outputPath, options },
            });
        }
        const pathWhereFileIsStored = await this._downloadMedia(result.url, outputPath);
        // we delete it from cloudinary as we do not need to store it permanently there
        this._cloudinary
            .delete(result.public_id)
            .then((res) => logger.info('[CLOUDINARY] delete media success : ' + JSON.stringify(res)))
            .catch((err) => logger.error('[CLOUDINARY] delete media error : ' + JSON.stringify(err)));
        if (!filePath.includes('http')) {
            fs.unlink(filePath, (err) => {
                if (err) {
                    logger.error(err.message);
                }
            });
        }
        return {
            format: result.format,
            height: result.height,
            width: result.width,
            size: result.bytes,
            url: result.url,
            name: result.original_filename,
            pathWhereFileIsStored,
            mimetype: getMimetypeFromExtension(result.format),
            type: MediaType.VIDEO,
        };
    }

    private async _downloadMedia(url: string, outputPath: string): Promise<string> {
        const response = await axios.get(url, { responseType: 'stream' });

        const writer = fs.createWriteStream(outputPath);

        response.data.pipe(writer);

        return new Promise((resolve, reject) => {
            writer.on('finish', () => {
                resolve(outputPath);
            });
            writer.on('error', (err: any) => {
                reject(err);
            });
        });
    }
}
