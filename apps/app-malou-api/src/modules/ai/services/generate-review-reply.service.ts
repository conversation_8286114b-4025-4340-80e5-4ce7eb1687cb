import { singleton } from 'tsyringe';

import { IReview } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

@singleton()
export class GenerateReviewReplyService {
    getReviewText(review: IReview): string | null {
        if (review.key !== PlatformKey.UBEREATS) {
            return review.text ?? '';
        }
        const ratingTags = review.ratingTags;
        const menuItemReviews = review.menuItemReviews;
        const text = review.text;
        const ratingTagsText = ratingTags?.length ? `General: ${ratingTags.join(', ')}` : null;
        const menuItemReviewsText = menuItemReviews?.length
            ? `${menuItemReviews
                  .map((menuItemReview) => (menuItemReview.rating ? 'Like: ' : 'Dislike: ') + menuItemReview.name)
                  .join('. ')}`
            : null;
        return [text, ratingTagsText, menuItemReviewsText].filter(Boolean).join('. ');
    }
}
