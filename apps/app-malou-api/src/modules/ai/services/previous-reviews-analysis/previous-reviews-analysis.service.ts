import { sampleSize } from 'lodash';
import { singleton } from 'tsyringe';

import { IReview, ReadPreferenceMode, toDbId } from '@malou-io/package-models';
import {
    AI_HARD_LIMIT_CALL_COUNT,
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    getPlatformDefinitions,
    isNotNil,
    MalouErrorCode,
    PlatformCategory,
    PlatformKey,
    PostedStatus,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { isDeliveryPlatform } from ':helpers/utils';
import { GenericAiServiceCompletionResponse } from ':microservices/ai-lambda-template/generic-ai-service';
import {
    AiPreviousReviewsAnalysisService,
    DEFAULT_RESPONSE_STYLE_VALUE,
    DEFAULT_REVIEWER_NAME_VALIDATION_VALUE,
    PreviousReviewsAnalysisAndReviewerNameValidation,
} from ':microservices/ai-previous-review-analysis.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { assertRestaurantCanMakeAiCall } from ':modules/ai/helpers';
import { PreviousReviewsAnalysisPayload } from ':modules/ai/interfaces/ai.interfaces';
import { AiCompletionMapper } from ':modules/ai/mappers/ai-completion.mapper';
import { GenerateReviewReplyService } from ':modules/ai/services/generate-review-reply.service';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';

@singleton()
export class PreviousReviewsAnalysisService {
    readonly MINIMAL_REVIEWS_FOUND_WITH_LANG_SEARCH = 2;

    constructor(
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _aiPreviousReviewsAnalysisService: AiPreviousReviewsAnalysisService,
        private readonly _generateReviewReplyService: GenerateReviewReplyService,
        private readonly _aiCompletionMapper: AiCompletionMapper
    ) {}

    async analyzePreviousReviews({
        review,
        restaurantId,
        previousReviews,
    }: {
        review: IReview;
        restaurantId: string;
        previousReviews: { id: string; reviewText: string | null; responseText: string }[];
    }): Promise<PreviousReviewsAnalysisAndReviewerNameValidation> {
        if (review.responseStyle && review.matchedReviewsIds && review.reviewerNameValidation) {
            return this._mapToLambdaPreviousReviewsAnalysisAndReviewerNameValidation({
                responseStyle: review.responseStyle,
                matchedReviewsIds: review.matchedReviewsIds.map((id) => id.toString()),
                reviewerNameValidation: review.reviewerNameValidation,
            });
        }
        try {
            const restaurant = await this._restaurantsRepository.getRestaurantById(restaurantId);

            if (!restaurant) {
                logger.warn('[AI_USE_CASE] Restaurant not found', { restaurantId });
                throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, { message: 'Restaurant not found', metadata: { restaurantId } });
            }

            assertRestaurantCanMakeAiCall(restaurant, AI_HARD_LIMIT_CALL_COUNT);

            const payload = this._computePayload(review, previousReviews);

            const { aiResponse, aiInteractionDetails } =
                await this._aiPreviousReviewsAnalysisService.generatePreviousReviewsAnalysis(payload);

            await this._restaurantsRepository.incrementRestaurantAiCallCount({
                restaurantId: restaurant._id,
                feature: AiInteractionType.GENERATE_SEO_DUPLICATION,
            });
            if (isNotNil(aiInteractionDetails)) {
                await this._saveInteractions({
                    restaurantId: restaurant._id.toString(),
                    aiInteractions: aiInteractionDetails,
                    reviewId: review._id.toString(),
                });
            }

            const dbPreviousReviewsAnalysisAndReviewerNameValidation =
                this._mapToDbPreviousReviewsAnalysisAndReviewerNameValidation(aiResponse);
            await this._reviewsRepository.updateOne({
                filter: { _id: review._id },
                update: {
                    matchedReviewsIds: dbPreviousReviewsAnalysisAndReviewerNameValidation.matchedReviewsIds,
                    responseStyle: dbPreviousReviewsAnalysisAndReviewerNameValidation.responseStyle,
                    reviewerNameValidation: dbPreviousReviewsAnalysisAndReviewerNameValidation.reviewerNameValidation,
                },
            });

            return this._mapToLambdaPreviousReviewsAnalysisAndReviewerNameValidation(aiResponse);
        } catch (error: any) {
            logger.error('[AiUseCases] [analyzePreviousReviews] Error', {
                error: error.stack,
                restaurantId,
            });
            throw error;
        }
    }

    async getReviewCommentsSample(
        review: IReview,
        size: number,
        lang?: string
    ): Promise<{ id: string; reviewText: string | null; responseText: string }[]> {
        const searchQuery = this._getAnswerReviewSearchQuery(review, lang);

        if (review.restaurantId) {
            const restaurant = await this._restaurantsRepository.getRestaurantById(review.restaurantId.toString());

            if (restaurant?.minimalReviewsDateForReviewReplyAutomation) {
                searchQuery.socialCreatedAt = { $gte: restaurant.minimalReviewsDateForReviewReplyAutomation };
            }
        }

        const previousReviews = await this._reviewsRepository.find({
            filter: searchQuery,
            projection: { _id: 1, text: 1, comments: 1 },
            options: { lean: true, sort: { socialCreatedAt: -1 }, limit: size, readPreference: ReadPreferenceMode.SECONDARY },
        });

        if (previousReviews.length < this.MINIMAL_REVIEWS_FOUND_WITH_LANG_SEARCH && lang) {
            return this.getReviewCommentsSample(review, size, undefined);
        }

        const previousReviewsSample = sampleSize(previousReviews, size);
        return previousReviewsSample.map((previousReview) => {
            const postedComment = previousReview.comments.find((comment) => comment.posted === PostedStatus.POSTED)!;
            return {
                id: previousReview._id.toString(),
                reviewText: previousReview.text ?? null,
                responseText: postedComment.text,
            };
        });
    }

    private _computePayload(
        review: IReview,
        previousReviews: { id: string; reviewText: string | null; responseText: string }[]
    ): PreviousReviewsAnalysisPayload {
        return {
            relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
            type: AiInteractionType.PREVIOUS_REVIEWS_ANALYSIS_AND_REVIEWER_NAME_VALIDATION,
            restaurantData: {
                previousReviewsComments: previousReviews.reduce(
                    (acc, previousReview) => {
                        acc[previousReview.id] = {
                            reviewText: previousReview.reviewText ?? '',
                            responseText: previousReview.responseText,
                        };
                        return acc;
                    },
                    {} as Record<string, { reviewText: string; responseText: string }>
                ),
                reviewText: review.text ?? '',
                reviewerName: review.reviewer?.displayName ?? '',
            },
        };
    }

    private async _saveInteractions({
        restaurantId,
        aiInteractions,
        reviewId,
    }: {
        restaurantId: string;
        aiInteractions: GenericAiServiceCompletionResponse[];
        reviewId: string;
    }): Promise<void> {
        if (!aiInteractions || !aiInteractions.length) {
            return;
        }
        const aiInteractionsToSave = aiInteractions.map((aiInteraction) => ({
            ...this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(aiInteraction, toDbId(restaurantId)),
            restaurantId,
            userId: undefined,
            relatedEntityId: reviewId,
            relatedEntityCollection: aiInteraction.relatedEntityCollection || AiInteractionRelatedEntityCollection.REVIEWS,
            createdAt: new Date(),
            updatedAt: new Date(),
        }));
        await this._aiInteractionsRepository.createAiInteractions(aiInteractionsToSave);
    }

    private _mapToDbPreviousReviewsAnalysisAndReviewerNameValidation(
        previousReviewsAnalysisAndReviewerNameValidation: PreviousReviewsAnalysisAndReviewerNameValidation
    ): PreviousReviewsAnalysisAndReviewerNameValidation {
        return {
            responseStyle: {
                responseStructure:
                    previousReviewsAnalysisAndReviewerNameValidation.responseStyle.responseStructure || DEFAULT_RESPONSE_STYLE_VALUE,
                style: previousReviewsAnalysisAndReviewerNameValidation.responseStyle.style || DEFAULT_RESPONSE_STYLE_VALUE,
                toneOfVoice: previousReviewsAnalysisAndReviewerNameValidation.responseStyle.toneOfVoice || DEFAULT_RESPONSE_STYLE_VALUE,
            },
            matchedReviewsIds: previousReviewsAnalysisAndReviewerNameValidation.matchedReviewsIds || [],
            reviewerNameValidation: {
                firstName:
                    previousReviewsAnalysisAndReviewerNameValidation.reviewerNameValidation.firstName ||
                    DEFAULT_REVIEWER_NAME_VALIDATION_VALUE,
                isFirstNameValid: previousReviewsAnalysisAndReviewerNameValidation.reviewerNameValidation.isFirstNameValid || false,
                lastName:
                    previousReviewsAnalysisAndReviewerNameValidation.reviewerNameValidation.lastName ||
                    DEFAULT_REVIEWER_NAME_VALIDATION_VALUE,
                isLastNameValid: previousReviewsAnalysisAndReviewerNameValidation.reviewerNameValidation.isLastNameValid || false,
                gender: previousReviewsAnalysisAndReviewerNameValidation.reviewerNameValidation.gender,
            },
        };
    }

    private _mapToLambdaPreviousReviewsAnalysisAndReviewerNameValidation(
        previousReviewsAnalysisAndReviewerNameValidation: PreviousReviewsAnalysisAndReviewerNameValidation
    ): PreviousReviewsAnalysisAndReviewerNameValidation {
        return {
            responseStyle: {
                responseStructure:
                    previousReviewsAnalysisAndReviewerNameValidation.responseStyle.responseStructure === DEFAULT_RESPONSE_STYLE_VALUE
                        ? ''
                        : previousReviewsAnalysisAndReviewerNameValidation.responseStyle.responseStructure,
                style:
                    previousReviewsAnalysisAndReviewerNameValidation.responseStyle.style === DEFAULT_RESPONSE_STYLE_VALUE
                        ? ''
                        : previousReviewsAnalysisAndReviewerNameValidation.responseStyle.style,
                toneOfVoice:
                    previousReviewsAnalysisAndReviewerNameValidation.responseStyle.toneOfVoice === DEFAULT_RESPONSE_STYLE_VALUE
                        ? ''
                        : previousReviewsAnalysisAndReviewerNameValidation.responseStyle.toneOfVoice,
            },
            matchedReviewsIds: previousReviewsAnalysisAndReviewerNameValidation.matchedReviewsIds,
            reviewerNameValidation: {
                firstName:
                    previousReviewsAnalysisAndReviewerNameValidation.reviewerNameValidation.firstName ===
                    DEFAULT_REVIEWER_NAME_VALIDATION_VALUE
                        ? ''
                        : previousReviewsAnalysisAndReviewerNameValidation.reviewerNameValidation.firstName,
                isFirstNameValid: previousReviewsAnalysisAndReviewerNameValidation.reviewerNameValidation.isFirstNameValid,
                lastName:
                    previousReviewsAnalysisAndReviewerNameValidation.reviewerNameValidation.lastName ===
                    DEFAULT_REVIEWER_NAME_VALIDATION_VALUE
                        ? ''
                        : previousReviewsAnalysisAndReviewerNameValidation.reviewerNameValidation.lastName,
                isLastNameValid: previousReviewsAnalysisAndReviewerNameValidation.reviewerNameValidation.isLastNameValid,
                gender: previousReviewsAnalysisAndReviewerNameValidation.reviewerNameValidation.gender,
            },
        };
    }

    private _getAnswerReviewSearchQuery(review: IReview, lang?: string): any {
        const platformKeys = getPlatformDefinitions()
            .filter((p) =>
                isDeliveryPlatform(review.key as PlatformKey)
                    ? p.category === PlatformCategory.DELIVERY
                    : p.category !== PlatformCategory.DELIVERY
            )
            .map((p) => p.key);

        const reviewHasText: boolean = !!this._generateReviewReplyService.getReviewText(review);
        const searchQuery: any = {
            _id: { $ne: review._id },
            restaurantId: review.restaurantId,
            socialCreatedAt: { $ne: null },
            'comments.posted': PostedStatus.POSTED,
            key: { $in: platformKeys },
            rating: review.rating,
            wasAnsweredAutomatically: false,
        };

        if (reviewHasText) {
            if (lang) {
                searchQuery.lang = lang;
            }
            searchQuery.$or = [
                { text: { $ne: null } },
                { ratingTags: { $exists: true, $ne: [] } },
                { menuItemReviews: { $exists: true, $ne: [] } },
            ];
        } else {
            searchQuery.$or = [
                { $and: [{ text: null }, { ratingTags: { $exists: false } }, { menuItemReviews: { $exists: false } }] },
                { $and: [{ text: null }, { ratingTags: { $size: 0 } }, { menuItemReviews: { $size: 0 } }] },
            ];
        }
        return searchQuery;
    }
}
