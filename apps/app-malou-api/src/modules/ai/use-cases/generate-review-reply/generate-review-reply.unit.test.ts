import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { AI_HARD_LIMIT_CALL_COUNT, Civility, MalouErrorCode } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { AiReviewsService } from ':microservices/ai-reviews.service';
import { GenerateReviewReplyAdvancedSettingsPayload } from ':modules/ai/interfaces/ai.interfaces';
import { AiCompletionLangService } from ':modules/ai/services/ai-completion-lang.service';
import { PreviousReviewsAnalysisService } from ':modules/ai/services/previous-reviews-analysis/previous-reviews-analysis.service';
import { GenerateReviewReplyUseCase } from ':modules/ai/use-cases/generate-review-reply/generate-review-reply.use-case';
import { getDefaultCategory } from ':modules/categories/tests/categories.builder';
import { Breakdown } from ':modules/keywords/entities/breakdown.entity';
import { KeywordBricksUseCase } from ':modules/keywords/modules/keyword-bricks/keyword-bricks.use-case';
import { getDefaultRestaurantAiSettings } from ':modules/restaurant-ai-settings/tests/restaurant-ai-settings.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultComment, getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import * as experimentationModule from ':services/experimentations-service/experimentation.service';

let generateReviewReply: GenerateReviewReplyUseCase;
let aiReviewsService: AiReviewsService<GenerateReviewReplyAdvancedSettingsPayload>;
let keywordBricksUseCase: KeywordBricksUseCase;
let aiCompletionLangService: AiCompletionLangService;
let previousReviewsAnalysisService: PreviousReviewsAnalysisService;

describe('generate-review-reply.use-case.ts', () => {
    beforeEach(() => {
        container.reset();
        keywordBricksUseCase = {} as KeywordBricksUseCase;
        container.registerInstance(KeywordBricksUseCase, keywordBricksUseCase);
        aiReviewsService = {} as AiReviewsService<GenerateReviewReplyAdvancedSettingsPayload>;
        container.registerInstance(AiReviewsService, aiReviewsService);
        aiCompletionLangService = {} as AiCompletionLangService;
        container.registerInstance(AiCompletionLangService, aiCompletionLangService);
        previousReviewsAnalysisService = {} as PreviousReviewsAnalysisService;
        container.registerInstance(PreviousReviewsAnalysisService, previousReviewsAnalysisService);

        registerRepositories([
            'RestaurantAiSettingsRepository',
            'RestaurantsRepository',
            'ReviewsRepository',
            'AiInteractionsRepository',
            'CategoriesRepository',
        ]);

        generateReviewReply = container.resolve(GenerateReviewReplyUseCase);

        const isFeatureAvailableForRestaurantSpy = jest.spyOn(experimentationModule, 'isFeatureAvailableForRestaurant');
        isFeatureAvailableForRestaurantSpy.mockResolvedValue(true);
    });
    describe('execute', () => {
        it('should throw if review not found', async () => {
            const userId = newDbId().toString();
            const reviewId = newDbId().toString();

            await generateReviewReply
                .execute({ reviewId, userId })
                .catch((err) => expect(err.malouErrorCode).toEqual(MalouErrorCode.REVIEW_NOT_FOUND));
        });

        it('should throw if restaurant not found', async () => {
            const restaurantId = newDbId();
            const userId = newDbId().toString();
            const reviewId = newDbId();

            const testCase = new TestCaseBuilderV2({
                seeds: {
                    reviews: {
                        data() {
                            return [getDefaultReview()._id(reviewId).restaurantId(restaurantId).build()];
                        },
                    },
                },
                expectedErrorCode: MalouErrorCode.RESTAURANT_NOT_FOUND,
            });

            await testCase.build();
            const errorCode = testCase.getExpectedErrorCode();

            await generateReviewReply
                .execute({ reviewId: reviewId.toString(), userId })
                .catch((err) => expect(err.malouErrorCode).toEqual(errorCode));
        });

        it('should throw if not enough credits', async () => {
            const restaurantId = newDbId();
            const userId = newDbId().toString();
            const reviewId = newDbId();

            const testCase = new TestCaseBuilderV2({
                seeds: {
                    categories: {
                        data() {
                            return [getDefaultCategory().build()];
                        },
                    },
                    reviews: {
                        data() {
                            return [getDefaultReview()._id(reviewId).restaurantId(restaurantId).build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    ._id(restaurantId)
                                    .ai({ monthlyCallCount: AI_HARD_LIMIT_CALL_COUNT + 1 })
                                    .build(),
                            ];
                        },
                    },
                },
                expectedErrorCode: MalouErrorCode.NOT_ENOUGH_CREDIT_TO_MAKE_AI_API_CALL,
            });

            await testCase.build();
            const errorCode = testCase.getExpectedErrorCode();

            return generateReviewReply
                .execute({ reviewId: reviewId.toString(), userId })
                .catch((err) => expect(err.malouErrorCode).toEqual(errorCode));
        });

        it('should return clean completion', async () => {
            const restaurantId = newDbId();
            const reviewId = newDbId();
            const userId = newDbId().toString();

            const keywordBricks = [
                new Breakdown({ text: 'text1', category: 'category' }),
                new Breakdown({ text: 'text2', category: 'category' }),
            ];
            const cleanedCompletion = 'zefze';
            const reviewLang = 'fr';

            const testCase = new TestCaseBuilderV2({
                seeds: {
                    categories: {
                        data() {
                            return [getDefaultCategory().build()];
                        },
                    },
                    reviews: {
                        data() {
                            return [
                                getDefaultReview()._id(reviewId).restaurantId(restaurantId).text('text').lang(reviewLang).build(),
                                getDefaultReview()
                                    .restaurantId(restaurantId)
                                    .text('text1')
                                    .lang(reviewLang)
                                    .comments([
                                        {
                                            ...getDefaultComment().build(),
                                            text: 'text1',
                                        },
                                    ])
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(restaurantId)
                                    .text('text2')
                                    .lang(reviewLang)
                                    .comments([
                                        {
                                            ...getDefaultComment().build(),
                                            text: 'text2',
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [getDefaultRestaurant()._id(restaurantId).category(dependencies.categories()[0]._id).build()];
                        },
                    },
                    restaurantAiSettings: {
                        data() {
                            return [getDefaultRestaurantAiSettings().restaurantId(restaurantId).build()];
                        },
                    },
                },
                expectedErrorCode: undefined,
            });

            await testCase.build();
            const seededObjects = await testCase.getSeededObjects();

            keywordBricksUseCase.getSelectedKeywordsBricks = jest.fn().mockResolvedValue(keywordBricks);

            aiReviewsService.generateCompletion = jest.fn().mockResolvedValue({ aiResponse: cleanedCompletion });
            previousReviewsAnalysisService.getReviewCommentsSample = jest.fn().mockResolvedValue([]);
            previousReviewsAnalysisService.analyzePreviousReviews = jest.fn().mockResolvedValue({
                responseStyle: { style: '', toneOfVoice: '', responseStructure: '' },
                matchedReviewsIds: [seededObjects.reviews[1]._id.toString(), seededObjects.reviews[2]._id.toString()],
                reviewerNameValidation: {
                    lastName: 'Einstein',
                    isLastNameValid: true,
                    firstName: 'Albert',
                    isFirstNameValid: true,
                    gender: Civility.MALE,
                },
            });

            aiCompletionLangService.getLangForReviewReplyGeneration = jest.fn().mockReturnValue(reviewLang);

            const { completionText } = await generateReviewReply.execute({ reviewId: reviewId.toString(), userId });
            expect(completionText).toEqual(cleanedCompletion);
            jest.restoreAllMocks();
        });
    });
});
