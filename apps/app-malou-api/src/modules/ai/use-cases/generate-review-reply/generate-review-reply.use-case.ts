import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IReview, toDbId } from '@malou-io/package-models';
import {
    AI_HARD_LIMIT_CALL_COUNT,
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    AiModel,
    AiModelProvider,
    ApplicationLanguage,
    CustomerNaming,
    FrenchTutoiementVouvoiement,
    getApplicationLanguageDisplayName,
    isLangInApplicationLanguages,
    MalouErrorCode,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { GenericAiServiceResponseType } from ':microservices/ai-lambda-template/generic-ai-service';
import { AiReviewsService, AiTranslationsText } from ':microservices/ai-reviews.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { assertRestaurantCanMakeAiCall, getSignatureAndCatchphrase } from ':modules/ai/helpers';
import { AiPayloadOptions, GenerateReviewReplyAdvancedSettingsPayload, RestaurantWithCategory } from ':modules/ai/interfaces/ai.interfaces';
import { AiCompletionMapper } from ':modules/ai/mappers/ai-completion.mapper';
import { AiCompletionLangService } from ':modules/ai/services/ai-completion-lang.service';
import { GenerateReviewReplyService } from ':modules/ai/services/generate-review-reply.service';
import { PreviousReviewsAnalysisService } from ':modules/ai/services/previous-reviews-analysis/previous-reviews-analysis.service';
import { TranslateTextService } from ':modules/ai/services/translate-text.service';
import { RestaurantAiSettings } from ':modules/restaurant-ai-settings/entities/restaurant-ai-settings.entity';
import { RestaurantAiSettingsRepository } from ':modules/restaurant-ai-settings/restaurant-ai-settings.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { isFeatureAvailableForRestaurant } from ':services/experimentations-service/experimentation.service';

@singleton()
export class GenerateReviewReplyUseCase {
    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        private readonly _restaurantAiSettingsRepository: RestaurantAiSettingsRepository,
        private readonly _aiCompletionMapper: AiCompletionMapper,
        private readonly _aiReviewsService: AiReviewsService<GenerateReviewReplyAdvancedSettingsPayload>,
        private readonly _reviewReplyService: GenerateReviewReplyService,
        private readonly _aiCompletionLangService: AiCompletionLangService,
        private readonly _translateTextService: TranslateTextService,
        private readonly _previousReviewsAnalysisService: PreviousReviewsAnalysisService
    ) {}

    async execute({
        reviewId,
        userId,
        retryCount = 1,
    }: {
        reviewId: string;
        userId: string;
        retryCount?: number;
    }): Promise<{ aiInteractionId: string; completionText: string; completionLang: string }> {
        const aiInteraction = await this._aiInteractionsRepository.create({
            data: {
                type: AiInteractionType.ANSWER_REVIEW_ADVANCED_SETTINGS,
                relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
                relatedEntityId: toDbId(reviewId),
                userId: toDbId(userId),
            },
        });

        try {
            logger.info('[AiUseCases] [generateReviewReply] Start', { reviewId, aiInteraction });
            const review = await this._reviewsRepository.getReviewById(reviewId);

            if (!review) {
                throw new MalouError(MalouErrorCode.REVIEW_NOT_FOUND, { message: `Review not found : ${reviewId}` });
            }

            const restaurant = await this._restaurantsRepository.findOne({
                filter: { _id: review.restaurantId },
                options: { lean: true, populate: [{ path: 'category' }] },
            });
            if (!restaurant) {
                throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, { message: 'Restaurant not found' });
            }
            assertRestaurantCanMakeAiCall(restaurant, AI_HARD_LIMIT_CALL_COUNT);

            const restaurantAiSettings = await this._restaurantAiSettingsRepository.getRestaurantAiSettingsByRestaurantIdWithTranslations(
                restaurant._id.toString()
            );
            const language = await this._aiCompletionLangService.getLangForReviewReplyGeneration(review, restaurantAiSettings);

            // For all languages not in the app, we generate in English and then translate to the language
            const generationLanguage = isLangInApplicationLanguages(language) ? language : ApplicationLanguage.EN;
            const payload = await this._computePayload({ review, restaurant, restaurantAiSettings, generationLanguage });

            let completion: GenericAiServiceResponseType<AiTranslationsText | string> | undefined = undefined;
            let retry = 0;
            while (retry < retryCount) {
                try {
                    completion = await this._aiReviewsService.generateCompletion(payload);
                    break;
                } catch (error) {
                    retry++;
                    if (retry === retryCount) {
                        throw error;
                    }
                }
            }

            assert(completion);

            await this._restaurantsRepository.incrementRestaurantAiCallCount({
                restaurantId: restaurant._id,
                feature: AiInteractionType.ANSWER_REVIEW_ADVANCED_SETTINGS,
            });
            const updatedAiInteraction = this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(
                completion.aiInteractionDetails?.[0],
                restaurant._id
            );
            const generationAiInteraction = await this._aiInteractionsRepository.findOneAndUpdateOrFail({
                filter: { _id: aiInteraction._id },
                update: updatedAiInteraction,
                options: { new: true },
            });

            let completionText: string = completion?.aiResponse.toString();
            if (generationLanguage !== language) {
                completionText = await this._translateTextService.execute({
                    relatedEntityId: reviewId,
                    aiInteractionRelatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
                    type: AiInteractionType.REVIEW_ANSWER_TRANSLATION,
                    text: completionText,
                    lang: language,
                    restaurantId: restaurant._id?.toString(),
                    userId,
                });
            }

            return { aiInteractionId: generationAiInteraction?._id.toString(), completionText, completionLang: language };
        } catch (error: any) {
            logger.error('[AiUseCases] [generateReviewReply] Error', { error, reviewId });
            await this._aiInteractionsRepository.findOneAndUpdateOrFail({
                filter: { _id: aiInteraction._id },
                update: {
                    error: {
                        malouErrorCode: error?.malouErrorCode,
                        message: error?.message,
                        stack: error?.stack,
                    },
                },
            });
            throw error;
        }
    }

    private async _computePayload({
        review,
        restaurant,
        restaurantAiSettings,
        generationLanguage,
    }: {
        review: IReview;
        restaurant: RestaurantWithCategory;
        restaurantAiSettings: RestaurantAiSettings | undefined;
        generationLanguage: ApplicationLanguage;
    }): Promise<GenerateReviewReplyAdvancedSettingsPayload> {
        const previousReviews = await this._previousReviewsAnalysisService.getReviewCommentsSample(
            review,
            AiPayloadOptions.previousReviewsSampleSize,
            generationLanguage
        );
        const previousMatchedReviewsAnalysis = await this._previousReviewsAnalysisService.analyzePreviousReviews({
            review,
            restaurantId: restaurant._id.toString(),
            previousReviews,
        });

        const matchedPreviousReviews = previousReviews.filter((previousReview) =>
            previousMatchedReviewsAnalysis.matchedReviewsIds.includes(previousReview.id)
        );
        const reviewText = this._reviewReplyService.getReviewText(review);

        const restaurantName = restaurantAiSettings ? restaurantAiSettings.restaurantName : restaurant.name;

        const {
            category: { categoryName },
        } = restaurant;
        const category = categoryName?.[generationLanguage] || categoryName?.backup || AiPayloadOptions.defaultBusinessCategoryName;

        const languageDisplayName = getApplicationLanguageDisplayName(generationLanguage, 'en') ?? generationLanguage;

        const { catchphrase, signature } = getSignatureAndCatchphrase({ restaurantAiSettings, language: generationLanguage });

        const customPrompt = (await this._isReviewsAiSettingsCustomPromptEnabled({ restaurantId: restaurant._id.toString() }))
            ? (restaurantAiSettings?.reviewSettings?.prompt ?? '')
            : '';

        return {
            relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
            type: AiInteractionType.ANSWER_REVIEW_ADVANCED_SETTINGS,
            restaurantData: {
                restaurantName: restaurantName,
                restaurantCategory: category,
                restaurantCity: restaurant.address?.locality ?? '',
                language: languageDisplayName,
                previousReviewsComments: matchedPreviousReviews.map((previousReview) => ({
                    reviewText: previousReview.reviewText ?? '',
                    responseText: previousReview.responseText,
                })),
                platform: review.key,
                reviewRating: review.rating,
                reviewerName: review.reviewer?.displayName ?? '',
                review: reviewText ?? '',
                badwords: restaurantAiSettings?.reviewSettings?.forbiddenWords ?? [],
                signature: signature,
                userAddress: restaurantAiSettings?.reviewSettings?.customerNaming ?? CustomerNaming.FIRSTNAME,
                tone: restaurantAiSettings?.reviewSettings?.replyTone ?? FrenchTutoiementVouvoiement.DOES_NOT_MATTER,
                introductiveSentence: catchphrase,
                customPrompt,
                responseStyle: previousMatchedReviewsAnalysis.responseStyle,
                reviewerNameValidation: previousMatchedReviewsAnalysis.reviewerNameValidation,
            },
            model: restaurantAiSettings?.reviewSettings?.model ?? AiModel.GPT_4O,
            modelProvider: restaurantAiSettings?.reviewSettings?.modelProvider ?? AiModelProvider.OPENAI,
        };
    }

    private async _isReviewsAiSettingsCustomPromptEnabled({ restaurantId }: { restaurantId: string }): Promise<boolean> {
        const isEnabledForRestaurant = await isFeatureAvailableForRestaurant({
            restaurantId,
            featureName: 'release-reviews-ai-settings-custom-prompt',
        });
        return isEnabledForRestaurant;
    }
}
