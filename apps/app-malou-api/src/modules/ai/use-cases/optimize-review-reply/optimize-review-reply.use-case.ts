import { singleton } from 'tsyringe';

import { AiOptimizeTextDto } from '@malou-io/package-dto';
import { IReview, toDbId } from '@malou-io/package-models';
import {
    AI_HARD_LIMIT_CALL_COUNT,
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    AiModel,
    AiModelProvider,
    CustomerNaming,
    FrenchTutoiementVouvoiement,
    getApplicationLanguageDisplayName,
    MalouErrorCode,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { AiReviewsService } from ':microservices/ai-reviews.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { assertRestaurantCanMakeAiCall, getSignatureAndCatchphrase } from ':modules/ai/helpers';
import { AiPayloadOptions, OptimizeReviewReplyAdvancedSettingsPayload, RestaurantWithCategory } from ':modules/ai/interfaces/ai.interfaces';
import { AiCompletionMapper } from ':modules/ai/mappers/ai-completion.mapper';
import { AiCompletionLangService } from ':modules/ai/services/ai-completion-lang.service';
import { GenerateReviewReplyService } from ':modules/ai/services/generate-review-reply.service';
import { PreviousReviewsAnalysisService } from ':modules/ai/services/previous-reviews-analysis/previous-reviews-analysis.service';
import { RestaurantAiSettings } from ':modules/restaurant-ai-settings/entities/restaurant-ai-settings.entity';
import { RestaurantAiSettingsRepository } from ':modules/restaurant-ai-settings/restaurant-ai-settings.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';

@singleton()
export class OptimizeReviewReplyUseCase {
    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        private readonly _restaurantAiSettingsRepository: RestaurantAiSettingsRepository,
        private readonly _aiCompletionMapper: AiCompletionMapper,
        private readonly _aiReviewsService: AiReviewsService<OptimizeReviewReplyAdvancedSettingsPayload>,
        private readonly _reviewReplyService: GenerateReviewReplyService,
        private readonly _aiCompletionLangService: AiCompletionLangService,
        private readonly _previousReviewsAnalysisService: PreviousReviewsAnalysisService
    ) {}

    async execute(reviewId: string, textToOptimize: string, restaurantId: string, userId: string): Promise<AiOptimizeTextDto> {
        const aiInteractionType = AiInteractionType.OPTIMIZE_REVIEW_ANSWER_ADVANCED_SETTINGS;
        const aiInteraction = await this._aiInteractionsRepository.create({
            data: {
                type: aiInteractionType,
                relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
                relatedEntityId: toDbId(reviewId),
                userId: toDbId(userId),
            },
        });

        try {
            const review = await this._reviewsRepository.findOneOrFail({
                filter: { _id: toDbId(reviewId) },
                options: { lean: true },
            });

            const restaurant = await this._restaurantsRepository.findOne({
                filter: { _id: toDbId(restaurantId) },
                options: { lean: true, populate: [{ path: 'category' }] },
            });
            if (!restaurant) {
                throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, { message: 'Restaurant not found' });
            }
            assertRestaurantCanMakeAiCall(restaurant, AI_HARD_LIMIT_CALL_COUNT);

            const optimizationLang = await this._aiCompletionLangService.getLangForReviewReplyOptimization({
                reviewId: reviewId.toString(),
                restaurantId: restaurant._id.toString(),
                text: textToOptimize,
            });

            const payload = await this._computePayload(review, textToOptimize, restaurant, optimizationLang);

            const { aiResponse, aiInteractionDetails } = await this._aiReviewsService.generateCompletion(payload);

            await this._restaurantsRepository.incrementRestaurantAiCallCount({
                restaurantId: restaurant._id,
                feature: AiInteractionType.OPTIMIZE_REVIEW_ANSWER_ADVANCED_SETTINGS,
            });
            const updatedAiInteraction = this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(
                aiInteractionDetails?.[0],
                restaurant._id
            );

            await this._aiInteractionsRepository.findOneAndUpdate({
                filter: { _id: aiInteraction._id },
                update: updatedAiInteraction,
                options: { new: true },
            });
            return {
                optimizedText: aiResponse.toString(),
                lang: optimizationLang,
            };
        } catch (error: any) {
            logger.error('[AiUseCases] [optimizeReviewAnswerText] Error', { error, reviewId });
            await this._aiInteractionsRepository.findOneAndUpdate({
                filter: { _id: aiInteraction._id },
                update: {
                    error: {
                        malouErrorCode: error?.malouErrorCode,
                        message: error?.message,
                        stack: error?.stack,
                    },
                },
            });
            throw error;
        }
    }

    private async _computePayload(
        review: IReview,
        textToOptimize: string,
        restaurant: RestaurantWithCategory,
        lang: string
    ): Promise<OptimizeReviewReplyAdvancedSettingsPayload> {
        const restaurantAiSettings: RestaurantAiSettings | undefined =
            await this._restaurantAiSettingsRepository.getRestaurantAiSettingsByRestaurantIdWithTranslations(restaurant._id.toString());

        const previousReviews = await this._previousReviewsAnalysisService.getReviewCommentsSample(
            review,
            AiPayloadOptions.previousReviewsSampleSize,
            lang
        );
        const previousMatchedReviewsAnalysis = await this._previousReviewsAnalysisService.analyzePreviousReviews({
            review,
            restaurantId: restaurant._id.toString(),
            previousReviews,
        });

        const matchedPreviousReviews = previousReviews.filter((previousReview) =>
            previousMatchedReviewsAnalysis.matchedReviewsIds.includes(previousReview.id)
        );

        const {
            category: { categoryName },
        } = restaurant;
        const category = categoryName?.[lang] || categoryName?.backup || AiPayloadOptions.defaultBusinessCategoryName;

        const languageDisplayName = getApplicationLanguageDisplayName(lang, 'en') || lang;

        const { catchphrase, signature } = getSignatureAndCatchphrase({ restaurantAiSettings, language: lang });

        return {
            relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
            type: AiInteractionType.OPTIMIZE_REVIEW_ANSWER_ADVANCED_SETTINGS,
            restaurantData: {
                restaurantName: restaurant.name,
                restaurantCategory: category,
                restaurantCity: restaurant.address?.locality ?? '',
                language: languageDisplayName,
                previousReviewsComments: matchedPreviousReviews.map((previousReview) => ({
                    reviewText: previousReview.reviewText ?? '',
                    responseText: previousReview.responseText,
                })),
                textToOptimize,
                reviewerName: review.reviewer?.displayName ?? '',
                reviewRating: review.rating,
                platform: review.key,
                badwords: restaurantAiSettings?.reviewSettings?.forbiddenWords ?? [],
                signature: signature,
                userAddress: restaurantAiSettings?.reviewSettings?.customerNaming ?? CustomerNaming.FIRSTNAME,
                tone: restaurantAiSettings?.reviewSettings?.replyTone ?? FrenchTutoiementVouvoiement.DOES_NOT_MATTER,
                introductiveSentence: catchphrase,
                responseStyle: previousMatchedReviewsAnalysis.responseStyle,
                reviewerNameValidation: previousMatchedReviewsAnalysis.reviewerNameValidation,
            },
            model: restaurantAiSettings?.reviewSettings?.model ?? AiModel.GPT_4O,
            modelProvider: restaurantAiSettings?.reviewSettings?.modelProvider ?? AiModelProvider.OPENAI,
        };
    }
}
