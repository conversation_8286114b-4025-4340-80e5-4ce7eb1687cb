import { container } from 'tsyringe';

import { DbId, newDbId, toDbId } from '@malou-io/package-models';
import {
    AI_HARD_LIMIT_CALL_COUNT,
    AiModel,
    AiModelProvider,
    AiPostGenerationEmojiStatus,
    AiPostSettingsLength,
    AiPostSettingsTone,
    ApplicationLanguage,
    Civility,
    CustomerNaming,
    FrenchTutoiementVouvoiement,
    MalouErrorCode,
    TranslationSource,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { AiReviewsService } from ':microservices/ai-reviews.service';
import { PreviousReviewsAnalysisService } from ':modules/ai/services/previous-reviews-analysis/previous-reviews-analysis.service';
import { AnswerReviewPreviewUseCase } from ':modules/ai/use-cases/answer-review-preview/answer-review-preview.use-case';
import { getDefaultCategory } from ':modules/categories/tests/categories.builder';
import { RestaurantAiSettings } from ':modules/restaurant-ai-settings/entities/restaurant-ai-settings.entity';
import { getDefaultRestaurantAiSettings } from ':modules/restaurant-ai-settings/tests/restaurant-ai-settings.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { Translations } from ':modules/translations/entities/translations.entity';
import { TranslateCatchphraseAndSignatureService } from ':modules/translations/services/translate-catchphrase-and-signature.service';
import { getDefaultUser } from ':modules/users/tests/user.builder';
import * as experimentationModule from ':services/experimentations-service/experimentation.service';

describe('answer-review-preview.use-case.ts', () => {
    const cleanedCompletion = 'CleanedCompletion';

    beforeAll(() => {
        registerRepositories([
            'ReviewsRepository',
            'UsersRepository',
            'RestaurantsRepository',
            'CategoriesRepository',
            'RestaurantAiSettingsRepository',
        ]);

        class AiReviewsServiceMock {
            generateCompletion(): Promise<{ aiResponse: string }> {
                return Promise.resolve({ aiResponse: cleanedCompletion });
            }
        }
        container.register(AiReviewsService, {
            useValue: new AiReviewsServiceMock() as any,
        });

        container.register(TranslateCatchphraseAndSignatureService, {
            useValue: {
                execute: jest.fn().mockImplementation(() =>
                    Promise.resolve(
                        new Translations({
                            id: newDbId().toString(),
                            language: ApplicationLanguage.FR,
                            source: TranslationSource.SERVERLESS_AI_TEXT_GENERATOR,
                        })
                    )
                ),
            } as unknown as any,
        });

        const isFeatureAvailableForRestaurantSpy = jest.spyOn(experimentationModule, 'isFeatureAvailableForRestaurant');
        isFeatureAvailableForRestaurantSpy.mockResolvedValue(true);

        container.register(PreviousReviewsAnalysisService, {
            useValue: {
                analyzePreviousReviews: jest.fn().mockResolvedValue({
                    responseStyle: { style: 'short', toneOfVoice: 'formal', responseStructure: 'paragraph' },
                    matchedReviewsIds: [],
                    reviewerNameValidation: {
                        lastName: 'Einstein',
                        isLastNameValid: true,
                        firstName: 'Albert',
                        isFirstNameValid: true,
                        gender: Civility.MALE,
                    },
                }),
                getReviewCommentsSample: jest.fn().mockResolvedValue([]),
            } as unknown as PreviousReviewsAnalysisService,
        });
    });

    describe('execute', () => {
        it('should throw if restaurant not found', async () => {
            const answerReviewPreviewUseCase = container.resolve(AnswerReviewPreviewUseCase);
            const restaurantId = newDbId().toString();
            const userId = newDbId().toString();
            const reviewLang = ApplicationLanguage.FR;

            const testCase = new TestCaseBuilderV2({
                seeds: {},
                expectedErrorCode: MalouErrorCode.RESTAURANT_NOT_FOUND,
            });

            await testCase.build();
            const expectedErrorCode = testCase.getExpectedErrorCode();

            await expect(
                answerReviewPreviewUseCase.execute({
                    restaurantId,
                    userId,
                    sourceLanguage: reviewLang,
                    restaurantAiSettings: {} as unknown as RestaurantAiSettings,
                    text: '',
                    reviewerName: '',
                    lang: reviewLang,
                })
            ).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: expectedErrorCode,
                })
            );
        });

        it('should throw if not enough credits', async () => {
            const answerReviewPreviewUseCase = container.resolve(AnswerReviewPreviewUseCase);
            const userId = newDbId().toString();
            const reviewLang = ApplicationLanguage.FR;

            const credits = AI_HARD_LIMIT_CALL_COUNT + 1;

            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().ai({ monthlyCallCount: credits }).build()];
                        },
                    },
                },
                expectedErrorCode: MalouErrorCode.NOT_ENOUGH_CREDIT_TO_MAKE_AI_API_CALL,
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedErrorCode = testCase.getExpectedErrorCode();

            await expect(
                answerReviewPreviewUseCase.execute({
                    restaurantId,
                    userId,
                    restaurantAiSettings: {} as unknown as RestaurantAiSettings,
                    sourceLanguage: reviewLang,
                    text: '',
                    reviewerName: '',
                    lang: reviewLang,
                })
            ).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: expectedErrorCode,
                })
            );
        });

        it('should return clean completion', async () => {
            const answerReviewPreviewUseCase = container.resolve(AnswerReviewPreviewUseCase);

            const reviewLang = ApplicationLanguage.FR;

            const testCase = new TestCaseBuilderV2({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    categories: {
                        data() {
                            return [getDefaultCategory().build()];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [getDefaultRestaurant().category(toDbId(dependencies.categories()[0]._id)).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview().restaurantId(dependencies.restaurants()[0]._id).lang(reviewLang).build(),
                                getDefaultReview().restaurantId(dependencies.restaurants()[0]._id).lang(reviewLang).build(),
                            ];
                        },
                    },
                    restaurantAiSettings: {
                        data(dependencies) {
                            return [getDefaultRestaurantAiSettings().restaurantId(dependencies.restaurants()[0]._id).build()];
                        },
                    },
                },
                expectedResult() {
                    return cleanedCompletion;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const userId = (seededObjects.users[0]._id as DbId).toString();

            const expectedResult = testCase.getExpectedResult();

            const result = await answerReviewPreviewUseCase.execute({
                restaurantId,
                userId,
                restaurantAiSettings: new RestaurantAiSettings({
                    defaultLanguageResponse: ApplicationLanguage.FR,
                    postSettings: {
                        seo: {
                            isDefaultPostSettings: false,
                            denomination: FrenchTutoiementVouvoiement.DOES_NOT_MATTER,
                            emoji: AiPostGenerationEmojiStatus.none,
                            tone: [AiPostSettingsTone.casual],
                            length: AiPostSettingsLength.medium,
                            prompt: 'test prompt',
                        },
                        social: {
                            isDefaultPostSettings: false,
                            denomination: FrenchTutoiementVouvoiement.DOES_NOT_MATTER,
                            emoji: AiPostGenerationEmojiStatus.none,
                            tone: [AiPostSettingsTone.casual],
                            length: AiPostSettingsLength.medium,
                            prompt: 'test prompt',
                        },
                    },
                    restaurantId: restaurantId,
                    restaurantName: 'Restaurant Name',
                    reviewSettings: {
                        replyTone: FrenchTutoiementVouvoiement.DOES_NOT_MATTER,
                        customerNaming: CustomerNaming.FIRSTNAME,
                        restaurantKeywordIds: [],
                        forbiddenWords: [],
                        catchphrase: 'wsh alors jul',
                        signatures: [''],
                        shouldTranslateCatchphrase: true,
                        shouldTranslateSignature: true,
                        prompt: '',
                        model: AiModel.GPT_4O,
                        modelProvider: AiModelProvider.OPENAI,
                    },
                }),
                sourceLanguage: reviewLang,
                text: '',
                reviewerName: '',
                lang: reviewLang,
            });
            expect(result).toEqual(expectedResult);
        });
    });
});
