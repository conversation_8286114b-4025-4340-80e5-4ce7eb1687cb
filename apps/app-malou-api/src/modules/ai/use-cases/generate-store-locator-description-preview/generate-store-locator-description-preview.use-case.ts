import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { GenerateStoreLocatorDescriptionPreviewBodyDto } from '@malou-io/package-dto';
import { ReadPreferenceMode, toDbId, toDbIds } from '@malou-io/package-models';
import { BusinessCategory, GenerateStoreLocatorContentType, MalouErrorCode, StoreLocatorLanguage } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { AttributesRepository } from ':modules/attributes/attributes.repository';
import { RestaurantKeywordsRepository } from ':modules/restaurant-keywords/restaurant-keywords.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { GenerateStorePageContentService } from ':modules/store-locator/services/generate-store-page-content/generate-store-page-content.service';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';

@singleton()
export class GenerateStoreLocatorDescriptionPreviewUseCase {
    constructor(
        private readonly _generateStorePageContentService: GenerateStorePageContentService,
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _attributesRepository: AttributesRepository,
        private readonly _restaurantKeywordsRepository: RestaurantKeywordsRepository
    ) {}

    async execute(params: GenerateStoreLocatorDescriptionPreviewBodyDto & { userId: string }): Promise<string> {
        const { organizationId, aiSettings } = params;
        const lang = StoreLocatorLanguage.FR;

        const [storeLocatorOrganizationConfig, restaurant, organizationAttributes, organizationKeywords] = await Promise.all([
            this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(organizationId),
            this._restaurantsRepository.findOne({
                filter: {
                    organizationId: toDbId(organizationId),
                    active: true,
                    type: BusinessCategory.LOCAL_BUSINESS,
                },
                projection: { _id: 1 },
                options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY },
            }),
            this._attributesRepository.find({
                filter: { attributeId: { $in: aiSettings.attributeIds } },
                projection: { _id: 1, attributeId: 1, attributeName: 1 },
                options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY },
            }),
            this._restaurantKeywordsRepository.find({
                filter: { _id: { $in: toDbIds(aiSettings.restaurantKeywordIds) } },
                options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY, populate: [{ path: 'keyword' }] },
            }),
        ]);

        assert(restaurant, 'No active restaurant found for the organization');

        const keywords = organizationKeywords.map(({ keyword }) => keyword.text);
        const payload = await this._generateStorePageContentService.getLambdaPayloadFromOrganizationParams({
            restaurantId: restaurant._id.toString(),
            organizationConfig: {
                organizationName: storeLocatorOrganizationConfig.organization.name,
                tone: aiSettings.tone,
                languageStyle: aiSettings.languageStyle,
                specialAttributes: aiSettings.specialAttributes,
                attributes: organizationAttributes,
                keywords,
            },
            lang,
        });

        const response = await this._generateStorePageContentService.generateStoreLocatorContent({
            type: GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_GENERATION,
            payload,
        });

        const descriptions = response.blocks?.[0]?.sections?.map((section) => section.text);
        const descriptionPreview = descriptions?.length ? descriptions[Math.floor(Math.random() * descriptions.length)] : undefined;

        if (!descriptionPreview) {
            throw new MalouError(MalouErrorCode.AI_REQUEST_FAILED, {
                message: '[GenerateStoreLocatorDescriptionPreviewUseCase] - No description preview generated',
                metadata: { organizationId: params.organizationId },
            });
        }

        return descriptionPreview;
    }
}
