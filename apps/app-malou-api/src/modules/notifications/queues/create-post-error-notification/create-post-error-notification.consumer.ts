import assert from 'node:assert/strict';
import { SQSMessage } from 'sqs-consumer';
import { singleton } from 'tsyringe';

import { Config } from ':config';
import { CreatePostErrorNotificationUseCase } from ':modules/notifications/use-cases/create-post-error-notification/create-post-error-notification.use-case';
import { UseCaseQueueTag } from ':queues/sqs-template/constant';
import { GenericSqsConsumer } from ':queues/sqs-template/generic-sqs-consumer';

@singleton()
export class CreatePostErrorNotificationConsumer extends GenericSqsConsumer {
    constructor(private readonly _createPostErrorNotificationUseCase: CreatePostErrorNotificationUseCase) {
        super({
            useCaseQueueTag: UseCaseQueueTag.CREATE_POST_ERROR_NOTIFICATION,
            queueUrl: Config.services.sqs.createPostErrorNotificationQueueUrl,
            shouldAwaitExecution: true,
        });
    }

    async handleMessage(msg: SQSMessage): Promise<void> {
        assert(msg.Body, '[CreatePostErrorNotificationConsumer] Message body is required');
        const { postId }: { postId: string } = JSON.parse(msg.Body);
        await this._createPostErrorNotificationUseCase.execute({ postId });
    }
}
