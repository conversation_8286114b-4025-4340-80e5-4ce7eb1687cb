import { singleton } from 'tsyringe';

import { EntityRepository, ID, IUser, IUserRestaurant, ReadPreferenceMode, UserModel, UserRestaurantModel } from '@malou-io/package-models';
import { ADMIN_EMAILS_TO_EXCLUDE_FROM_NOTIFICATIONS, NOTIFICATIONS_ROLE, NotificationType } from '@malou-io/package-utils';

import { ReviewNotificationUser } from ':modules/notifications/entities/child-entities/review-notification-user.entity';

@singleton()
export class ReviewNotificationsUserRepository extends EntityRepository<IUserRestaurant> {
    constructor() {
        super(UserRestaurantModel);
    }

    async getUsersToNotify(): Promise<ReviewNotificationUser[]> {
        const userIdsToExclude = await UserModel.find({ email: { $in: ADMIN_EMAILS_TO_EXCLUDE_FROM_NOTIFICATIONS } }).then((docs) =>
            docs.map((doc) => doc._id)
        );

        const docs = await this.model.aggregate(
            [
                {
                    $match: {
                        caslRole: { $in: NOTIFICATIONS_ROLE[NotificationType.REVIEW_REPLY_REMINDER] },
                        userId: { $nin: userIdsToExclude },
                    },
                },
                {
                    $project: { _id: 1, restaurantId: 1, userId: 1 },
                },
                {
                    $lookup: {
                        from: 'restaurants',
                        localField: 'restaurantId',
                        foreignField: '_id',
                        as: 'restaurant',
                        pipeline: [{ $match: { active: true } }, { $project: { _id: 1, name: 1, internalName: 1 } }],
                    },
                },
                { $unwind: { path: '$restaurant' } },
                {
                    $group: {
                        _id: '$userId',
                        userId: { $first: '$userId' },
                        data: {
                            $push: {
                                restaurant: '$restaurant',
                            },
                        },
                    },
                },
                {
                    $lookup: {
                        from: 'users',
                        localField: 'userId',
                        foreignField: '_id',
                        as: 'user',
                        pipeline: [
                            {
                                $match: {
                                    verified: true,
                                },
                            },
                            {
                                $project: {
                                    _id: 1,
                                    email: 1,
                                    name: 1,
                                    lastname: 1,
                                    defaultLanguage: 1,
                                    createdAt: 1,
                                    updatedAt: 1,
                                    'settings.notifications': 1,
                                },
                            },
                        ],
                    },
                },
                {
                    $unwind: {
                        path: '$user',
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY,
                comment: 'getUsersToBeNotified',
            }
        );

        return docs.map((doc) => this._toEntity(doc));
    }

    private _toEntity(
        doc: IUserRestaurant & { user: IUser; data: { restaurant: { _id: ID; name: string; internalName?: string } }[] }
    ): ReviewNotificationUser {
        return new ReviewNotificationUser({
            userId: doc.userId.toString(),
            user: {
                id: doc.user._id.toString(),
                email: doc.user.email,
                name: doc.user.name,
                defaultLanguage: doc.user.defaultLanguage,
                createdAt: doc.user.createdAt,
                updatedAt: doc.user.updatedAt,
                lastname: doc.user.lastname,
                notificationSettings: {
                    ...doc.user.settings.notifications,
                    web: {
                        ...doc.user.settings.notifications.web,
                        filters: {
                            restaurantIds: doc.user.settings.notifications.web.filters.restaurantIds.map((r) => r.toString()),
                        },
                    },
                },
            },
            restaurants: doc.data.map((r) => ({
                id: r.restaurant._id.toString(),
                name: r.restaurant.name,
                internalName: r.restaurant.internalName,
            })),
        });
    }
}
