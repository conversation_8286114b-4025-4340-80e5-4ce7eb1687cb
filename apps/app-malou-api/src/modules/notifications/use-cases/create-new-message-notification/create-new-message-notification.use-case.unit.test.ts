import assert from 'node:assert/strict';
import { container } from 'tsyringe';

import { CaslRole, NotificationChannel, NotificationType } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { MessagesRepository } from ':modules/messages/messages.repository';
import { getDefaultConversation } from ':modules/messages/tests/conversation.builder';
import { getDefaultMessage } from ':modules/messages/tests/message.builder';
import { NotificationUser } from ':modules/notifications/entities/child-entities/notification-user.entity';
import { MessageNotification, MessageNotificationData } from ':modules/notifications/entities/message-notification.entity';
import NotificationsRepository from ':modules/notifications/repositories/notifications.repository';
import { NotificationSenderService } from ':modules/notifications/services/notifications-sender-service/notification-sender.service.interface';
import { CreateNewMessageNotificationUseCase } from ':modules/notifications/use-cases/create-new-message-notification/create-new-message-notification.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultUser, getDefaultUserRestaurant } from ':modules/users/tests/user.builder';
import { UsersRepository } from ':modules/users/users.repository';

describe('CreateNewMessageNotificationUseCase', () => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    let baseTestCase: TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'messages' | 'conversations'>;
    beforeAll(() => {
        registerRepositories([
            'UserRestaurantsRepository',
            'UsersRepository',
            'RestaurantsRepository',
            'ConversationsRepository',
            'MessagesRepository',
        ]);
        container.register<NotificationSenderService>(InjectionToken.NotificationSenderService, {
            useValue: {
                sendNotificationsToChannels: async (_) => void 0,
            },
        });
    });

    beforeEach(async () => {
        baseTestCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'messages' | 'conversations'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build(), getDefaultRestaurant().name('restaurant_1').build()];
                    },
                },
                users: {
                    data() {
                        return [
                            getDefaultUser().name('user_0').email('<EMAIL>').build(),
                            getDefaultUser().name('user_1').email('<EMAIL>').build(),
                            getDefaultUser()
                                .name('no_notif_user')
                                .email('<EMAIL>')
                                .settings({
                                    receiveFeedbacks: false,
                                    notifications: {
                                        email: {
                                            reviewReplyReminder: {
                                                active: false,
                                            },
                                            specialHourReminder: {
                                                active: false,
                                            },
                                            postSuggestion: {
                                                active: false,
                                            },
                                            postError: {
                                                active: false,
                                            },
                                            roiActivated: {
                                                active: false,
                                            },
                                            summary: {
                                                active: false,
                                            },
                                            platformDisconnected: {
                                                active: false,
                                            },
                                        },
                                        web: {
                                            showFloatingToast: false,
                                            filters: {
                                                restaurantIds: [],
                                            },
                                            newReviews: {
                                                active: false,
                                            },
                                            reviewReplyReminder: {
                                                active: false,
                                            },
                                            specialHourReminder: {
                                                active: false,
                                            },
                                            postSuggestion: {
                                                active: false,
                                            },
                                            postError: {
                                                active: false,
                                            },
                                            newMessage: {
                                                active: false,
                                            },
                                            roiActivated: {
                                                active: false,
                                            },
                                            platformDisconnected: {
                                                active: false,
                                            },
                                            informationUpdateError: {
                                                active: false,
                                            },
                                        },
                                        mobile: {
                                            userDevicesTokens: ['token'],
                                            active: false,
                                            newMessage: {
                                                active: true,
                                                realtime: true,
                                                receivingWeekDays: [1, 2, 3, 4, 5, 6, 7],
                                            },
                                            newReviews: {
                                                active: true,
                                                realtime: true,
                                                receivingWeekDays: [1, 2, 3, 4, 5, 6, 7],
                                                concernedRatings: [1, 2, 3, 4, 5],
                                                includeAutoRepliedReviews: true,
                                            },
                                            noMoreScheduledPosts: {
                                                active: true,
                                            },
                                        },
                                    },
                                    notificationSettings: {
                                        userDevicesTokens: ['token'],
                                        active: false,
                                        reviews: {
                                            active: true,
                                            realtime: true,
                                            receivingWeekDays: [1, 2, 3, 4, 5, 6, 7],
                                            concernedRatings: [1, 2, 3, 4, 5],
                                            includeAutoRepliedReviews: true,
                                        },
                                        messages: {
                                            active: true,
                                            realtime: true,
                                            receivingWeekDays: [1, 2, 3, 4, 5, 6, 7],
                                        },
                                        posts: {
                                            noMoreScheduledPosts: {
                                                active: true,
                                                lastNotificationSentDate: new Date(),
                                            },
                                            publishError: {
                                                active: true,
                                            },
                                        },
                                    },
                                    receiveMessagesNotifications: {
                                        active: false,
                                        restaurantsIds: [],
                                    },
                                })
                                .build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),

                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[1]._id)
                                .caslRole(CaslRole.EDITOR)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[1]._id)
                                .caslRole(CaslRole.EDITOR)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[2]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[2]._id)
                                .restaurantId(dependencies.restaurants()[1]._id)
                                .caslRole(CaslRole.EDITOR)
                                .build(),
                        ];
                    },
                },
                conversations: {
                    data(deps) {
                        return [
                            getDefaultConversation().restaurantId(deps.restaurants()[0]._id).build(),
                            getDefaultConversation().restaurantId(deps.restaurants()[1]._id).build(),
                        ];
                    },
                },
                messages: {
                    data(deps) {
                        return [getDefaultMessage().conversationId(deps.conversations()[0]._id).isFromRestaurant(false).build()];
                    },
                },
            },
            expectedResult(_dependencies) {
                return [];
            },
        });

        await baseTestCase.build();
    });

    describe('execute', () => {
        const notificationsRepository = container.resolve(NotificationsRepository);
        const messagesRepository = container.resolve(MessagesRepository);
        const usersRepository = container.resolve(UsersRepository);
        it('should create a new notification for each user (with active notifications) with the new unread message', async () => {
            const usecase = container.resolve(CreateNewMessageNotificationUseCase);
            const [message] = baseTestCase.getSeededObjects().messages;
            assert(message.userInfo.displayName, '[CreateNewMessageNotificationUseCaseTest] Message userInfo is required');
            assert(message.text, '[CreateNewMessageNotificationUseCaseTest] Message text is required');
            assert(message.userInfo.profilePictureUrl, '[CreateNewMessageNotificationUseCaseTest] Message userInfo is required');

            const [conversation0] = baseTestCase.getSeededObjects().conversations;
            const [restaurant0] = baseTestCase.getSeededObjects().restaurants;
            const [user0, user1] = baseTestCase.getSeededObjects().users.map(usersRepository.toEntity);
            await usecase.execute({ messageId: message._id.toString() });

            const notifications = await notificationsRepository
                .find({ filter: {}, options: { lean: true, populate: [{ path: 'user' }] } })
                .then((result) =>
                    result.map((n) => new MessageNotification(notificationsRepository.toNotificationEntityData<MessageNotificationData>(n)))
                );

            const expectedNotifications = [
                new MessageNotification({
                    id: notifications[0].id,
                    user: NotificationUser.fromUser(user0),
                    data: {
                        restaurantIds: [restaurant0._id.toString()],
                        conversations: [
                            {
                                conversationId: conversation0._id.toString(),
                                restaurantId: restaurant0._id.toString(),
                                messages: [
                                    {
                                        id: message._id.toString(),
                                        senderName: message.userInfo.displayName,
                                        text: message.text,
                                        senderProfilePictureUrl: message.userInfo.profilePictureUrl,
                                    },
                                ],
                            },
                        ],
                        messageCount: 1,
                    },
                    channel: NotificationChannel.WEB,
                    type: NotificationType.NEW_MESSAGE,
                    completedAt: notifications[0].completedAt,
                    error: notifications[0].error,
                    readAt: notifications[0].readAt,
                    restaurantId: notifications[0].restaurantId,
                    userId: user0.id,
                    createdAt: notifications[0].createdAt,
                    updatedAt: notifications[0].updatedAt,
                }),
                new MessageNotification({
                    id: notifications[1].id,
                    user: NotificationUser.fromUser(user0),
                    data: {
                        restaurantIds: [restaurant0._id.toString()],
                        conversations: [
                            {
                                conversationId: conversation0._id.toString(),
                                restaurantId: restaurant0._id.toString(),
                                messages: [
                                    {
                                        id: message._id.toString(),
                                        senderName: message.userInfo.displayName,
                                        text: message.text,
                                        senderProfilePictureUrl: message.userInfo.profilePictureUrl,
                                    },
                                ],
                            },
                        ],
                        messageCount: 1,
                    },
                    channel: NotificationChannel.MOBILE,
                    type: NotificationType.NEW_MESSAGE,
                    userId: user0.id,
                    completedAt: notifications[1].completedAt,
                    error: notifications[1].error,
                    readAt: notifications[1].readAt,
                    restaurantId: notifications[1].restaurantId,
                    createdAt: notifications[1].createdAt,
                    updatedAt: notifications[1].updatedAt,
                }),
                new MessageNotification({
                    id: notifications[2].id,
                    user: NotificationUser.fromUser(user1),
                    data: {
                        restaurantIds: [restaurant0._id.toString()],
                        conversations: [
                            {
                                conversationId: conversation0._id.toString(),
                                restaurantId: restaurant0._id.toString(),
                                messages: [
                                    {
                                        id: message._id.toString(),
                                        senderName: message.userInfo.displayName,
                                        text: message.text,
                                        senderProfilePictureUrl: message.userInfo.profilePictureUrl,
                                    },
                                ],
                            },
                        ],
                        messageCount: 1,
                    },
                    channel: NotificationChannel.WEB,
                    type: NotificationType.NEW_MESSAGE,
                    completedAt: notifications[2].completedAt,
                    error: notifications[2].error,
                    readAt: notifications[2].readAt,
                    restaurantId: notifications[2].restaurantId,
                    userId: user1.id,
                    createdAt: notifications[2].createdAt,
                    updatedAt: notifications[2].updatedAt,
                }),
                new MessageNotification({
                    id: notifications[3].id,
                    user: NotificationUser.fromUser(user1),
                    data: {
                        restaurantIds: [restaurant0._id.toString()],
                        conversations: [
                            {
                                conversationId: conversation0._id.toString(),
                                restaurantId: restaurant0._id.toString(),
                                messages: [
                                    {
                                        id: message._id.toString(),
                                        senderName: message.userInfo.displayName,
                                        text: message.text,
                                        senderProfilePictureUrl: message.userInfo.profilePictureUrl,
                                    },
                                ],
                            },
                        ],
                        messageCount: 1,
                    },
                    channel: NotificationChannel.MOBILE,
                    type: NotificationType.NEW_MESSAGE,
                    completedAt: notifications[3].completedAt,
                    error: notifications[3].error,
                    readAt: notifications[3].readAt,
                    restaurantId: notifications[3].restaurantId,
                    userId: user1.id,
                    createdAt: notifications[3].createdAt,
                    updatedAt: notifications[3].updatedAt,
                }),
            ];
            expect(notifications).toEqual(expectedNotifications);
        });

        it('should update the latest existing new message notification with a new message', async () => {
            const usecase = container.resolve(CreateNewMessageNotificationUseCase);
            const [message] = baseTestCase.getSeededObjects().messages;

            assert(message.userInfo.displayName, '[CreateNewMessageNotificationUseCaseTest] Message userInfo is required');
            assert(message.text, '[CreateNewMessageNotificationUseCaseTest] Message text is required');
            assert(message.userInfo.profilePictureUrl, '[CreateNewMessageNotificationUseCaseTest] Message userInfo is required');

            const [conversation0] = baseTestCase.getSeededObjects().conversations;
            const [restaurant0] = baseTestCase.getSeededObjects().restaurants;
            const [user0, user1] = baseTestCase.getSeededObjects().users.map(usersRepository.toEntity);
            await usecase.execute({ messageId: message._id.toString() });

            let notifications = await notificationsRepository
                .find({ filter: {}, options: { lean: true, populate: [{ path: 'user' }] } })
                .then((result) =>
                    result.map((n) => new MessageNotification(notificationsRepository.toNotificationEntityData<MessageNotificationData>(n)))
                );
            expect(notifications).toHaveLength(4);

            const [conversation] = baseTestCase.getSeededObjects().conversations;
            const newMessage = await messagesRepository.create({
                data: getDefaultMessage().conversationId(conversation._id).isFromRestaurant(false).build(),
            });

            assert(newMessage.userInfo.displayName, '[CreateNewMessageNotificationUseCaseTest] Message userInfo is required');
            assert(newMessage.text, '[CreateNewMessageNotificationUseCaseTest] Message text is required');
            assert(newMessage.userInfo.profilePictureUrl, '[CreateNewMessageNotificationUseCaseTest] Message userInfo is required');

            await usecase.execute({ messageId: newMessage._id.toString() });
            notifications = await notificationsRepository
                .find({ filter: {}, options: { lean: true, populate: [{ path: 'user' }] } })
                .then((result) =>
                    result.map((n) => new MessageNotification(notificationsRepository.toNotificationEntityData<MessageNotificationData>(n)))
                );

            const expectedNotifications = [
                new MessageNotification({
                    id: notifications[0].id,
                    user: NotificationUser.fromUser(user0),
                    data: {
                        restaurantIds: [restaurant0._id.toString()],
                        conversations: [
                            {
                                conversationId: conversation0._id.toString(),
                                restaurantId: restaurant0._id.toString(),
                                messages: [
                                    {
                                        id: newMessage._id.toString(),
                                        senderName: newMessage.userInfo.displayName,
                                        text: newMessage.text,
                                        senderProfilePictureUrl: newMessage.userInfo.profilePictureUrl,
                                    },
                                ],
                            },
                        ],
                        messageCount: 2,
                    },
                    channel: NotificationChannel.WEB,
                    type: NotificationType.NEW_MESSAGE,
                    completedAt: notifications[0].completedAt,
                    error: notifications[0].error,
                    readAt: notifications[0].readAt,
                    restaurantId: notifications[0].restaurantId?.toString(),
                    userId: user0.id,
                    createdAt: notifications[0].createdAt,
                    updatedAt: notifications[0].updatedAt,
                }),
                new MessageNotification({
                    id: notifications[1].id,
                    user: NotificationUser.fromUser(user0),
                    data: {
                        restaurantIds: [restaurant0._id.toString()],
                        conversations: [
                            {
                                conversationId: conversation0._id.toString(),
                                restaurantId: restaurant0._id.toString(),
                                messages: [
                                    {
                                        id: newMessage._id.toString(),
                                        senderName: newMessage.userInfo.displayName,
                                        text: newMessage.text,
                                        senderProfilePictureUrl: newMessage.userInfo.profilePictureUrl,
                                    },
                                ],
                            },
                        ],
                        messageCount: 2,
                    },
                    channel: NotificationChannel.MOBILE,
                    type: NotificationType.NEW_MESSAGE,
                    completedAt: notifications[1].completedAt,
                    error: notifications[1].error,
                    readAt: notifications[1].readAt,
                    restaurantId: notifications[1].restaurantId?.toString(),
                    userId: user0.id,
                    createdAt: notifications[1].createdAt,
                    updatedAt: notifications[1].updatedAt,
                }),
                new MessageNotification({
                    id: notifications[2].id,
                    user: NotificationUser.fromUser(user1),
                    data: {
                        restaurantIds: [restaurant0._id.toString()],
                        conversations: [
                            {
                                conversationId: conversation0._id.toString(),
                                restaurantId: restaurant0._id.toString(),
                                messages: [
                                    {
                                        id: newMessage._id.toString(),
                                        senderName: newMessage.userInfo.displayName,
                                        text: newMessage.text,
                                        senderProfilePictureUrl: newMessage.userInfo.profilePictureUrl,
                                    },
                                ],
                            },
                        ],
                        messageCount: 2,
                    },
                    channel: NotificationChannel.WEB,
                    type: NotificationType.NEW_MESSAGE,
                    userId: user1.id,
                    completedAt: notifications[2].completedAt,
                    error: notifications[2].error,
                    readAt: notifications[2].readAt,
                    restaurantId: notifications[2].restaurantId?.toString(),
                    createdAt: notifications[2].createdAt,
                    updatedAt: notifications[2].updatedAt,
                }),
                new MessageNotification({
                    id: notifications[3].id,
                    user: NotificationUser.fromUser(user1),
                    data: {
                        restaurantIds: [restaurant0._id.toString()],
                        conversations: [
                            {
                                conversationId: conversation0._id.toString(),
                                restaurantId: restaurant0._id.toString(),
                                messages: [
                                    {
                                        id: newMessage._id.toString(),
                                        senderName: newMessage.userInfo.displayName,
                                        text: newMessage.text,
                                        senderProfilePictureUrl: newMessage.userInfo.profilePictureUrl,
                                    },
                                ],
                            },
                        ],
                        messageCount: 2,
                    },
                    channel: NotificationChannel.MOBILE,
                    type: NotificationType.NEW_MESSAGE,
                    userId: user1.id,
                    completedAt: notifications[3].completedAt,
                    error: notifications[3].error,
                    readAt: notifications[3].readAt,
                    restaurantId: notifications[3].restaurantId?.toString(),
                    createdAt: notifications[3].createdAt,
                    updatedAt: notifications[3].updatedAt,
                }),
            ];
            expect(notifications).toIncludeAllMembers(expectedNotifications);
        });

        it('should aggregate the new conversation messages to the existing notification if it is still unread', async () => {
            const usecase = container.resolve(CreateNewMessageNotificationUseCase);
            const [message] = baseTestCase.getSeededObjects().messages;

            assert(message.userInfo.displayName, '[CreateNewMessageNotificationUseCaseTest] Message userInfo is required');
            assert(message.text, '[CreateNewMessageNotificationUseCaseTest] Message text is required');
            assert(message.userInfo.profilePictureUrl, '[CreateNewMessageNotificationUseCaseTest] Message userInfo is required');

            const [_conversation0, conversation1] = baseTestCase.getSeededObjects().conversations;
            const [restaurant0, restaurant1] = baseTestCase.getSeededObjects().restaurants;
            const [user0, user1] = baseTestCase.getSeededObjects().users.map(usersRepository.toEntity);
            await usecase.execute({ messageId: message._id.toString() });

            let notifications = await notificationsRepository
                .find({ filter: {}, options: { lean: true, populate: [{ path: 'user' }] } })
                .then((result) =>
                    result.map((n) => new MessageNotification(notificationsRepository.toNotificationEntityData<MessageNotificationData>(n)))
                );
            expect(notifications).toHaveLength(4);

            const [_, conversation2] = baseTestCase.getSeededObjects().conversations;
            const newMessage = await messagesRepository.create({
                data: getDefaultMessage().conversationId(conversation2._id).isFromRestaurant(false).build(),
            });

            assert(newMessage.userInfo.displayName, '[CreateNewMessageNotificationUseCaseTest] Message userInfo is required');
            assert(newMessage.text, '[CreateNewMessageNotificationUseCaseTest] Message text is required');
            assert(newMessage.userInfo.profilePictureUrl, '[CreateNewMessageNotificationUseCaseTest] Message userInfo is required');

            await usecase.execute({ messageId: newMessage._id.toString() });

            notifications = await notificationsRepository
                .find({ filter: {}, options: { lean: true, populate: [{ path: 'user' }] } })
                .then((result) =>
                    result.map((n) => new MessageNotification(notificationsRepository.toNotificationEntityData<MessageNotificationData>(n)))
                );

            const expectedNotifications = [
                new MessageNotification({
                    id: notifications[0].id,
                    user: NotificationUser.fromUser(user0),
                    data: {
                        restaurantIds: [restaurant0._id.toString(), restaurant1._id.toString()],
                        conversations: [
                            {
                                conversationId: conversation1._id.toString(),
                                restaurantId: restaurant1._id.toString(),
                                messages: [
                                    {
                                        id: newMessage._id.toString(),
                                        senderName: newMessage.userInfo.displayName,
                                        text: newMessage.text,
                                        senderProfilePictureUrl: newMessage.userInfo.profilePictureUrl,
                                    },
                                ],
                            },
                        ],
                        messageCount: 2,
                    },
                    channel: NotificationChannel.WEB,
                    type: NotificationType.NEW_MESSAGE,
                    completedAt: notifications[0].completedAt,
                    error: notifications[0].error,
                    readAt: notifications[0].readAt,
                    restaurantId: notifications[0].restaurantId?.toString(),
                    userId: user0.id,
                    createdAt: notifications[0].createdAt,
                    updatedAt: notifications[0].updatedAt,
                }),
                new MessageNotification({
                    id: notifications[1].id,
                    user: NotificationUser.fromUser(user0),
                    data: {
                        restaurantIds: [restaurant0._id.toString(), restaurant1._id.toString()],
                        conversations: [
                            {
                                conversationId: conversation1._id.toString(),
                                restaurantId: restaurant1._id.toString(),
                                messages: [
                                    {
                                        id: newMessage._id.toString(),
                                        senderName: newMessage.userInfo.displayName,
                                        text: newMessage.text,
                                        senderProfilePictureUrl: newMessage.userInfo.profilePictureUrl,
                                    },
                                ],
                            },
                        ],
                        messageCount: 2,
                    },
                    channel: NotificationChannel.MOBILE,
                    type: NotificationType.NEW_MESSAGE,
                    completedAt: notifications[1].completedAt,
                    error: notifications[1].error,
                    readAt: notifications[1].readAt,
                    restaurantId: notifications[1].restaurantId?.toString(),
                    userId: user0.id,
                    createdAt: notifications[1].createdAt,
                    updatedAt: notifications[1].updatedAt,
                }),
                new MessageNotification({
                    id: notifications[2].id,
                    user: NotificationUser.fromUser(user1),
                    data: {
                        restaurantIds: [restaurant0._id.toString(), restaurant1._id.toString()],
                        conversations: [
                            {
                                conversationId: conversation1._id.toString(),
                                restaurantId: restaurant1._id.toString(),
                                messages: [
                                    {
                                        id: newMessage._id.toString(),
                                        senderName: newMessage.userInfo.displayName,
                                        text: newMessage.text,
                                        senderProfilePictureUrl: newMessage.userInfo.profilePictureUrl,
                                    },
                                ],
                            },
                        ],
                        messageCount: 2,
                    },
                    channel: NotificationChannel.WEB,
                    type: NotificationType.NEW_MESSAGE,
                    completedAt: notifications[2].completedAt,
                    error: notifications[2].error,
                    readAt: notifications[2].readAt,
                    restaurantId: notifications[2].restaurantId?.toString(),
                    userId: user1.id,
                    createdAt: notifications[2].createdAt,
                    updatedAt: notifications[2].updatedAt,
                }),
                new MessageNotification({
                    id: notifications[3].id,
                    user: NotificationUser.fromUser(user1),
                    data: {
                        restaurantIds: [restaurant0._id.toString(), restaurant1._id.toString()],
                        conversations: [
                            {
                                conversationId: conversation1._id.toString(),
                                restaurantId: restaurant1._id.toString(),
                                messages: [
                                    {
                                        id: newMessage._id.toString(),
                                        senderName: newMessage.userInfo.displayName,
                                        text: newMessage.text,
                                        senderProfilePictureUrl: newMessage.userInfo.profilePictureUrl,
                                    },
                                ],
                            },
                        ],
                        messageCount: 2,
                    },
                    channel: NotificationChannel.MOBILE,
                    type: NotificationType.NEW_MESSAGE,
                    completedAt: notifications[3].completedAt,
                    error: notifications[3].error,
                    readAt: notifications[3].readAt,
                    restaurantId: notifications[3].restaurantId?.toString(),
                    userId: user1.id,
                    createdAt: notifications[3].createdAt,
                    updatedAt: notifications[3].updatedAt,
                }),
            ];
            expect(notifications).toIncludeAllMembers(expectedNotifications);
        });
    });
});
