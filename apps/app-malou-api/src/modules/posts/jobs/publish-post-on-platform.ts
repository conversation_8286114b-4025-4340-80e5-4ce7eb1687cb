import { Job } from 'agenda';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import { objectIdValidator } from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';
import { TimeInMilliseconds } from '@malou-io/package-utils';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import { PublishPostOnPlatformUseCase } from ':modules/posts/v2/use-cases/publish-post-on-platform/publish-post-on-platform.use-case';

export const publishPostOnPlatformValidator = z.object({
    postId: objectIdValidator,
});
type DataAttributes = z.infer<typeof publishPostOnPlatformValidator>;

@singleton()
export class PublishPostOnPlatformJob extends GenericJobDefinition {
    constructor(
        private readonly _publishPostOnPlatformUseCase: PublishPostOnPlatformUseCase,
        private readonly _postsRepository: PostsRepository
    ) {
        super({
            agendaJobName: AgendaJobName.PUBLISH_POST_ON_PLATFORM,
            lockLifetimeMs: 15 * TimeInMilliseconds.MINUTE,
            getLogMetadata: async (job: Job<DataAttributes>) => {
                const data = publishPostOnPlatformValidator.parse(job.attrs.data);
                const { postId } = data;
                const { restaurantId, author } = await this._postsRepository.findOneOrFail({
                    filter: { _id: toDbId(postId) },
                    projection: { restaurantId: 1, author: 1 },
                    options: { lean: true },
                });

                assert(restaurantId, 'Missing restaurantId on post');
                assert(author, 'Missing author on post');

                return {
                    restaurant: { id: restaurantId.toString() },
                    user: { id: author._id.toString() },
                };
            },
        });
    }

    async executeJob(job: Job<DataAttributes>): Promise<void> {
        const data = publishPostOnPlatformValidator.parse(job.attrs.data);
        const { postId } = data;
        await this._publishPostOnPlatformUseCase.execute({ postId });
    }
}
