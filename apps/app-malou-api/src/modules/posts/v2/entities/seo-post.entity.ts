import { DateTime } from 'luxon';
import { v4 as uuidv4 } from 'uuid';

import { GetMediaForEditionResponseDto, SeoPostDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import {
    DeviceType,
    EntityConstructor,
    PlatformKey,
    PostErrorData,
    PostFeedbacks,
    PostPublicationStatus,
    PostType,
    roundUpToTheNext15MinuteInterval,
    SeoPostCallToAction,
    SeoPostTopic,
} from '@malou-io/package-utils';

import { PostAuthor } from ':modules/posts/v2/entities/author.entity';

export type SeoPostMedia = GetMediaForEditionResponseDto;

export type SeoPostProps = EntityConstructor<SeoPost> & { id: string };

interface SeoPostEvent {
    title?: string;
    startDate?: Date;
    endDate?: Date;
}

interface SeoPostOffer {
    couponCode?: string;
    onlineUrl?: string;
    termsConditions?: string;
}

export class SeoPost {
    id: string;
    text: string;
    key: PlatformKey.GMB;
    published: PostPublicationStatus;
    postType: PostType;
    postTopic: SeoPostTopic;
    event?: SeoPostEvent;
    offer?: SeoPostOffer;
    plannedPublicationDate: Date;
    attachments: SeoPostMedia[];
    callToAction?: SeoPostCallToAction;
    feedbacks?: PostFeedbacks;
    error?: PostErrorData;
    socialLink?: string;
    socialCreatedAt?: Date;
    author?: PostAuthor;
    bindingId?: string;
    createdFromDeviceType?: DeviceType;

    constructor(data: SeoPostProps) {
        this.id = data.id;
        this.text = data.text;
        this.key = data.key;
        this.published = data.published;
        this.postType = data.postType;
        this.postTopic = data.postTopic;
        this.event = data.event;
        this.offer = data.offer;
        this.plannedPublicationDate = data.plannedPublicationDate;
        this.attachments = data.attachments;
        this.feedbacks = data.feedbacks;
        this.callToAction = data.callToAction;
        this.error = data.error;
        this.socialLink = data.socialLink;
        this.socialCreatedAt = data.socialCreatedAt;
        this.author = data.author;
        this.bindingId = data.bindingId;
        this.createdFromDeviceType = data.createdFromDeviceType;
    }

    toDto(): SeoPostDto {
        return {
            id: this.id,
            text: this.text ?? '',
            key: this.key,
            published: this.published,
            postType: this.postType,
            postTopic: this.postTopic,
            event: this.event
                ? {
                      title: this.event.title,
                      startDate: this.event.startDate?.toISOString(),
                      endDate: this.event.endDate?.toISOString(),
                  }
                : undefined,
            offer: this.offer,
            plannedPublicationDate: this.plannedPublicationDate?.toISOString(),
            attachments: this.attachments,
            callToAction: this.callToAction
                ? {
                      actionType: this.callToAction.actionType,
                      url: this.callToAction.url,
                  }
                : undefined,
            feedbacks: this.feedbacks
                ? {
                      id: this.feedbacks.id,
                      isOpen: this.feedbacks.isOpen,
                      participants: this.feedbacks.participants,
                      feedbackMessages: this.feedbacks.feedbackMessages.map((message) => ({
                          id: message.id,
                          type: message.type,
                          visibility: message.visibility,
                          text: message.text,
                          author: message.author,
                          createdAt: message.createdAt.toISOString(),
                          updatedAt: message.updatedAt.toISOString(),
                      })),
                      createdAt: this.feedbacks.createdAt.toISOString(),
                      updatedAt: this.feedbacks.updatedAt.toISOString(),
                  }
                : null,
            error: this.error,
            socialLink: this.socialLink,
            socialCreatedAt: this.socialCreatedAt?.toISOString(),
            author: this.author?.toDto(),
            createdFromDeviceType: this.createdFromDeviceType,
        };
    }

    copyWith(data: Partial<SeoPostProps>): SeoPost {
        return new SeoPost({
            id: data.id ?? this.id,
            text: data.text ?? this.text,
            key: data.key ?? this.key,
            published: data.published ?? this.published,
            postType: data.postType ?? this.postType,
            postTopic: data.postTopic ?? this.postTopic,
            event: data.event ?? this.event,
            offer: data.offer ?? this.offer,
            plannedPublicationDate: data.plannedPublicationDate ?? this.plannedPublicationDate,
            attachments: data.attachments ?? this.attachments,
            callToAction: data.callToAction ?? this.callToAction,
            feedbacks: data.feedbacks ?? this.feedbacks,
            error: data.error ?? this.error,
            socialLink: data.socialLink ?? this.socialLink,
            socialCreatedAt: data.socialCreatedAt ?? this.socialCreatedAt,
            author: data.author ?? this.author,
            bindingId: data.bindingId ?? this.bindingId,
            createdFromDeviceType: data.createdFromDeviceType ?? this.createdFromDeviceType,
        });
    }

    static createEmpty(author: PostAuthor): SeoPost {
        const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();
        const plannedPublicationDate = roundUpToTheNext15MinuteInterval(tomorrow);

        return new SeoPost({
            id: newDbId().toString(),
            text: '',
            key: PlatformKey.GMB,
            published: PostPublicationStatus.DRAFT,
            postType: PostType.IMAGE,
            postTopic: SeoPostTopic.STANDARD,
            plannedPublicationDate,
            attachments: [],
            author,
            bindingId: uuidv4(),
        });
    }
}
