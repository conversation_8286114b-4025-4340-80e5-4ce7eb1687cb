import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IPopulatedPost } from '@malou-io/package-models';
import { errorReplacer, removeAndAddHashtagsToText, RetryError, retryResult } from '@malou-io/package-utils';

import { Platform } from ':modules/platforms/platforms.entity';
import { MetaGraphApiCredentialsHandlerErrorCodes } from ':modules/posts/v2/providers/meta/meta-graph-api-credentials-handler/meta-graph-api-credentials-handler.definitions';
import { MetaGraphApiHelper } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper';
import {
    MetaGraphApiHelperEndpoint,
    MetaGraphApiHelperErrorObject,
} from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper.definitions';

// TODO posts-v2 write a test for this
@singleton()
export class PublishVideoPostOnFacebookService {
    constructor(private readonly _metaGraphApiHelper: MetaGraphApiHelper) {}

    async execute(
        post: IPopulatedPost,
        platform: Platform,
        credentialId: string,
        videoUrl: string
    ): Promise<Result<{ pagePostId: string }, MetaGraphApiHelperErrorObject>> {
        const pageId = platform.socialId;
        assert(pageId, 'Missing socialId on platform');

        const selectedHashtagTexts = post.hashtags?.selected?.map((hashtag) => hashtag.text) ?? [];
        const textWithHashtags = removeAndAddHashtagsToText(post.text ?? '', selectedHashtagTexts);

        const videoIdRes = await this._metaGraphApiHelper.publishFbVideoPost(
            credentialId,
            pageId,
            {
                text: textWithHashtags ?? '',
            },
            videoUrl
        );
        if (videoIdRes.isErr()) {
            return err(videoIdRes.error);
        }

        const getVideoRes = await retryResult(() => this._metaGraphApiHelper.getFbVideo(credentialId, pageId, videoIdRes.value.videoId), {
            attempts: 10,
            backoffStrategy: 'exponential',
            isSuccess: (res) => res.status.video_status === 'ready',
            shouldRetrySuccess: (res) => res.status.video_status !== 'error',
        });
        if (getVideoRes.isErr()) {
            if (getVideoRes.error.error === RetryError.SHOULD_NOT_RETRY_AFTER_SUCCESS) {
                return err({
                    endpoint: MetaGraphApiHelperEndpoint.GET_FB_VIDEO,
                    code: MetaGraphApiCredentialsHandlerErrorCodes.UNKNOWN_ERROR,
                    stringifiedRawError: JSON.stringify(getVideoRes.error, errorReplacer),
                });
            }
            assert(getVideoRes.error.error === RetryError.STILL_ERROR_AFTER_RETRIES);
            assert(getVideoRes.error.lastResult.isErr());
            return err(getVideoRes.error.lastResult.error);
        }

        // In v1 we directly use the videoId instead of post_id, that works, but it's not referenced in the documentation
        const pagePostId = `${pageId}_${getVideoRes.value.post_id}`;

        return ok({ pagePostId });
    }
}
