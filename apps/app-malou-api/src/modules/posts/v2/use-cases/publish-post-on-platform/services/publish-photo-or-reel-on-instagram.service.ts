import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IPopulatedPost } from '@malou-io/package-models';
import { errorReplacer, removeAndAddHashtagsToText, RetryError, retryR<PERSON>ult } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { Platform } from ':modules/platforms/platforms.entity';
import { MetaGraphApiCredentialsHandlerErrorCodes } from ':modules/posts/v2/providers/meta/meta-graph-api-credentials-handler/meta-graph-api-credentials-handler.definitions';
import { MetaGraphApiHelper } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper';
import {
    MetaGraphApiHelperEndpoint,
    MetaGraphApiHelperErrorObject,
} from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper.definitions';

// TODO posts-v2 write a test for this
@singleton()
export class PublishPhotoOrReelOnInstagramService {
    constructor(private readonly _metaGraphApiHelper: MetaGraphApiHelper) {}

    async execute(
        post: IPopulatedPost,
        platform: Platform,
        credentialId: string,
        options: { type: 'photo'; photoUrl: string } | { type: 'reel'; videoUrl: string; thumbnailUrl: string | undefined }
    ): Promise<Result<{ mediaId: string }, MetaGraphApiHelperErrorObject>> {
        const igUserId = platform.socialId;
        assert(igUserId, 'Missing socialId on platform');

        const selectedHashtagTexts = post.hashtags?.selected?.map((hashtag) => hashtag.text) ?? [];
        const textWithHashtags = removeAndAddHashtagsToText(post.text ?? '', selectedHashtagTexts);

        let containerIdRes: Result<{ containerId: string }, MetaGraphApiHelperErrorObject> | undefined;
        if (options.type === 'photo') {
            containerIdRes = await this._metaGraphApiHelper.createIgPhotoContainer(
                credentialId,
                igUserId,
                options.photoUrl,
                textWithHashtags,
                post.location?.id,
                post.userTagsList?.[0] ?? undefined,
                post.instagramCollaboratorsUsernames
            );
        } else if (options.type === 'reel') {
            containerIdRes = await this._metaGraphApiHelper.createIgReelContainer({
                credentialId,
                igUserId,
                videoUrl: options.videoUrl,
                coverUrl: options.thumbnailUrl,
                caption: textWithHashtags,
                locationId: post.location?.id,
                userTags: post.userTagsList?.[0] ?? undefined,
                collaboratorsUsernames: post.instagramCollaboratorsUsernames,
            });
        }
        assert(containerIdRes);

        if (containerIdRes.isErr()) {
            return err(containerIdRes.error);
        }

        const containerStatusRes = await retryResult(
            () => this._metaGraphApiHelper.getIgContainer(credentialId, igUserId, containerIdRes.value.containerId),
            {
                attempts: 10,
                backoffStrategy: 'exponential',
                isSuccess: (res) => res.status_code === 'FINISHED',
                shouldRetrySuccess: (res) => !['ERROR', 'EXPIRED'].includes(res.status_code),
            }
        );
        if (containerStatusRes.isErr()) {
            if (containerStatusRes.error.error === RetryError.SHOULD_NOT_RETRY_AFTER_SUCCESS) {
                logger.info('[PublishPhotoOrReelOnInstagramService] getIgContainer failed', containerStatusRes.error);
                return err({
                    endpoint: MetaGraphApiHelperEndpoint.GET_IG_CONTAINER,
                    code: MetaGraphApiCredentialsHandlerErrorCodes.UNKNOWN_ERROR,
                    stringifiedRawError: JSON.stringify(containerStatusRes.error, errorReplacer),
                });
            }
            assert(containerStatusRes.error.error === RetryError.STILL_ERROR_AFTER_RETRIES);
            assert(containerStatusRes.error.lastResult.isErr());
            return err(containerStatusRes.error.lastResult.error);
        }

        const mediaIdRes = await this._metaGraphApiHelper.publishIgContainer(credentialId, igUserId, containerIdRes.value.containerId);

        if (mediaIdRes.isErr()) {
            return err(mediaIdRes.error);
        }

        return ok({ mediaId: mediaIdRes.value.mediaId });
    }
}
