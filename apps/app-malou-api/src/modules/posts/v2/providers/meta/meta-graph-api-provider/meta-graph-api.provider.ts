import axios, { AxiosResponse } from 'axios';
import { Err, err, ok, Result } from 'neverthrow';
import { singleton } from 'tsyringe';
import { ZodType } from 'zod';

import { errorReplacer } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import {
    IMetaGraphApiError,
    MetaApiErrorCode,
    MetaApiErrorSubcode,
    MetaGraphApiError,
    MetaGraphApiErrorObject,
    metaGraphApiErrorResponseValidator,
    MetaGraphApiRequestOptions,
    metaIgErrorMapToMetaGraphApiError,
    tokenRelatedErrorCodes,
    tokenRelatedErrorSubCodes,
} from ':modules/posts/v2/providers/meta/meta-graph-api-provider/meta-graph-api.definitions';

@singleton()
export class MetaGraphApiProvider {
    async callApi<T>(params: {
        responseValidator: ZodType<T>;
        token: string;
        requestOptions: MetaGraphApiRequestOptions;
    }): Promise<Result<T, MetaGraphApiErrorObject>> {
        logger.info('[MetaGraphApiProvider.callApi] Start', {
            requestOptions: params.requestOptions,
            responseValidatorDescription: params.responseValidator.description,
        });
        let res: AxiosResponse;
        try {
            res = await axios({
                method: params.requestOptions.method,
                baseURL: 'https://' + (params.requestOptions.hostname ?? 'graph.facebook.com'),
                url: params.requestOptions.endpoint,
                params: { ...params.requestOptions.queryParams, access_token: params.token },
                headers: params.requestOptions.headers ?? {},
            });
        } catch (error: unknown) {
            logger.error('[MetaGraphApiProvider.callApi] Error', { error });
            return this._handleError(error);
        }

        logger.info('[MetaGraphApiProvider.callApi] OK', { res: this._stringifyResultWithLimit(res.data) });
        const validatedResData = params.responseValidator.safeParse(res.data);
        if (!validatedResData.success) {
            logger.error('[MetaGraphApiProvider.callApi] Error CANNOT_VALIDATE_RESPONSE', {
                error: validatedResData.error,
                response: res.data,
            });
            return err({
                code: MetaGraphApiError.CANNOT_VALIDATE_RESPONSE,
                stringifiedRawError: JSON.stringify(validatedResData.error), // Do not use errorReplacer for ZodError
            });
        }
        return ok(validatedResData.data);
    }

    private _handleError(error: unknown): Err<never, MetaGraphApiErrorObject> {
        let stringifiedRawError = JSON.stringify(error, errorReplacer);
        if (axios.isAxiosError(error)) {
            stringifiedRawError = JSON.stringify(
                { data: error.response?.data, status: error.response?.status, statusText: error.response?.statusText },
                errorReplacer
            );
            const errorResponse = metaGraphApiErrorResponseValidator.safeParse(error.response?.data);
            if (!errorResponse.success) {
                logger.error('[MetaGraphApiProvider.callApi] Error CANNOT_VALIDATE_ERROR_RESPONSE', {
                    stringifiedRawError,
                    error: errorResponse.error,
                });
                return err({
                    code: MetaGraphApiError.CANNOT_VALIDATE_ERROR_RESPONSE,
                    stringifiedRawError: JSON.stringify(errorResponse.error), // Do not use errorReplacer for ZodError,
                });
            }
            const code = errorResponse.data.error.code;
            const subCode = errorResponse.data.error.error_subcode;
            if (code === MetaApiErrorCode.TOKEN_EXPIRED && subCode === MetaApiErrorSubcode.USER_CHECKPOINT) {
                logger.error('[MetaGraphApiProvider.callApi] Error USER_NEEDS_TO_LOG_IN');
                return err({ code: MetaGraphApiError.USER_NEEDS_TO_LOG_IN, stringifiedRawError });
            }
            if (code === MetaApiErrorCode.TOKEN_EXPIRED && subCode === MetaApiErrorSubcode.INVALID_SESSION) {
                logger.error('[MetaGraphApiProvider.callApi] Error USER_MISSING_APPROPRIATE_ROLE');
                return err({ code: MetaGraphApiError.USER_MISSING_APPROPRIATE_ROLE, stringifiedRawError });
            }
            if (subCode === MetaApiErrorSubcode.MEDIA_PUBLISHED_BEFORE_BUSINESS_ACCOUNT_CONVERSION) {
                logger.error('[MetaGraphApiProvider.callApi] Error MEDIA_PUBLISHED_BEFORE_BUSINESS_ACCOUNT_CONVERSION');
                return err({
                    code: MetaGraphApiError.MEDIA_PUBLISHED_BEFORE_BUSINESS_ACCOUNT_CONVERSION,
                    stringifiedRawError,
                });
            }
            if (subCode === MetaApiErrorSubcode.INVALID_LOCATION) {
                logger.error('[MetaGraphApiProvider.callApi] Error INVALID_LOCATION');
                return err({ code: MetaGraphApiError.INVALID_LOCATION, stringifiedRawError });
            }
            if ((code && tokenRelatedErrorCodes.includes(code)) || (subCode && tokenRelatedErrorSubCodes.includes(subCode))) {
                logger.error('[MetaGraphApiProvider.callApi] Error INVALID_TOKEN');
                return err({ code: MetaGraphApiError.INVALID_TOKEN, stringifiedRawError });
            }
            const igError = this._getAssociatedMetaGraphApiErrorFromIgError(code, subCode);
            if (igError) {
                return err({
                    code: igError,
                    stringifiedRawError,
                });
            }
        }
        logger.error('[MetaGraphApiProvider.callApi] Error UNKNOWN_ERROR');
        return err({ code: MetaGraphApiError.UNKNOWN_ERROR, stringifiedRawError });
    }

    private _stringifyResultWithLimit(res: any): string {
        try {
            return JSON.stringify(res).substring(0, 500);
        } catch (error) {
            logger.warn('_stringifyResultWithLimit', error);
            return 'error stringify result';
        }
    }

    private _getAssociatedMetaGraphApiErrorFromIgError(
        code: number | undefined,
        subcode: number | undefined
    ): IMetaGraphApiError | undefined {
        const entries = Object.entries(metaIgErrorMapToMetaGraphApiError) as [IMetaGraphApiError, { code: number; subcode: number }][];
        return entries.find(([_key, value]) => value.code === code && value.subcode === subcode)?.[0];
    }
}
