import { singleton } from 'tsyringe';

import { OrganizationUserDto, SearchUserDto, SearchUsersQueryDto } from '@malou-io/package-dto';
import { ID, IUser, ReadPreferenceMode } from '@malou-io/package-models';

import { UsersRepository } from ':modules/users/users.repository';

@singleton()
export class SearchUsersUseCase {
    constructor(private readonly _usersRepository: UsersRepository) {}

    async execute(params: SearchUsersQueryDto): Promise<{ data: Partial<OrganizationUserDto>[]; total: number }> {
        const { text, fields = [], limit, offset } = params;

        const filter: any = {};

        if (text?.trim()) {
            const searchText = text.trim();
            const searchTerms = searchText.split(/\s+/).filter((term) => term.length > 0);

            filter.$or = [
                { email: { $regex: searchTerms.join('|'), $options: 'i' } },
                { name: { $regex: searchTerms.join('|'), $options: 'i' } },
                { lastname: { $regex: searchTerms.join('|'), $options: 'i' } },
            ];
        }

        const options: any = {
            populate: [
                {
                    path: 'organizations',
                },
                {
                    path: 'profilePicture',
                },
            ],
            lean: true,
            sort: {
                createdAt: -1,
                email: 1,
            },
            readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
        };

        if (limit !== undefined) {
            options.limit = limit;
        }
        if (offset !== undefined) {
            options.skip = offset;
        }

        const projection =
            fields.length > 0
                ? fields.reduce((acc, field) => ({ ...acc, [field]: 1 }), { _id: 1 })
                : {
                      _id: 1,
                      email: 1,
                      name: 1,
                      lastname: 1,
                      role: 1,
                      organizationIds: 1,
                      defaultLanguage: 1,
                      createdAt: 1,
                      updatedAt: 1,
                  };

        const [users, totalCount] = await Promise.all([
            this._usersRepository.find({
                filter,
                projection,
                options,
            }),
            this._usersRepository.countDocuments({ filter }),
        ]);

        return {
            data: users.map((u) => this._toDto(u)),
            total: totalCount,
        };
    }

    private _toDto(user: Partial<IUser> & { _id: ID }): SearchUserDto {
        return {
            _id: user._id.toString(),
            name: user.name,
            lastname: user.lastname,
            email: user.email,
            role: user.role,
            defaultLanguage: user.defaultLanguage,
        };
    }
}
