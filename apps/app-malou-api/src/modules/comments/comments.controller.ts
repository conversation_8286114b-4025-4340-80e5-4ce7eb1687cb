import { ForbiddenError, subject } from '@casl/ability';
import { NextFunction, Request, Response } from 'express';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import {
    GetCommentAndMentionUnansweredCountParamsDto,
    getCommentAndMentionUnansweredCountParamsValidator,
    GetCommentAndMentionUnansweredCountQueryDto,
    getCommentAndMentionUnansweredCountQueryValidator,
    GetCommentAndMentionUnansweredCountResponseDto,
    GetCommentsAndMentionsByPostSocialIdParamsDto,
    getCommentsAndMentionsByPostSocialIdParamsValidator,
    GetCommentsByFiltersBodyDto,
    getCommentsByFiltersBodyValidator,
    RestaurantIdParamsTransformDto,
    restaurantIdParamsTransformValidator,
    restaurantIdParamsValidator,
} from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';
import { ApiResult, CaslAction, CaslSubject, CommentDisplayMode, MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Body, Params, Query } from ':helpers/decorators/validators';
import { CommentsFilters } from ':helpers/filters/comments-filters';
import { Pagination } from ':helpers/pagination';
import { RequestWithPermissions, RequestWithUser } from ':helpers/utils.types';
import { GetCommentAndMentionUnansweredCountUseCase } from ':modules/comments/use-cases/get-comments-and-mention-unanswered-count/get-comments-and-mention-unanswered-count.use-case';
import { GetCommentsAndMentionsByPostSocialIdUseCase } from ':modules/comments/use-cases/get-comments-and-mentions-by-post-social-id/get-comments-and-mentions-by-post-social-id.use-case';
import { GetCommentsAndMentionsListUseCase } from ':modules/comments/use-cases/get-comments-and-mentions-list/get-comments-and-mentions-list.use-case';
import { GetCommentsByPostUseCase } from ':modules/comments/use-cases/get-comments-by-post/get-comments-by-post.use-case';

import { CommentsUseCases } from './comments.use-cases';

enum CommentOrigin {
    COMMENT = 'comment',
    MENTION_IN_COMMENT = 'mention_in_comment',
    MENTION_IN_POST = 'mention_in_post',
}

@singleton()
export default class CommentsController {
    constructor(
        private _commentsUseCases: CommentsUseCases,
        private readonly _getCommentsAndMentionsListUsecase: GetCommentsAndMentionsListUseCase,
        private readonly _getCommentAndMentionUnansweredCountUseCase: GetCommentAndMentionUnansweredCountUseCase,
        private readonly _getCommentsByPostUseCase: GetCommentsByPostUseCase,
        private readonly _getCommentsAndMentionsByPostSocialIdUseCase: GetCommentsAndMentionsByPostSocialIdUseCase
    ) {}

    @Params(restaurantIdParamsTransformValidator)
    @Body(getCommentsByFiltersBodyValidator)
    async handleGetCommentsByPosts(
        req: Request<RestaurantIdParamsTransformDto, any, GetCommentsByFiltersBodyDto>,
        // TODO: add the proper dto to the response [@hamza]
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { filters, pagination: paginationPayload } = req.body;
            assert(filters, 'Filters are required');
            assert(paginationPayload, 'Pagination is required');

            const pagination = new Pagination({
                pageNumber: paginationPayload.pageNumber,
                pageSize: paginationPayload.pageSize,
                total: paginationPayload.total,
            });

            const { posts, total: actualTotal } = await this._getCommentsByPostUseCase.execute(restaurantId, filters, pagination);
            pagination.total = actualTotal;
            return res.status(200).json({
                data: {
                    posts,
                    pagination,
                },
            });
        } catch (err) {
            next(err);
        }
    }

    @Params(getCommentAndMentionUnansweredCountParamsValidator)
    @Query(getCommentAndMentionUnansweredCountQueryValidator)
    async handleGetCommentAndMentionUnansweredCount(
        req: Request<GetCommentAndMentionUnansweredCountParamsDto, any, any, GetCommentAndMentionUnansweredCountQueryDto>,
        res: Response<ApiResult<GetCommentAndMentionUnansweredCountResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { startDate } = req.query;
            const { unansweredCommentCount, unansweredMentionCount } = await this._getCommentAndMentionUnansweredCountUseCase.execute(
                restaurantId,
                startDate
            );
            return res.status(200).json({
                data: {
                    unansweredCommentCount,
                    unansweredMentionCount,
                },
            });
        } catch (err) {
            next(err);
        }
    }

    @Params(restaurantIdParamsTransformValidator)
    @Body(getCommentsByFiltersBodyValidator)
    async handleGetCommentsAndMentionsList(
        req: Request<RestaurantIdParamsTransformDto, any, GetCommentsByFiltersBodyDto>,
        // TODO: add the proper dto to the response [@hamza]
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { filters, pagination: paginationPayload } = req.body;
            assert(filters, 'Filters are required');
            assert(paginationPayload, 'Pagination is required');

            const pagination = new Pagination({
                pageNumber: paginationPayload.pageNumber,
                pageSize: paginationPayload.pageSize,
                total: paginationPayload.total,
            });

            const { comments, totalCount, commentCount, mentionCount } = await this._getCommentsAndMentionsListUsecase.execute(
                restaurantId,
                filters,
                pagination
            );
            pagination.total = totalCount;
            const counts = {
                commentCount,
                mentionCount,
            };
            return res.status(200).json({
                data: {
                    comments,
                    pagination,
                    counts,
                },
            });
        } catch (err) {
            next(err);
        }
    }

    handleGetCommentById = async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { comment_id: commentId } = req.params;
            const comment = await this._commentsUseCases.getCommentById(toDbId(commentId));
            return res.status(200).json({
                data: {
                    comment,
                },
                msg: 'Comment Retrieved',
            });
        } catch (err) {
            next(err);
        }
    };

    @Params(getCommentsAndMentionsByPostSocialIdParamsValidator)
    @Body(getCommentsByFiltersBodyValidator)
    async handleGetCommentsAndMentionsByPostSocialId(
        req: Request<GetCommentsAndMentionsByPostSocialIdParamsDto, any, GetCommentsByFiltersBodyDto>,
        // TODO: add the proper dto to the response [@hamza]
        res: Response,
        next: NextFunction
    ) {
        try {
            const { postSocialId, restaurantId } = req.params;
            const { filters } = req.body;
            assert(filters, 'Filters are required');

            const fullFilters = {
                ...filters,
                restaurantId,
                postSocialId,
            };
            const commentsAndMentions = await this._getCommentsAndMentionsByPostSocialIdUseCase.execute(
                restaurantId,
                postSocialId,
                fullFilters
            );
            return res.status(200).json({
                data: commentsAndMentions,
            });
        } catch (err) {
            next(err);
        }
    }

    handleGetCommentsPaginated = async (req: Request, res: Response, next: NextFunction) => {
        try {
            const restaurantId = req.params.restaurant_id;
            const {
                page_number: pageNumber,
                page_size: pageSize,
                total,
                text,
                sort_order: sortOrder,
                sort_by: sortBy,
                end_date: endDate,
                start_date: startDate,
                platforms,
                answered,
                not_answered: notAnswered,
                without_own_comment: withoutOwnComment,
                archived,
                display_mode: displayMode = CommentDisplayMode.LIST,
            } = req.query as any;
            const pagination = new Pagination({ pageNumber, pageSize, total });
            const filters = new CommentsFilters({
                text,
                sortOrder,
                sortBy,
                startDate,
                endDate,
                platforms,
                answered,
                notAnswered,
                withoutOwnComment,
                archived,
                restaurantId,
            });
            const pipeline = await this._commentsUseCases.buildPipeline(
                restaurantId,
                filters,
                displayMode === CommentDisplayMode.POST ? null : pagination,
                {
                    answered,
                    notAnswered,
                    withoutOwnComment,
                    sortBy,
                    sortOrder,
                }
            );
            const [result] = await this._commentsUseCases.getRestaurantCommentsPaginated(pipeline);
            const { comments, totalCount } = result;
            let count = 0;
            if (totalCount?.[0]) {
                ({ count } = totalCount[0]);
            }
            pagination.total = displayMode === CommentDisplayMode.POST ? this._commentsUseCases.getTotalPostsCount(comments) : count;
            return res.json({
                data: {
                    comments:
                        displayMode === CommentDisplayMode.POST
                            ? this._commentsUseCases.paginateCommentsPostMode(comments, pagination)
                            : comments,
                    pagination,
                },
            });
        } catch (err) {
            return next(err);
        }
    };

    handleGetPostsComments = async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { post_social_id: postSocialId, platform_id: platformId } = req.params;
            const {
                text,
                sort_order: sortOrder,
                sort_by: sortBy,
                end_date: endDate,
                start_date: startDate,
                platforms,
                answered,
                not_answered: notAnswered,
                without_own_comment: withoutOwnComment,
                archived,
            } = req.query as any;
            const post = await this._commentsUseCases.getPostForComments(postSocialId, toDbId(platformId));
            assert(post, 'Post not found');
            const filters = new CommentsFilters({
                text,
                sortOrder,
                sortBy,
                startDate,
                endDate,
                platforms,
                answered,
                notAnswered,
                withoutOwnComment,
                archived,
                postSocialId,
                restaurantId: post.restaurantId,
            });
            const pipeline = await this._commentsUseCases.buildPipeline(post.restaurantId, filters, null, {
                answered,
                notAnswered,
                withoutOwnComment,
                sortBy,
                sortOrder,
            });
            const [result] = await this._commentsUseCases.getRestaurantCommentsPaginated(pipeline);
            const { comments } = result;
            return res.json({ msg: 'Comments retrieved', data: comments });
        } catch (err) {
            next(err);
        }
    };

    handleReplyComment = async (req: RequestWithPermissions, res: Response, next: NextFunction) => {
        try {
            const { restaurantId } = req.query;
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.UPDATE,
                subject(CaslSubject.COMMENT, { restaurantId })
            );
            const { user } = req;
            assert(user, 'User not found');
            const { comment_id: commentId } = req.params;
            const { message } = req.body;
            const reply = await this._commentsUseCases.reply({ user, commentId, message });
            const comment = await this._commentsUseCases.refreshComment(commentId, reply.id, user._id);
            return res.json({ msg: 'Reply published.', data: comment });
        } catch (err) {
            next(err);
        }
    };

    @Params(restaurantIdParamsValidator)
    async handleSynchronizeComments(req: RequestWithUser, res: Response, next: NextFunction) {
        try {
            const { restaurant_id: restaurantId } = req.params;
            await this._commentsUseCases.startUpdateCommentsForRestaurant(restaurantId);
            return res.json({ msg: 'Started synchronization' });
        } catch (err) {
            next(err);
        }
    }

    handleUpdateComment = async (req: RequestWithPermissions, res: Response, next: NextFunction) => {
        try {
            const { restaurantId } = req.query;
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.UPDATE,
                subject(CaslSubject.COMMENT, { restaurantId })
            );
            const { comment_id: commentId } = req.params;
            const { comment } = req.body;
            let updatedComment;

            if (comment.type === CommentOrigin.COMMENT) {
                updatedComment = await this._commentsUseCases.updateCommentById(commentId, comment);
            } else if ([CommentOrigin.MENTION_IN_POST, CommentOrigin.MENTION_IN_COMMENT].includes(comment.type)) {
                updatedComment = await this._commentsUseCases.updateMentionById(commentId, comment);
            } else {
                throw new MalouError(MalouErrorCode.COMMENT_INVALID_TYPE);
            }

            return res.json({ data: updatedComment });
        } catch (err) {
            next(err);
        }
    };

    handleDeleteComment = async (req: RequestWithPermissions, res: Response, next: NextFunction) => {
        try {
            const { restaurantId } = req.query;
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.UPDATE,
                subject(CaslSubject.COMMENT, { restaurantId })
            );
            const { comment_id: commentId } = req.params;
            await this._commentsUseCases.deleteCommentById(commentId);
            return res.json({ message: 'comment deleted' });
        } catch (err) {
            next(err);
        }
    };

    handleArchiveAllCommentsFromPost = async (req: RequestWithPermissions, res: Response, next: NextFunction) => {
        try {
            const { post_id: postId, restaurant_id: restaurantId } = req.params;
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.UPDATE,
                subject(CaslSubject.COMMENT, { restaurantId })
            );
            const { archived } = req.body;
            const commentsUpdated = await this._commentsUseCases.toggleArchivedCommentsForPost(postId, archived, toDbId(restaurantId));
            return res.json({ message: 'Comments updated', data: commentsUpdated });
        } catch (err) {
            next(err);
        }
    };
}
