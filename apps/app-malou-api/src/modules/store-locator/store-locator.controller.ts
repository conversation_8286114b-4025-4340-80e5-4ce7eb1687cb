import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import {
    CheckForStoreLocatorRestaurantPagesParamsDto,
    checkForStoreLocatorRestaurantPagesParamsValidator,
    CheckForStoreLocatorRestaurantPagesResponseDto,
    GenerateStoreLocatorContentResponseDto,
    GenerateStoreLocatorStorePageContentBodyDto,
    generateStoreLocatorStorePageContentBodyValidator,
    GetOrganizationConfigurationParamsDto,
    getOrganizationConfigurationParamsValidator,
    GetStoreLocatorDraftPagesDto,
    GetStoreLocatorOrganizationConfigurationDto,
    GetStoreLocatorOrganizationJobResponseDto,
    GetStoreLocatorOrganizationJobsParamsDto,
    getStoreLocatorOrganizationJobsParamsValidator,
    GetStoreLocatorPagesDto,
    GetStoreLocatorStoresParamsDto,
    getStoreLocatorStoresParamsValidator,
    HandleStartStoreLocatorPublicationResponseDto,
    StartStoreLocatorPagesGenerationParamsDto,
    startStoreLocatorPagesGenerationParamsValidator,
    StartStoreLocatorPagesGenerationResponseDto,
    StartStoreLocatorStorePublicationParamsDto,
    startStoreLocatorStorePublicationParamsValidator,
    StoreLocatorOrganizationConfigurationResponseDto,
    SuccessResponse,
    UpdateDeploymentStatusBodyDto,
    updateDeploymentStatusBodyValidator,
    UpdateOrganizationConfigurationAiSettingsBodyDto,
    updateOrganizationConfigurationAiSettingsBodyValidator,
    UpdateOrganizationConfigurationLanguagesBodyDto,
    updateOrganizationConfigurationLanguagesBodyValidator,
    UpdateOrganizationConfigurationParamsDto,
    updateOrganizationConfigurationParamsValidator,
    UpdateOrganizationConfigurationStorePagesBodyDto,
    updateOrganizationConfigurationStorePagesBodyValidator,
    UpdateStoreLocatorStorePageParamsDto,
    updateStoreLocatorStorePageParamsValidator,
    UpdateStoreLocatorStorePagesBodyDto,
    updateStoreLocatorStorePagesBodyValidator,
    WatchStoreLocatorJobResponseDto,
    WatchStoreLocatorPagesGenerationParamsDto,
    watchStoreLocatorPagesGenerationParamsValidator,
    watchStoreLocatorStorePublicationParamsValidator,
} from '@malou-io/package-dto';
import { ApiResultError, ApiResultV2 } from '@malou-io/package-utils';

import { Body, Params } from ':helpers/decorators/validators';
import { CheckForStoreLocatorRestaurantPagesUseCase } from ':modules/store-locator/use-cases/check-for-store-locator-restaurant-pages/check-for-store-locator-restaurant-pages.use-case';
import { StartPagesGenerationUseCase } from ':modules/store-locator/use-cases/generate-store-locator-pages/start-pages-generation.use-case';
import { WatchPagesGenerationUseCase } from ':modules/store-locator/use-cases/generate-store-locator-pages/watch-pages-generation.use-case';
import GenerateStoreLocatorStorePageContentUseCase from ':modules/store-locator/use-cases/generate-store-page-content/generate-store-page-content.use-case';
import { GetOrganizationConfigurationUseCase } from ':modules/store-locator/use-cases/get-organization-configuration/get-organization-configuration.use-case';
import { GetStoreLocatorOrganizationJobsUseCase } from ':modules/store-locator/use-cases/get-organization-jobs/get-organization-jobs.use-case';
import { GetStoreLocatorOrganizationConfigurationUseCase } from ':modules/store-locator/use-cases/get-store-locator-organization-configuration/get-store-locator-organization-configuration.use-case';
import { GetStoreLocatorPagesUseCase } from ':modules/store-locator/use-cases/get-store-locator-pages/get-store-locator-pages.use-case';
import { GetStoreLocatorStorePagesForEditUseCase } from ':modules/store-locator/use-cases/get-store-locator-store-pages-for-edit/get-store-locator-store-pages-for-edit.use-case';
import { StartStoreLocatorPublicationUseCase } from ':modules/store-locator/use-cases/publish-store/start-store-publication.use-case';
import { WatchStoreLocatorPublicationUseCase } from ':modules/store-locator/use-cases/publish-store/watch-store-publication.use-case';
import { sendSubscriptionRequestNotificationUseCase } from ':modules/store-locator/use-cases/send-subscription-request-notification/send-subscription-request-notification.use-case';
import UpdateOrganizationConfigAISettingsUseCase from ':modules/store-locator/use-cases/update-organization-config-ai-settings/update-organization-config-ai-settings.use-case';
import UpdateOrganizationConfigLanguagesUseCase from ':modules/store-locator/use-cases/update-organization-config-languages/update-organization-config-languages.use-case';
import UpdateOrganizationConfigStorePagesUseCase from ':modules/store-locator/use-cases/update-organization-config-store-pages/update-organization-config-store-pages.use-case';
import { UpdateDeploymentJobStatusUseCase } from ':modules/store-locator/use-cases/update-store-deployment-job-status/update-store-deployment-job-status.use-case';
import UpdateStoreLocatorStorePagesUseCase from ':modules/store-locator/use-cases/update-store-pages/update-store-pages.use-case';

@singleton()
export default class StoreLocatorController {
    constructor(
        private readonly _getOrganizationConfigurationUseCase: GetOrganizationConfigurationUseCase,
        private readonly _updateOrganizationConfigAISettingsUseCase: UpdateOrganizationConfigAISettingsUseCase,
        private readonly _getStoreLocatorPagesUseCase: GetStoreLocatorPagesUseCase,
        private readonly _getStoreLocatorOrganizationConfigurationUseCase: GetStoreLocatorOrganizationConfigurationUseCase,
        private readonly _updateStoreLocatorStorePagesUseCase: UpdateStoreLocatorStorePagesUseCase,
        private readonly _generateStoreLocatorStorePageContentUseCase: GenerateStoreLocatorStorePageContentUseCase,
        private readonly _updateOrganizationConfigStorePagesUseCase: UpdateOrganizationConfigStorePagesUseCase,
        private readonly _startPagesGenerationUseCase: StartPagesGenerationUseCase,
        private readonly _watchPagesGenerationUseCase: WatchPagesGenerationUseCase,
        private readonly _startStoreLocatorPublicationUseCase: StartStoreLocatorPublicationUseCase,
        private readonly _watchStoreLocatorStorePublicationUseCase: WatchStoreLocatorPublicationUseCase,
        private readonly _checkForStoreLocatorRestaurantPagesUseCase: CheckForStoreLocatorRestaurantPagesUseCase,
        private readonly _getStoreLocatorStorePagesForEditUseCase: GetStoreLocatorStorePagesForEditUseCase,
        private readonly _updateDeploymentJobStatusUseCase: UpdateDeploymentJobStatusUseCase,
        private readonly _getStoreLocatorOrganizationJobsUseCase: GetStoreLocatorOrganizationJobsUseCase,
        private readonly _updateOrganizationConfigLanguagesUseCase: UpdateOrganizationConfigLanguagesUseCase,
        private readonly _sendSubscriptionRequestNotificationUseCase: sendSubscriptionRequestNotificationUseCase
    ) {}

    @Params(getStoreLocatorStoresParamsValidator)
    async handleGetPages(
        req: Request<GetStoreLocatorStoresParamsDto, never, never>,
        res: Response<ApiResultV2<GetStoreLocatorPagesDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const data = await this._getStoreLocatorPagesUseCase.execute(organizationId);
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(getStoreLocatorStoresParamsValidator)
    async handleGetPagesForEdit(
        req: Request<GetStoreLocatorStoresParamsDto, never, never>,
        res: Response<ApiResultV2<GetStoreLocatorDraftPagesDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const data = await this._getStoreLocatorStorePagesForEditUseCase.execute(organizationId);
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(getOrganizationConfigurationParamsValidator)
    async handleGetOrganizationConfiguration(
        req: Request<GetOrganizationConfigurationParamsDto, never, never>,
        res: Response<ApiResultV2<GetStoreLocatorOrganizationConfigurationDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const data = await this._getOrganizationConfigurationUseCase.execute(organizationId);

            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(getOrganizationConfigurationParamsValidator)
    async handleGetStoreLocatorOrganizationConfiguration(
        req: Request<GetOrganizationConfigurationParamsDto, never, never>,
        res: Response<ApiResultV2<StoreLocatorOrganizationConfigurationResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const data = await this._getStoreLocatorOrganizationConfigurationUseCase.execute(organizationId);

            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateOrganizationConfigurationParamsValidator)
    @Body(updateOrganizationConfigurationAiSettingsBodyValidator)
    async handleUpdateOrganizationConfigurationAiSettings(
        req: Request<UpdateOrganizationConfigurationParamsDto, never, UpdateOrganizationConfigurationAiSettingsBodyDto>,
        res: Response<ApiResultV2<StoreLocatorOrganizationConfigurationResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const { aiSettings } = req.body;
            const organizationConfig = await this._updateOrganizationConfigAISettingsUseCase.execute({ organizationId, aiSettings });
            return res.json({ data: organizationConfig });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateStoreLocatorStorePageParamsValidator)
    @Body(updateStoreLocatorStorePagesBodyValidator)
    async handleUpdateRestaurantPages(
        req: Request<UpdateStoreLocatorStorePageParamsDto, never, UpdateStoreLocatorStorePagesBodyDto>,
        res: Response<ApiResultV2<SuccessResponse, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            await this._updateStoreLocatorStorePagesUseCase.execute({ organizationId, pages: req.body });
            return res.json({ data: { success: true } });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateStoreLocatorStorePageParamsValidator)
    @Body(generateStoreLocatorStorePageContentBodyValidator)
    async handleGeneratePageContent(
        req: Request<UpdateStoreLocatorStorePageParamsDto, never, GenerateStoreLocatorStorePageContentBodyDto>,
        res: Response<ApiResultV2<GenerateStoreLocatorContentResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const { updates, type, currentContent } = req.body;

            const data = await this._generateStoreLocatorStorePageContentUseCase.execute({
                organizationId,
                updates,
                type,
                currentContent,
            });
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateOrganizationConfigurationParamsValidator)
    @Body(updateOrganizationConfigurationStorePagesBodyValidator)
    async handleUpdateOrganizationConfigurationStorePages(
        req: Request<UpdateOrganizationConfigurationParamsDto, never, UpdateOrganizationConfigurationStorePagesBodyDto>,
        res: Response<ApiResultV2<SuccessResponse, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const { data } = req.body;
            await this._updateOrganizationConfigStorePagesUseCase.execute({ organizationId, storePages: data });
            return res.json({ data: { success: true } });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateOrganizationConfigurationParamsValidator)
    @Body(updateOrganizationConfigurationLanguagesBodyValidator)
    async handleUpdateOrganizationConfigurationLanguages(
        req: Request<UpdateOrganizationConfigurationParamsDto, never, UpdateOrganizationConfigurationLanguagesBodyDto>,
        res: Response<ApiResultV2<StoreLocatorOrganizationConfigurationResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const { languages } = req.body;
            const organizationConfig = await this._updateOrganizationConfigLanguagesUseCase.execute({ organizationId, languages });
            return res.json({ data: organizationConfig });
        } catch (err) {
            next(err);
        }
    }

    @Params(checkForStoreLocatorRestaurantPagesParamsValidator)
    async handleCheckForStoreLocatorRestaurantPages(
        req: Request<CheckForStoreLocatorRestaurantPagesParamsDto, never, never>,
        res: Response<ApiResultV2<CheckForStoreLocatorRestaurantPagesResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        const { organizationId } = req.params;
        try {
            const data = await this._checkForStoreLocatorRestaurantPagesUseCase.execute(organizationId);
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(getStoreLocatorOrganizationJobsParamsValidator)
    async handleGetOrganizationJobs(
        req: Request<GetStoreLocatorOrganizationJobsParamsDto, GetStoreLocatorOrganizationJobResponseDto[]>,
        res: Response<ApiResultV2<GetStoreLocatorOrganizationJobResponseDto[], ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const jobs = await this._getStoreLocatorOrganizationJobsUseCase.execute(organizationId);
            return res.json({ data: jobs ?? [] });
        } catch (err) {
            next(err);
        }
    }

    @Params(startStoreLocatorPagesGenerationParamsValidator)
    async handleStartStoreLocatorPagesGeneration(
        req: Request<StartStoreLocatorPagesGenerationParamsDto, never, never>,
        res: Response<ApiResultV2<StartStoreLocatorPagesGenerationResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const jobId = await this._startPagesGenerationUseCase.execute(organizationId);
            return res.json({ data: { jobId } });
        } catch (err) {
            next(err);
        }
    }

    @Params(watchStoreLocatorPagesGenerationParamsValidator)
    async handleWatchStoreLocatorPagesGeneration(
        req: Request<WatchStoreLocatorPagesGenerationParamsDto, never, never>,
        res: Response<ApiResultV2<WatchStoreLocatorJobResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        const { organizationId, jobId } = req.params;
        try {
            const data = await this._watchPagesGenerationUseCase.execute({ organizationId, jobId });
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(startStoreLocatorStorePublicationParamsValidator)
    async handleStartStoreLocatorPublication(
        req: Request<StartStoreLocatorStorePublicationParamsDto, never, never>,
        res: Response<ApiResultV2<HandleStartStoreLocatorPublicationResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const jobId = await this._startStoreLocatorPublicationUseCase.execute(organizationId);
            return res.json({ data: { jobId } });
        } catch (err) {
            next(err);
        }
    }

    @Params(watchStoreLocatorStorePublicationParamsValidator)
    async handleWatchStoreLocatorStorePublication(
        req: Request<WatchStoreLocatorPagesGenerationParamsDto, never, never>,
        res: Response<ApiResultV2<WatchStoreLocatorJobResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        const { organizationId, jobId } = req.params;
        try {
            const data = await this._watchStoreLocatorStorePublicationUseCase.execute({ organizationId, jobId });
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Body(updateDeploymentStatusBodyValidator)
    async handleUpdateDeploymentStatus(
        req: Request<{ organizationId: string }, never, UpdateDeploymentStatusBodyDto>,
        res: Response<ApiResultV2<SuccessResponse, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { jobId, status, workflowRunId } = req.body;
            await this._updateDeploymentJobStatusUseCase.execute({ jobId, status, workflowRunId });
            return res.json({ data: { success: true } });
        } catch (err) {
            next(err);
        }
    }

    @Params(getOrganizationConfigurationParamsValidator)
    async handleStoreLocatorSendSubscriptionRequest(
        req: Request<{ organizationId: string }, never, never>,
        res: Response<ApiResultV2<SuccessResponse, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const userId = req.user._id;

            await this._sendSubscriptionRequestNotificationUseCase.execute({ organizationId, userId });
            return res.json({ data: { success: true } });
        } catch (err) {
            next(err);
        }
    }
}
