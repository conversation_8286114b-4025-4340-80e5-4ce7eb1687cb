import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { StoreLocatorJobStatus } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';

@singleton()
export class StartStoreLocatorPublicationUseCase {
    constructor(private readonly _agendaSingleton: AgendaSingleton) {}

    async execute(organizationId: string): Promise<string> {
        logger.info('[STORE_LOCATOR] [Publish Store] Calling job to start publishing', {
            organizationId,
        });
        const job = await this._agendaSingleton.now(AgendaJobName.STORE_LOCATOR_PUBLICATION, {
            organizationId,
            status: StoreLocatorJobStatus.PENDING,
        });
        assert(job.attrs._id, 'Job not created');

        return job.attrs._id.toString();
    }
}
