import { Job } from 'agenda';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IStoreLocatorRestaurantPageWithRestaurant, toDbId } from '@malou-io/package-models';
import { isNotNil, StoreLocatorJobStatus, StoreLocatorPageStatus } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import { GenerateMediaDescriptionImageType } from ':modules/ai/interfaces/ai.interfaces';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { PublishStoreLocatorDataAttributes } from ':modules/store-locator/jobs/publish-store-locator';
import { PreprocessStoreLocatorPictureService } from ':modules/store-locator/services/preprocess-store-locator-picture/preprocess-store-locator-picture.service';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';
import { AwsS3 } from ':plugins/cloud-storage/s3';

@singleton()
export class ProcessStoreLocatorPublicationUseCase {
    constructor(
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _cloudStorageService: AwsS3,
        private readonly _preprocessStoreLocatorPictureService: PreprocessStoreLocatorPictureService,
        private readonly _agendaSingleton: AgendaSingleton
    ) {}

    async execute({ organizationId, job }: { organizationId: string; job: Job<PublishStoreLocatorDataAttributes> }): Promise<void> {
        const [storeLocatorOrganizationConfig, storeLocatorRestaurantsPages] = await Promise.all([
            this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(organizationId),
            this._storeLocatorRestaurantPageRepository.find({
                filter: { organizationId, status: StoreLocatorPageStatus.DRAFT, hasBeenUpdated: true },
                options: { populate: [{ path: 'restaurant' }], lean: true },
            }),
        ]);

        assert(storeLocatorOrganizationConfig, 'Store Locator Organization Config not found');
        assert(storeLocatorRestaurantsPages, 'Store Locator Restaurants Pages not found');

        // Process and publish all modified store locator restaurant pages
        await Promise.all(
            storeLocatorRestaurantsPages.map((storeLocatorRestaurantPage) =>
                this._processStoreLocatorPage({
                    storeLocatorOrganizationConfig,
                    storeLocatorRestaurantPage,
                })
            )
        );

        // Update configuration configurations styles
        await this._updateConfigurationStyles(storeLocatorOrganizationConfig);

        // Trigger deployment job and attach to the publication job
        await this._triggerDeploymentJobAndAttachToPublicationJob({ organizationId, job });
    }

    private async _updateConfigurationStyles(storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration): Promise<void> {
        const newPageStyles = {
            ...storeLocatorOrganizationConfig.styles.pages,
            store: {
                ...storeLocatorOrganizationConfig.styles.pages.store,
                ...storeLocatorOrganizationConfig.styles.pages.storeDraft,
            },
            map: {
                ...storeLocatorOrganizationConfig.styles.pages.map,
                ...storeLocatorOrganizationConfig.styles.pages.mapDraft,
            },
            storeDraft: {},
            mapDraft: {},
        };
        await this._storeLocatorOrganizationConfigRepository.updateOne({
            filter: { organizationId: toDbId(storeLocatorOrganizationConfig.organizationId) },
            update: { 'styles.pages': newPageStyles },
        });
    }

    private async _triggerDeploymentJobAndAttachToPublicationJob({
        organizationId,
        job,
    }: {
        organizationId: string;
        job: Job<PublishStoreLocatorDataAttributes>;
    }): Promise<void> {
        const deploymentJob = await this._agendaSingleton.now(AgendaJobName.STORE_LOCATOR_DEPLOYMENT, {
            organizationId,
            status: StoreLocatorJobStatus.PENDING,
        });
        if (deploymentJob.attrs._id && job.attrs.data) {
            job.attrs.data.deploymentJobId = deploymentJob.attrs._id.toString();
        }
        await job.save();
    }

    private async _processStoreLocatorPage({
        storeLocatorOrganizationConfig,
        storeLocatorRestaurantPage,
    }: {
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPageWithRestaurant;
    }): Promise<void> {
        const images = await this._processImages({
            storeLocatorOrganizationConfig,
            storeLocatorRestaurantPage,
        });

        // Update current draft
        const draftWithProcessedImages = this._storeLocatorRestaurantPageRepository.toDocumentWithModifications(
            storeLocatorRestaurantPage,
            {
                restaurantId: storeLocatorRestaurantPage.restaurantId.toString(),
                lang: storeLocatorRestaurantPage.lang,
                information: {
                    title: storeLocatorRestaurantPage.blocks.information.title,
                    image: images.information[0],
                    ctas: storeLocatorRestaurantPage.blocks.information.ctas,
                },
                gallery: {
                    title: storeLocatorRestaurantPage.blocks.gallery.title,
                    subtitle: storeLocatorRestaurantPage.blocks.gallery.subtitle,
                    images: images.gallery,
                },
                descriptions: {
                    items: storeLocatorRestaurantPage.blocks.descriptions.items.map((item, index) => ({
                        ...item,
                        image: images.descriptions[index],
                    })),
                },
            }
        );
        const updatedDraft = await this._storeLocatorRestaurantPageRepository.findOneAndUpdate({
            filter: {
                _id: storeLocatorRestaurantPage._id,
            },
            update: {
                ...draftWithProcessedImages,
                hasBeenUpdated: false,
            },
            options: { lean: true },
        });

        // Remove published page and replace it with the updated draft
        if (updatedDraft) {
            const { _id, createdAt: _createdAt, updatedAt: _updatedAt, status: _status, ...newPageToPublish } = updatedDraft;

            await this._storeLocatorRestaurantPageRepository.atomicUpsert({
                filter: {
                    restaurantId: updatedDraft.restaurantId,
                    organizationId: storeLocatorOrganizationConfig.organizationId,
                    lang: updatedDraft.lang,
                    status: StoreLocatorPageStatus.PUBLISHED,
                },
                update: {
                    ...newPageToPublish,
                    status: StoreLocatorPageStatus.PUBLISHED,
                },
            });
        }
    }

    private async _processImages({
        storeLocatorOrganizationConfig,
        storeLocatorRestaurantPage,
    }: {
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPageWithRestaurant;
    }): Promise<Record<'information' | 'gallery' | 'descriptions', { url: string; description: string }[]>> {
        const organizationId = storeLocatorOrganizationConfig.organizationId;
        const restaurantId = storeLocatorRestaurantPage.restaurantId.toString();
        // todo store-locator: intersection with restaurant keywords ?
        const keywords = storeLocatorOrganizationConfig.aiSettings.keywords.map((keyword) => keyword.text);
        const lang = storeLocatorRestaurantPage.lang;
        const restaurantName = storeLocatorRestaurantPage.restaurant.name;

        const medias: Record<'information' | 'gallery' | 'descriptions', { url: string; description: string }[]> = {
            information: [storeLocatorRestaurantPage.blocks.information.image],
            gallery: storeLocatorRestaurantPage.blocks.gallery.images,
            descriptions: storeLocatorRestaurantPage.blocks.descriptions.items.map((item) => item.image),
        };
        const imageTypeMapping: Record<'information' | 'gallery' | 'descriptions', GenerateMediaDescriptionImageType> = {
            information: GenerateMediaDescriptionImageType.ALT_TEXT_INFORMATION_BLOCK,
            gallery: GenerateMediaDescriptionImageType.ALT_TEXT_GALLERY_BLOCK,
            descriptions: GenerateMediaDescriptionImageType.ALT_TEXT_DESCRIPTIONS_BLOCK,
        };

        await Promise.all(
            Object.entries(medias).map(async ([blockType, images]) => {
                medias[blockType] = await Promise.all(
                    images.map(async (image, index) => {
                        try {
                            if (
                                !this._shouldBeProcessed({
                                    imageUrl: image.url,
                                    organizationId,
                                })
                            ) {
                                return image;
                            }

                            const s3Key = `store-locator/organization/${organizationId}/restaurants/${restaurantId}/${blockType}/photo${index}`;
                            const imageType = imageTypeMapping[blockType];

                            const processedImage = await this._preprocessStoreLocatorPictureService.execute({
                                image: { url: image.url, s3Key },
                                metadata: {
                                    imageType,
                                    restaurantName,
                                    keywords,
                                    lang,
                                },
                            });

                            return isNotNil(processedImage)
                                ? {
                                      url: processedImage.url,
                                      description: processedImage.text,
                                  }
                                : image;
                        } catch (error) {
                            logger.error(`[STORE_LOCATOR] Failed to process image for ${blockType}`, {
                                error,
                                metadata: {
                                    organizationId,
                                    restaurantId,
                                    imageUrl: image.url,
                                    blockType,
                                    index,
                                },
                            });

                            return image;
                        }
                    })
                );
            })
        );

        return medias;
    }

    private _shouldBeProcessed({ imageUrl, organizationId }: { imageUrl: string; organizationId: string }): boolean {
        return !imageUrl.startsWith(`${this._cloudStorageService.getBucketBaseUrl()}/store-locator/organization/${organizationId}`);
    }
}
