import { container } from 'tsyringe';

import { CheckForStoreLocatorRestaurantPagesResponseDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { BusinessCategory, StoreLocatorLanguage, StoreLocatorPageStatus } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultStoreLocatorRestaurantPage } from ':modules/store-locator/builders/store-locator-restaurant-page.builder';
import { CheckForStoreLocatorRestaurantPagesUseCase } from ':modules/store-locator/use-cases/check-for-store-locator-restaurant-pages/check-for-store-locator-restaurant-pages.use-case';

describe('CheckForStoreLocatorRestaurantPagesUseCase', () => {
    let useCase: CheckForStoreLocatorRestaurantPagesUseCase;

    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'StoreLocatorRestaurantPageRepository']);
        useCase = container.resolve(CheckForStoreLocatorRestaurantPagesUseCase);
    });

    it('should return unique restaurant IDs for organization', async () => {
        const organizationId = newDbId();

        const otherRestaurantId = newDbId();
        const otherOrganizationId = newDbId();

        const testCase = new TestCaseBuilderV2<'restaurants' | 'storeLocatorRestaurantPage'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant().active(true).organizationId(organizationId).build(),
                            getDefaultRestaurant().active(true).organizationId(organizationId).build(),
                            // Restaurant with no store locator pages
                            getDefaultRestaurant().active(true).organizationId(organizationId).build(),
                            // brand restaurant
                            getDefaultRestaurant()
                                .active(true)
                                .organizationId(organizationId)
                                .type(BusinessCategory.BRAND)
                                .socialId('brand-socialid-1')
                                .build(),
                            // inactive restaurant
                            getDefaultRestaurant()
                                .active(false)
                                .organizationId(organizationId)
                                .type(BusinessCategory.LOCAL_BUSINESS)
                                .build(),
                        ];
                    },
                },
                storeLocatorRestaurantPage: {
                    data(dependecies) {
                        return [
                            getDefaultStoreLocatorRestaurantPage()
                                .organizationId(organizationId)
                                .restaurantId(dependecies.restaurants()[0]._id)
                                .lang(StoreLocatorLanguage.FR)
                                .status(StoreLocatorPageStatus.PUBLISHED)
                                .hasBeenUpdated(true)
                                .build(),
                            getDefaultStoreLocatorRestaurantPage()
                                .organizationId(organizationId)
                                .restaurantId(dependecies.restaurants()[1]._id)
                                .lang(StoreLocatorLanguage.EN)
                                .status(StoreLocatorPageStatus.DRAFT)
                                .hasBeenUpdated(false)
                                .build(),
                            getDefaultStoreLocatorRestaurantPage()
                                .organizationId(organizationId)
                                .restaurantId(dependecies.restaurants()[1]._id)
                                .lang(StoreLocatorLanguage.FR)
                                .status(StoreLocatorPageStatus.DRAFT)
                                .hasBeenUpdated(false)
                                .build(),
                            // brand restaurant should not appear in the results
                            getDefaultStoreLocatorRestaurantPage()
                                .organizationId(organizationId)
                                .restaurantId(dependecies.restaurants()[3]._id)
                                .lang(StoreLocatorLanguage.EN)
                                .status(StoreLocatorPageStatus.PUBLISHED)
                                .build(),
                            // inactive restaurant should not appear in the results
                            getDefaultStoreLocatorRestaurantPage()
                                .organizationId(organizationId)
                                .restaurantId(dependecies.restaurants()[4]._id)
                                .lang(StoreLocatorLanguage.EN)
                                .status(StoreLocatorPageStatus.PUBLISHED)
                                .build(),

                            // Other organization restaurant with a page
                            getDefaultStoreLocatorRestaurantPage()
                                .organizationId(otherOrganizationId)
                                .restaurantId(otherRestaurantId)
                                .lang(StoreLocatorLanguage.FR)
                                .status(StoreLocatorPageStatus.DRAFT)
                                .build(),
                        ];
                    },
                },
            },
            expectedResult(): CheckForStoreLocatorRestaurantPagesResponseDto {
                return {
                    organizationId: organizationId.toString(),
                    hasAtLeastOnePageGenerated: true,
                    hasUpdatedPages: true,
                    hasMissingRestaurantPages: true,
                };
            },
        });

        await testCase.build();

        const exepectedResult = testCase.getExpectedResult();

        const result = await useCase.execute(organizationId.toString());

        expect(result).toEqual(exepectedResult);
    });
});
