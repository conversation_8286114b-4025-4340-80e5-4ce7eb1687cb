import { singleton } from 'tsyringe';

import { UpdateOrganizationConfigurationStorePagesBodyDto } from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';

@singleton()
export default class UpdateOrganizationConfigStorePagesUseCase {
    constructor(private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository) {}

    async execute({
        organizationId,
        storePages,
    }: {
        organizationId: string;
        storePages: UpdateOrganizationConfigurationStorePagesBodyDto['data'];
    }): Promise<void> {
        const storePagesUpdateMap = this._getUpdateMap(storePages);

        logger.info(`[STORE_LOCATOR] [UPDATE_ORGANIZATION_CONFIG_STORE_PAGES] Updating store pages for organization id`, {
            metadata: {
                organizationId,
                storePagesUpdateMap,
            },
        });

        await this._storeLocatorOrganizationConfigRepository.findOneAndUpdate({
            filter: { organizationId: toDbId(organizationId) },
            update: storePagesUpdateMap,
        });

        return;
    }

    private _getUpdateMap(storePages: UpdateOrganizationConfigurationStorePagesBodyDto['data']): Record<string, string[]> {
        const base = 'styles.pages.storeDraft';
        return Object.entries(storePages).reduce(
            (acc, [key, value]) => {
                acc[`${base}.${key}`] = value;
                return acc;
            },
            {} as Record<string, string[]>
        );
    }
}
