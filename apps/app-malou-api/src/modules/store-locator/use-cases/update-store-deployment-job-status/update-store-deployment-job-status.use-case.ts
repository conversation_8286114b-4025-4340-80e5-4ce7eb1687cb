import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { GitHubActionStatus, StoreLocatorJobStatus } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';

@singleton()
export class UpdateDeploymentJobStatusUseCase {
    constructor(private readonly _agendaSingleton: AgendaSingleton) {}

    private readonly _githubActionStatusToJobStatus: Record<GitHubActionStatus, StoreLocatorJobStatus> = {
        [GitHubActionStatus.CANCELLED]: StoreLocatorJobStatus.CANCELLED,
        [GitHubActionStatus.SKIPPED]: StoreLocatorJobStatus.CANCELLED,
        [GitHubActionStatus.SUCCESS]: StoreLocatorJobStatus.SUCCEEDED,
        [GitHubActionStatus.FAILURE]: StoreLocatorJobStatus.FAILED,
    };

    async execute({ jobId, status, workflowRunId }: { jobId: string; status: GitHubActionStatus; workflowRunId: string }): Promise<void> {
        logger.info('[STORE_LOCATOR] [Deployment] About to update jobs status', {
            jobId,
            status,
            workflowRunId,
        });

        const [[deploymentJob], [publicationJob]] = await Promise.all([
            this._agendaSingleton.jobs({
                _id: toDbId(jobId),
                name: AgendaJobName.STORE_LOCATOR_DEPLOYMENT,
            }),
            this._agendaSingleton.jobs({
                name: AgendaJobName.STORE_LOCATOR_PUBLICATION,
                'data.deploymentJobId': jobId,
                'data.status': StoreLocatorJobStatus.PENDING,
            }),
        ]);
        assert(deploymentJob, 'Job not found');

        const organizationId = deploymentJob.attrs.data?.organizationId?.toString();
        const publicationJobId = publicationJob?.attrs?._id?.toString();

        logger.info('[STORE_LOCATOR] [Deployment] Found jobs to update', {
            organizationId,
            jobId,
            status,
            ...(publicationJobId && { publicationJobId }),
        });

        const updatedStatus = this._githubActionStatusToJobStatus[status] ?? StoreLocatorJobStatus.FAILED;

        if (deploymentJob.attrs.data) {
            deploymentJob.attrs.data.status = updatedStatus;
            deploymentJob.attrs.data.workflowRunId = workflowRunId;
        }

        // Update the publication job linked to this deployment job if it exists
        if (publicationJob?.attrs?.data) {
            publicationJob.attrs.data.status = updatedStatus;
        }

        await Promise.all([deploymentJob.save(), ...(publicationJob ? [publicationJob.save()] : [])]);

        logger.info('[STORE_LOCATOR] [Deployment] Updated jobs status', {
            organizationId,
            jobId,
            status,
            ...(publicationJobId && { publicationJobId }),
        });
    }
}
