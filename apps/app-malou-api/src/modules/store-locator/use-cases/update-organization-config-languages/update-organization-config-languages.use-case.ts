import { singleton } from 'tsyringe';

import { StoreLocatorOrganizationConfigurationResponseDto, UpdateOrganizationConfigurationLanguagesBodyDto } from '@malou-io/package-dto';

import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';

@singleton()
export default class UpdateOrganizationConfigLanguagesUseCase {
    constructor(private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository) {}

    async execute({
        organizationId,
        languages,
    }: {
        organizationId: string;
        languages: UpdateOrganizationConfigurationLanguagesBodyDto['languages'];
    }): Promise<StoreLocatorOrganizationConfigurationResponseDto> {
        const updatedConfig = await this._storeLocatorOrganizationConfigRepository.updateLanguages(organizationId, languages);
        return updatedConfig.toDto();
    }
}
