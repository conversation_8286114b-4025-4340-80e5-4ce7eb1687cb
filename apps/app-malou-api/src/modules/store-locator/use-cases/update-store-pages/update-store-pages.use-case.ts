import { singleton } from 'tsyringe';

import { UpdateStoreLocatorStorePagesBodyDto } from '@malou-io/package-dto';
import { StoreLocatorLanguage } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
export default class UpdateStoreLocatorStorePagesUseCase {
    constructor(private readonly _storeLocatorRestaurantPagesRepository: StoreLocatorRestaurantPageRepository) {}

    async execute({ organizationId, pages }: { organizationId: string; pages: UpdateStoreLocatorStorePagesBodyDto }): Promise<void> {
        logger.info(`[STORE_LOCATOR] [UPDATE_PAGE] Updating store pages for organization id`, {
            metadata: {
                organizationId,
                pages,
            },
        });

        const pagesPerLanguage = pages.reduce(
            (acc, page) => {
                if (!acc[page.lang]) {
                    acc[page.lang] = [];
                }
                acc[page.lang].push(page);
                return acc;
            },
            {} as Record<StoreLocatorLanguage, UpdateStoreLocatorStorePagesBodyDto>
        );

        await Promise.all(
            Object.keys(pagesPerLanguage).map((language) =>
                this._storeLocatorRestaurantPagesRepository.updateStoreLocatorStorePages(
                    organizationId,
                    pagesPerLanguage[language],
                    language as StoreLocatorLanguage
                )
            )
        );

        return;
    }
}
