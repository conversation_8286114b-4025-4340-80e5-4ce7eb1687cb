import { container } from 'tsyringe';

import { StoreLocatorOrganizationConfigurationResponseDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { PlatformKey, StoreLocatorAiSettingsLanguageStyle } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultAttribute } from ':modules/attributes/tests/attribute.builder';
import { getDefaultKeywordTemp } from ':modules/keywords/tests/keyword.builder';
import { getDefaultOrganization } from ':modules/organizations/organization.builder';
import { getDefaultRestaurantKeyword } from ':modules/restaurant-keywords/tests/restaurant-keywords.builder';
import { getDefaultStoreLocatorOrganizationConfig } from ':modules/store-locator/builders/store-locator-organization-config.builder';
import { GetStoreLocatorOrganizationConfigurationUseCase } from ':modules/store-locator/use-cases/get-store-locator-organization-configuration/get-store-locator-organization-configuration.use-case';

describe('GetStoreLocatorOrganizationConfigurationUseCase', () => {
    beforeAll(() => {
        registerRepositories([
            'AttributesRepository',
            'RestaurantKeywordsRepository',
            'OrganizationsRepository',
            'KeywordsTempRepository',
            'StoreLocatorOrganizationConfigRepository',
        ]);
    });

    describe('execute', () => {
        it('should return store locator organization configuration response DTO', async () => {
            const useCase = container.resolve(GetStoreLocatorOrganizationConfigurationUseCase);
            const restaurantId = newDbId();

            const testCase = new TestCaseBuilderV2<
                'attributes' | 'keywordsTemp' | 'restaurantKeywords' | 'organizations' | 'storeLocatorOrganizationConfigs'
            >({
                seeds: {
                    attributes: {
                        data() {
                            return [
                                getDefaultAttribute()
                                    .attributeId('attribute_id_1')
                                    .attributeName({ fr: 'Attribute 1' })
                                    .platformKey(PlatformKey.GMB)
                                    .build(),
                                getDefaultAttribute()
                                    .attributeId('attribute_id_2')
                                    .attributeName({ fr: 'Attribute 2' })
                                    .platformKey(PlatformKey.GMB)
                                    .build(),
                            ];
                        },
                    },
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp().text('keyword text 1').build(),
                                getDefaultKeywordTemp().text('keyword text 2').build(),
                            ];
                        },
                    },
                    organizations: {
                        data() {
                            return [getDefaultOrganization().build()];
                        },
                    },
                    restaurantKeywords: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantKeyword()
                                    .restaurantId(restaurantId)
                                    .keywordId(dependencies.keywordsTemp()[0]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(restaurantId)
                                    .keywordId(dependencies.keywordsTemp()[1]._id)
                                    .build(),
                            ];
                        },
                    },
                    storeLocatorOrganizationConfigs: {
                        data(dependencies) {
                            const attributeIds = dependencies.attributes().map((a) => a.attributeId);
                            const restaurantKeywordIds = dependencies.restaurantKeywords().map((rk) => rk._id);
                            const organizationId = dependencies.organizations()[0]._id;

                            return [
                                getDefaultStoreLocatorOrganizationConfig()
                                    .organizationId(organizationId)
                                    .aiSettings({
                                        tone: ['inspiring', 'friendly'],
                                        languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                                        attributeIds,
                                        restaurantKeywordIds,
                                        specialAttributes: [],
                                    })
                                    .build(),
                                getDefaultStoreLocatorOrganizationConfig().organizationId(newDbId()).build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): StoreLocatorOrganizationConfigurationResponseDto {
                    const attributes = dependencies.attributes;
                    const restaurantKeywords = dependencies.restaurantKeywords;
                    const keywordsTemp = dependencies.keywordsTemp;
                    const config = dependencies.storeLocatorOrganizationConfigs[0];
                    const organization = dependencies.organizations[0];

                    return {
                        id: config._id.toString(),
                        organizationId: config.organizationId.toString(),
                        organization: {
                            id: organization._id.toString(),
                            name: organization.name,
                        },
                        cloudfrontDistributionId: config.cloudfrontDistributionId,
                        baseUrl: config.baseUrl,
                        isLive: config.isLive,
                        styles: config.styles as StoreLocatorOrganizationConfigurationResponseDto['styles'],
                        plugins: config.plugins,
                        languages: {
                            primary: config.languages.primary,
                            secondary: config.languages.secondary || [],
                        },
                        aiSettings: {
                            tone: config.aiSettings.tone,
                            languageStyle: config.aiSettings.languageStyle,
                            attributeIds: config.aiSettings.attributeIds,
                            restaurantKeywordIds: config.aiSettings.restaurantKeywordIds.map((id) => id.toString()),
                            specialAttributes: config.aiSettings.specialAttributes.map((attr) => ({
                                restaurantId: attr.restaurantId.toString(),
                                text: attr.text,
                            })),
                            attributes: attributes.map((attribute) => ({
                                id: attribute._id.toString(),
                                attributeId: attribute.attributeId,
                                platformKey: attribute.platformKey,
                                attributeName: {
                                    fr: attribute.attributeName.fr || '',
                                    en: attribute.attributeName.en || undefined,
                                    es: attribute.attributeName.es || undefined,
                                    it: attribute.attributeName.it || undefined,
                                },
                            })),
                            keywords: restaurantKeywords.map((restaurantKeyword) => ({
                                restaurantKeywordId: restaurantKeyword._id.toString(),
                                text: keywordsTemp.find((kt) => kt._id.toString() === restaurantKeyword.keywordId.toString())?.text || '',
                                restaurantId: restaurantKeyword.restaurantId.toString(),
                                keywordId: restaurantKeyword.keywordId.toString(),
                            })),
                        },
                    };
                },
            });
            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const expectedResult = testCase.getExpectedResult();
            const organizationId = seededObjects.organizations[0]._id.toString();
            const result = await useCase.execute(organizationId);

            expect(result).toEqual(expectedResult);
        });

        it('should throw an assertion error when organization configuration is not found', async () => {
            const useCase = container.resolve(GetStoreLocatorOrganizationConfigurationUseCase);
            const nonExistentOrganizationId = newDbId();

            const testCase = new TestCaseBuilderV2<'storeLocatorOrganizationConfigs'>({
                seeds: {
                    storeLocatorOrganizationConfigs: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult() {
                    return null;
                },
            });
            await testCase.build();

            await expect(useCase.execute(nonExistentOrganizationId.toString())).rejects.toThrow(
                `Store locator organization configuration not found for organization ID: ${nonExistentOrganizationId}`
            );
        });
    });
});
