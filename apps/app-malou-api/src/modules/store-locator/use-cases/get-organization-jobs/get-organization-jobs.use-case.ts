import { singleton } from 'tsyringe';

import { GetStoreLocatorOrganizationJobResponseDto } from '@malou-io/package-dto';
import { isNotNil, MalouErrorCode, StoreLocatorJobStatus, StoreLocatorJobType } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';

@singleton()
export class GetStoreLocatorOrganizationJobsUseCase {
    constructor(private readonly _agendaSingleton: AgendaSingleton) {}

    async execute(organizationId: string): Promise<GetStoreLocatorOrganizationJobResponseDto[]> {
        const jobs = await this._agendaSingleton.jobs({
            'data.organizationId': organizationId,
            'data.status': StoreLocatorJobStatus.PENDING,
        });
        const filteredJobs = jobs.filter((job) =>
            [AgendaJobName.STORE_LOCATOR_CONTENT_GENERATION, AgendaJobName.STORE_LOCATOR_PUBLICATION].includes(
                job.attrs.name as AgendaJobName
            )
        );

        return filteredJobs
            .filter((job) => isNotNil(job.attrs._id) && isNotNil(job.attrs.name))
            .map((job) => ({
                jobId: job.attrs._id!.toString(),
                jobType: this._mapJobNameToJobType(job.attrs.name),
                jobStartDate: job.attrs.lastRunAt,
            }));
    }

    private _mapJobNameToJobType(jobName: string): GetStoreLocatorOrganizationJobResponseDto['jobType'] {
        switch (jobName) {
            case AgendaJobName.STORE_LOCATOR_CONTENT_GENERATION:
                return StoreLocatorJobType.CONTENT_GENERATION;
            case AgendaJobName.STORE_LOCATOR_PUBLICATION:
                return StoreLocatorJobType.PUBLICATION;
            default:
                throw new MalouError(MalouErrorCode.STORE_LOCATOR_INVALID_JOB_NAME, {
                    metadata: jobName,
                });
        }
    }
}
