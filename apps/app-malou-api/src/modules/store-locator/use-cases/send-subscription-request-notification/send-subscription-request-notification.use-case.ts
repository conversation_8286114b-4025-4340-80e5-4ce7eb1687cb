import { render } from '@react-email/render';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { subscriptionRequestTemplate } from '@malou-io/package-emails';
import { ReadPreferenceMode } from '@malou-io/package-models';
import { Locale } from '@malou-io/package-utils';

import { Config } from ':config';
import { EmailSenderService } from ':modules/mailing/email-sender.service';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import { UsersRepository } from ':modules/users/users.repository';

@singleton()
export class sendSubscriptionRequestNotificationUseCase {
    constructor(
        private readonly _usersRepository: UsersRepository,
        private readonly _organizationsRepository: OrganizationsRepository,
        private readonly _emailSenderService: EmailSenderService
    ) {}

    async execute({ organizationId, userId }: { organizationId: string; userId: string }): Promise<void> {
        const [user, organization] = await Promise.all([
            this._usersRepository.findOne({
                filter: { _id: userId },
                projection: { email: 1, name: 1, lastname: 1 },
                options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY },
            }),
            this._organizationsRepository.findOne({
                filter: { _id: organizationId },
                projection: { name: 1 },
                options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY },
            }),
        ]);

        assert(user, '[sendSubscriptionRequestNotificationUseCase] User not found');
        assert(organization, '[sendSubscriptionRequestNotificationUseCase] Organization not found');

        await this._sendEmail({
            organizationName: organization.name,
            user: {
                email: user.email,
                name: user.name || '',
                lastname: user.lastname || '',
            },
        });
    }

    private async _sendEmail({
        organizationName,
        user,
    }: {
        organizationName: string;
        user: { email: string; name: string; lastname: string };
    }): Promise<void> {
        const html = render(
            subscriptionRequestTemplate({
                locale: Locale.FR,
                organizationName,
                user: {
                    ...user,
                    fullName: `${user.name} ${user.lastname}`.trim(),
                },
            })
        );

        await this._emailSenderService.sendEmail({
            to: process.env.NODE_ENV === 'production' ? '<EMAIL>' : '<EMAIL>',
            subject: `Demande de souscription store locator pour ${organizationName}`,
            html,
            fromEmail: Config.settings.adminUpdatesNotificationEmail,
        });
    }
}
