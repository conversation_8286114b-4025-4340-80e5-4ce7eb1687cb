import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IStoreLocatorRestaurantPage, toDbId } from '@malou-io/package-models';
import {
    BusinessCategory,
    DEFAULT_IMAGE_DESCRIPTION,
    DEFAULT_PLACEHOLDER_IMAGE_URL,
    isNotNil,
    MediaCategory,
    MediaType,
    StoreLocatorLanguage,
    StoreLocatorPageStatus,
} from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { GmbCategoryIdEnum } from ':modules/categories/types';
import { MediasRepository } from ':modules/media/medias.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { categoriesToServesCuisine } from ':modules/store-locator/mappings/categories-to-schema-org-serves-cuisine.mapping';
import { GenerateStorePageContentService } from ':modules/store-locator/services/generate-store-page-content/generate-store-page-content.service';
import { GetStoreCallToActionsSuggestionsService } from ':modules/store-locator/services/get-store-call-to-actions-suggestions/get-store-call-to-actions-suggestions.service';
import { GetStoreLocatorStorePagesForEditService } from ':modules/store-locator/services/get-store-locator-store-pages-for-edit/get-store-locator-store-pages-for-edit.service';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
export class ProcessPagesGenerationUseCase {
    constructor(
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _generateStorePageContentService: GenerateStorePageContentService,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _mediasRepository: MediasRepository,
        private readonly _getStoreCallToActionsSuggestionsService: GetStoreCallToActionsSuggestionsService,
        private readonly _getStoreLocatorStorePagesForEditService: GetStoreLocatorStorePagesForEditService
    ) {}

    async execute({ organizationId }: { organizationId: string }): Promise<void> {
        const [storeLocatorOrganizationConfig, restaurants] = await Promise.all([
            this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(organizationId),
            this._restaurantsRepository.find({
                filter: { organizationId, type: BusinessCategory.LOCAL_BUSINESS },
                projection: { _id: 1 },
                options: { lean: true },
            }),
        ]);
        assert(storeLocatorOrganizationConfig, 'Store Locator Organization Config not found');

        const desiredLanguages = [StoreLocatorLanguage.FR];
        await Promise.all(
            restaurants.map(async (restaurant) =>
                Promise.all(
                    desiredLanguages.map(async (lang) =>
                        this._generateRestaurantPage({ restaurantId: restaurant._id.toString(), storeLocatorOrganizationConfig, lang })
                    )
                )
            )
        );

        // Get draft pages to fill in cache and improve performance on first page load
        await this._getStoreLocatorStorePagesForEditService.execute(organizationId);
    }

    private async _generateRestaurantPage({
        restaurantId,
        storeLocatorOrganizationConfig,
        lang,
    }: {
        restaurantId: string;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        lang: StoreLocatorLanguage;
    }): Promise<void> {
        const storeLocatorRestaurantPage = await this._storeLocatorRestaurantPageRepository.findOne({
            filter: {
                restaurantId: toDbId(restaurantId),
                lang,
                status: StoreLocatorPageStatus.DRAFT,
            },
            options: { lean: true },
        });

        if (storeLocatorRestaurantPage) {
            return await this._updateExistingPageContent({ storeLocatorRestaurantPage, restaurantId, storeLocatorOrganizationConfig });
        }

        return await this._createNewPageContent({ restaurantId, storeLocatorOrganizationConfig, lang });
    }

    private async _updateExistingPageContent({
        storeLocatorRestaurantPage,
        restaurantId,
        storeLocatorOrganizationConfig,
    }: {
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
        restaurantId: string;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
    }): Promise<void> {
        const {
            title,
            metaDescription,
            twitterDescription,
            descriptions,
            ctaTitle,
            reviewsTitle,
            galleryTitle,
            gallerySubtitle,
            socialNetworksTitle,
        } = await this._generateStorePageContentService.generateWholePageContent({
            restaurantId,
            storeLocatorOrganizationConfig,
            lang: storeLocatorRestaurantPage.lang,
        });

        // todo store-locator: intersection with restaurant keywords ?
        const keywords = storeLocatorOrganizationConfig.aiSettings.keywords.map((keyword) => keyword.text).join(', ');

        await this._storeLocatorRestaurantPageRepository.updateOne({
            filter: { _id: storeLocatorRestaurantPage._id },
            update: {
                hasBeenUpdated: true,
                blocks: {
                    head: {
                        ...storeLocatorRestaurantPage.blocks.head,
                        title,
                        description: metaDescription,
                        twitterDescription,
                        keywords,
                    },
                    information: {
                        ...storeLocatorRestaurantPage.blocks.information,
                        title,
                    },
                    gallery: {
                        ...storeLocatorRestaurantPage.blocks.gallery,
                        title: galleryTitle,
                        subtitle: gallerySubtitle,
                    },
                    reviews: {
                        ...storeLocatorRestaurantPage.blocks.reviews,
                        title: reviewsTitle,
                    },
                    callToActions: {
                        ...storeLocatorRestaurantPage.blocks.callToActions,
                        title: ctaTitle,
                    },
                    descriptions: {
                        ...storeLocatorRestaurantPage.blocks.descriptions,
                        items: descriptions.map((description, index) => ({
                            ...storeLocatorRestaurantPage.blocks.descriptions.items[index],
                            title: description.title,
                            blocks: description.sections.map(({ subtitle, text }) => ({
                                title: subtitle,
                                text,
                            })),
                        })),
                    },
                    socialNetworks: {
                        ...storeLocatorRestaurantPage.blocks.socialNetworks,
                        title: socialNetworksTitle,
                    },
                },
            },
        });
    }

    private async _createNewPageContent({
        restaurantId,
        storeLocatorOrganizationConfig,
        lang,
    }: {
        restaurantId: string;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        lang: StoreLocatorLanguage;
    }): Promise<void> {
        const [
            {
                pageUrl,
                title,
                metaDescription,
                twitterDescription,
                descriptions,
                ctaTitle,
                reviewsTitle,
                galleryTitle,
                gallerySubtitle,
                socialNetworksTitle,
            },
            callToActionsSuggestions,
            medias,
            schemaOrgCuisineType,
        ] = await Promise.all([
            this._generateStorePageContentService.generateWholePageContent({
                restaurantId,
                storeLocatorOrganizationConfig,
                lang,
            }),
            this._getStoreCallToActionsSuggestionsService.execute(restaurantId),
            this._getImagesFromMedia(restaurantId),
            this._getSchemaOrgCuisineType(restaurantId),
        ]);

        const orderCta = callToActionsSuggestions.orderUrl
            ? { text: 'Commander', url: callToActionsSuggestions.orderUrl }
            : { text: 'Backup', url: callToActionsSuggestions.backup };
        const ctas = Object.entries(callToActionsSuggestions)
            .map(([text, url]) => ({ text, url }))
            .slice(0, 4);
        const keywords = storeLocatorOrganizationConfig.aiSettings.keywords.map((keyword) => keyword.text).join(', ');

        await this._storeLocatorRestaurantPageRepository.create({
            data: {
                restaurantId: toDbId(restaurantId),
                organizationId: toDbId(storeLocatorOrganizationConfig.organizationId),
                lang,
                fullUrl: `${storeLocatorOrganizationConfig.baseUrl}${pageUrl}`,
                relativePath: pageUrl.slice(1),
                status: StoreLocatorPageStatus.DRAFT,
                hasBeenUpdated: true,
                blocks: {
                    head: {
                        title,
                        description: metaDescription,
                        twitterDescription,
                        keywords,
                        schemaOrgCuisineType,
                        facebookImageUrl: medias.head[0].url,
                        twitterImageUrl: medias.head[0].url,
                        snippetImageUrl: medias.head[0].url,
                    },
                    information: {
                        title,
                        image: medias.information[0],
                        ctas: [orderCta],
                    },
                    gallery: {
                        title: galleryTitle,
                        subtitle: gallerySubtitle,
                        images: medias.gallery,
                    },
                    reviews: {
                        title: reviewsTitle,
                        cta: orderCta,
                    },
                    callToActions: {
                        title: ctaTitle,
                        ctas,
                    },
                    descriptions: {
                        items: descriptions.map((description, index) => ({
                            title: description.title,
                            image: medias.descriptions[index],
                            blocks: description.sections.map(({ subtitle, text }) => ({
                                title: subtitle,
                                text,
                            })),
                        })),
                    },
                    socialNetworks: {
                        title: socialNetworksTitle,
                    },
                },
            },
        });
    }

    private async _getImagesFromMedia(restaurantId: string): Promise<{
        head: { url: string; description: string }[];
        information: { url: string; description: string }[];
        gallery: { url: string; description: string }[];
        descriptions: { url: string; description: string }[];
    }> {
        const backup = {
            head: [{ url: DEFAULT_PLACEHOLDER_IMAGE_URL, description: DEFAULT_IMAGE_DESCRIPTION }],
            information: [{ url: DEFAULT_PLACEHOLDER_IMAGE_URL, description: DEFAULT_IMAGE_DESCRIPTION }],
            gallery: Array.from({ length: 7 }, () => ({
                url: DEFAULT_PLACEHOLDER_IMAGE_URL,
                description: DEFAULT_IMAGE_DESCRIPTION,
            })),
            descriptions: Array.from({ length: 2 }, () => ({
                url: DEFAULT_PLACEHOLDER_IMAGE_URL,
                description: DEFAULT_IMAGE_DESCRIPTION,
            })),
        };

        try {
            const medias = await this._mediasRepository.find({
                filter: {
                    restaurantId: toDbId(restaurantId),
                    category: MediaCategory.ADDITIONAL,
                    type: MediaType.PHOTO,
                },
                projection: { dimensions: 1, urls: 1 },
                options: { lean: true },
            });

            if (!medias || medias.length === 0) {
                logger.warn('[STORE_LOCATOR] No medias found for restaurant', { restaurantId });
                return backup;
            }

            // todo store-locator pick best images thanks to dimensions

            // Dispatch medias into information, gallery, ... to have the most different images possible, if there are more than needed, and reusing them the least if images count is not enough
            // information has 1 image, gallery 7, descriptions 2 and head 2
            const usedMediasOccurrence: Record<string, number> = {};
            medias.forEach((media) => (usedMediasOccurrence[media._id.toString()] = usedMediasOccurrence[media._id.toString()] ?? 0));

            const getNextMedia = (): string => {
                const minimumOccurrence = Math.min(...Object.values(usedMediasOccurrence));
                const mediasUsedTheLeast = Object.keys(usedMediasOccurrence).filter(
                    (mediaId) => usedMediasOccurrence[mediaId] === minimumOccurrence
                );
                const nextMediaId = mediasUsedTheLeast[Math.floor(Math.random() * mediasUsedTheLeast.length)];
                usedMediasOccurrence[nextMediaId]++;

                return medias.find((media) => media._id.toString() === nextMediaId)?.urls?.original ?? DEFAULT_PLACEHOLDER_IMAGE_URL;
            };

            const mediasPerSection = {
                head: [{ url: getNextMedia(), description: DEFAULT_IMAGE_DESCRIPTION }],
                information: [{ url: getNextMedia(), description: DEFAULT_IMAGE_DESCRIPTION }],
                gallery: Array.from({ length: 7 }, () => ({ url: getNextMedia(), description: DEFAULT_IMAGE_DESCRIPTION })),
                descriptions: Array.from({ length: 2 }, () => ({ url: getNextMedia(), description: DEFAULT_IMAGE_DESCRIPTION })),
            };

            return mediasPerSection;
        } catch (error) {
            logger.error('[STORE_LOCATOR] Error fetching media URLs', { restaurantId, error });
            return backup;
        }
    }

    private async _getSchemaOrgCuisineType(restaurantId: string): Promise<string> {
        try {
            const restaurant = await this._restaurantsRepository.findOne({
                filter: { _id: toDbId(restaurantId) },
                projection: { categoryId: 1 },
                options: {
                    lean: true,
                    populate: [
                        { path: 'categoryList', select: 'categoryId' },
                        { path: 'category', select: 'categoryId' },
                    ],
                },
            });
            assert(restaurant, 'Restaurant not found');

            const categoryIds = [...restaurant.categoryList, restaurant.category].map(({ categoryId }) => categoryId);

            const schemaOrgCuisineType = categoryIds
                .map((categoryId) => categoriesToServesCuisine[categoryId as GmbCategoryIdEnum])
                .filter(isNotNil)
                .flat();

            if (schemaOrgCuisineType.length === 0) {
                return 'Restaurant';
            }

            return schemaOrgCuisineType.join(', ');
        } catch (err) {
            return 'Restaurant';
        }
    }
}
