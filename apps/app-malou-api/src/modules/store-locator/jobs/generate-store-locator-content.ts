import { Job } from 'agenda';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import { StoreLocatorJobStatus } from '@malou-io/package-utils';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { ProcessPagesGenerationUseCase } from ':modules/store-locator/use-cases/generate-store-locator-pages/process-pages-generation.use-case';

export const generateContentStoreLocatorAttributesValidator = z.object({
    organizationId: z.string(),
    status: z.nativeEnum(StoreLocatorJobStatus),
});

export type GenerateContentStoreLocatorDataAttributes = z.infer<typeof generateContentStoreLocatorAttributesValidator>;

@singleton()
export class GenerateStoreLocatorContentJob extends GenericJobDefinition {
    constructor(private readonly _processPagesGenerationUseCase: ProcessPagesGenerationUseCase) {
        super({
            agendaJobName: AgendaJobName.STORE_LOCATOR_CONTENT_GENERATION,
        });
    }

    async executeJob(job: Job<GenerateContentStoreLocatorDataAttributes>): Promise<void> {
        try {
            const data = await generateContentStoreLocatorAttributesValidator.parseAsync(job.attrs.data);
            const { organizationId } = data;

            await this._processPagesGenerationUseCase.execute({ organizationId });
            if (job.attrs.data) {
                job.attrs.data.status = StoreLocatorJobStatus.SUCCEEDED;
            }
        } catch (error) {
            if (job.attrs.data) {
                job.attrs.data.status = StoreLocatorJobStatus.FAILED;
            }

            throw error; // Re-throw to ensure the job is marked as failed and follow generic path
        } finally {
            await job.save();
        }
    }
}
