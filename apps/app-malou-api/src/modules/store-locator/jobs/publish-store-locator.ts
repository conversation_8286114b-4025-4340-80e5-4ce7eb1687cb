import { Job } from 'agenda';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import { objectIdValidator } from '@malou-io/package-dto';
import { StoreLocatorJobStatus } from '@malou-io/package-utils';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { ProcessStoreLocatorPublicationUseCase } from ':modules/store-locator/use-cases/publish-store/process-store-publication.use-case';

export const publishStoreLocatorAttributesValidator = z.object({
    organizationId: objectIdValidator,
    status: z.nativeEnum(StoreLocatorJobStatus),
    deploymentJobId: objectIdValidator.optional(),
});
export type PublishStoreLocatorDataAttributes = z.infer<typeof publishStoreLocatorAttributesValidator>;

@singleton()
export class PublishStoreLocatorPagesJob extends GenericJobDefinition {
    constructor(private readonly _processStoreLocatorPublicationUseCase: ProcessStoreLocatorPublicationUseCase) {
        super({
            agendaJobName: AgendaJobName.STORE_LOCATOR_PUBLICATION,
        });
    }

    async executeJob(job: Job<PublishStoreLocatorDataAttributes>): Promise<void> {
        try {
            const data = await publishStoreLocatorAttributesValidator.parseAsync(job.attrs.data);
            const { organizationId } = data;

            await this._processStoreLocatorPublicationUseCase.execute({ job, organizationId });
        } catch (err) {
            if (job.attrs.data) {
                job.attrs.data.status = StoreLocatorJobStatus.FAILED;
            }
            await job.save();

            throw err;
        }
    }
}
