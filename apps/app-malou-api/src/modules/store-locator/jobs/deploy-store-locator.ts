import { Job } from 'agenda';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import { StoreLocatorJobStatus } from '@malou-io/package-utils';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import { DeployStoreLocatorService } from ':modules/store-locator/services/deploy-store-locator/deploy-store-locator.service';
import { SlackChannel, SlackService } from ':services/slack.service';

export const deployStoreLocatorValidator = z.object({
    organizationId: z.string(),
    status: z.nativeEnum(StoreLocatorJobStatus),
    workflowRunId: z.string().optional(),
});

export type StoreLocatorDeploymentDataAttributes = z.infer<typeof deployStoreLocatorValidator>;

@singleton()
export class DeployStoreLocator<PERSON><PERSON> extends GenericJobDefinition {
    constructor(
        private readonly _deployStoreLocatorService: DeployStoreLocatorService,
        private readonly _slackService: SlackService
    ) {
        super({
            agendaJobName: AgendaJobName.STORE_LOCATOR_DEPLOYMENT,
        });
    }

    async executeJob(job: Job<StoreLocatorDeploymentDataAttributes>): Promise<void> {
        try {
            const data = await deployStoreLocatorValidator.parseAsync(job.attrs.data);
            const { organizationId } = data;

            logger.info('[STORE_LOCATOR] [Deployment] Starting deployment job', {
                organizationId,
                jobId: job.attrs._id,
            });

            await this._deployStoreLocatorService.execute({ job, organizationId });
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Deployment] Error executing deployment job', {
                jobId: job.attrs._id,
                err,
            });

            if (job.attrs.data) {
                job.attrs.data.status = StoreLocatorJobStatus.FAILED;
            }
            await job.save();

            this._slackService.sendAlert({ data: { err }, channel: SlackChannel.STORE_LOCATOR_ALERTS, shouldPing: true });

            throw err;
        }
    }
}
