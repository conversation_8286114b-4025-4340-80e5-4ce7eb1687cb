import { singleton } from 'tsyringe';

import { EntityRepository, IStoreLocatorMapPage, ReadPreferenceMode, StoreLocatorMapPageModel, toDbId } from '@malou-io/package-models';
import { StoreLocatorPageStatus } from '@malou-io/package-utils';

@singleton()
export class StoreLocatorMapPageRepository extends EntityRepository<IStoreLocatorMapPage> {
    constructor() {
        super(StoreLocatorMapPageModel);
    }

    async getStoreLocatorMapPages(organizationId: string, options: { isForEdit?: boolean } = {}): Promise<IStoreLocatorMapPage[]> {
        return this.find({
            filter: {
                organizationId: toDbId(organizationId),
                status: options.isForEdit ? StoreLocatorPageStatus.DRAFT : StoreLocatorPageStatus.PUBLISHED,
            },
            options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY },
        });
    }
}
