import { singleton } from 'tsyringe';

import {
    GetStoreLocatorDraftStoreDto,
    GetStoreLocatorStorePageDto,
    GetStoreLocatorStorePageInstagramBlockDto,
    storeLocatorStorePageDraftSocialNetworksBlockValidator,
    storeLocatorStorePageSocialNetworksBlockValidator,
} from '@malou-io/package-dto';
import { IStoreLocatorRestaurantPage } from '@malou-io/package-models';
import { getInstagramUserName, SocialNetworkKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { RestaurantPopulatedForStoreLocator } from ':modules/store-locator/services/fetch-store-head/interfaces';
import {
    InstagramProfileData,
    StoreLocatorInstagramDataService,
} from ':modules/store-locator/services/fetch-store-social-networks/social-networks/store-locator-instagram-data.service';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

export type SocialNetworksData = {
    instagramAccounts: Record<string, InstagramProfileData>;
};

type FetchStoreLocatorSocialNetworksBlockResult<T extends boolean | undefined> = {
    success: boolean;
    data: T extends true
        ? GetStoreLocatorDraftStoreDto['socialNetworksBlock'] | undefined
        : GetStoreLocatorStorePageDto['socialNetworksBlock'] | undefined;
};

@singleton()
export class FetchStoreLocatorSocialNetworksBlockService {
    constructor(
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _storeLocatorInstagramDataService: StoreLocatorInstagramDataService,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute<T extends boolean | undefined = false>(params: {
        restaurant: RestaurantPopulatedForStoreLocator;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        socialNetworksData?: SocialNetworksData;
        isForEdit?: T;
    }): Promise<FetchStoreLocatorSocialNetworksBlockResult<T>> {
        const { isForEdit = false, storeLocatorOrganizationConfig, storeLocatorRestaurantPage, restaurant } = params;

        try {
            let socialNetworksData = params.socialNetworksData;
            let socialNetworks;

            if (isForEdit && storeLocatorRestaurantPage.blocks.socialNetworks.backup) {
                try {
                    await storeLocatorStorePageDraftSocialNetworksBlockValidator.parseAsync(
                        storeLocatorRestaurantPage.blocks.socialNetworks.backup
                    );

                    socialNetworks = storeLocatorRestaurantPage.blocks.socialNetworks.backup.socialNetworks as NonNullable<
                        GetStoreLocatorStorePageDto['socialNetworksBlock']
                    >['socialNetworks'];
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [Head block] Failed to validate backup', { err: error });
                }
            }

            if (!socialNetworks) {
                if (!socialNetworksData) {
                    logger.info('[STORE_LOCATOR] Fetching social networks data for store', {
                        restaurantId: storeLocatorRestaurantPage.restaurantId.toString(),
                    });
                    const instagramAccounts = await this._storeLocatorInstagramDataService.fetchInstagramDataForRestaurants({
                        organizationId: storeLocatorRestaurantPage.organizationId.toString(),
                        restaurants: [restaurant],
                    });
                    socialNetworksData = {
                        instagramAccounts,
                    };
                }

                socialNetworks = await this._computeSocialNetworksData({
                    restaurant,
                    storeLocatorRestaurantPage,
                    storeLocatorOrganizationConfig,
                    socialNetworksData,
                });
            }

            const socialNetworksBlock = {
                title: storeLocatorRestaurantPage.blocks.socialNetworks.title,
                socialNetworks,
            };

            const parsedSocialNetworksBlock = isForEdit
                ? await storeLocatorStorePageDraftSocialNetworksBlockValidator.parseAsync(socialNetworksBlock)
                : await storeLocatorStorePageSocialNetworksBlockValidator.parseAsync(socialNetworksBlock);

            logger.info('[STORE_LOCATOR] [SocialNetworks block] SocialNetworks block is valid, updating it as backup and returning it');
            await this._storeLocatorRestaurantPageRepository.updateOne({
                filter: { _id: storeLocatorRestaurantPage._id },
                update: { 'blocks.socialNetworks.backup': parsedSocialNetworksBlock },
            });

            return { success: true, data: parsedSocialNetworksBlock } as FetchStoreLocatorSocialNetworksBlockResult<T>;
        } catch (err) {
            logger.error('[STORE_LOCATOR] [SocialNetworks block] Failed to fetch store socialNetworks, try to return backup', { err });

            if (storeLocatorRestaurantPage.blocks?.socialNetworks?.backup) {
                try {
                    const socialNetworksBlock = storeLocatorRestaurantPage.blocks.socialNetworks.backup;
                    const parsedSocialNetworksBlock =
                        await storeLocatorStorePageSocialNetworksBlockValidator.parseAsync(socialNetworksBlock);

                    return { success: false, data: parsedSocialNetworksBlock } as FetchStoreLocatorSocialNetworksBlockResult<T>;
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [SocialNetworks block] Failed to validate backup', { err: error });
                }
            }

            return { success: false, data: undefined } as FetchStoreLocatorSocialNetworksBlockResult<T>;
        }
    }

    async fetchSocialProfilesData({ organizationId }: { organizationId: string }): Promise<SocialNetworksData> {
        const restaurants = await this._restaurantsRepository.find({
            filter: { organizationId },
            projection: { _id: 1, socialNetworkUrls: 1, name: 1 },
            options: { lean: true },
        });
        const instagramAccounts = await this._storeLocatorInstagramDataService.fetchInstagramDataForRestaurants({
            organizationId,
            restaurants,
        });

        return { instagramAccounts };
    }

    private async _computeSocialNetworksData({
        restaurant,
        storeLocatorRestaurantPage,
        storeLocatorOrganizationConfig,
        socialNetworksData,
    }: {
        restaurant: RestaurantPopulatedForStoreLocator;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        socialNetworksData: SocialNetworksData;
    }): Promise<{ instagram: GetStoreLocatorStorePageInstagramBlockDto | undefined }> {
        const [instagram] = await Promise.all([
            this._computeInstagramData({
                restaurant,
                storeLocatorRestaurantPage,
                storeLocatorOrganizationConfig,
                instagramAccounts: socialNetworksData.instagramAccounts,
            }),
        ]);

        return { instagram };
    }

    private async _computeInstagramData({
        restaurant,
        storeLocatorRestaurantPage,
        storeLocatorOrganizationConfig,
        instagramAccounts,
    }: {
        restaurant: RestaurantPopulatedForStoreLocator;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        instagramAccounts: SocialNetworksData['instagramAccounts'];
    }): Promise<GetStoreLocatorStorePageInstagramBlockDto | undefined> {
        const instagramUrl = restaurant.socialNetworkUrls?.find(({ key }) => key === SocialNetworkKey.INSTAGRAM)?.url;
        if (!instagramUrl) {
            logger.warn('[STORE_LOCATOR] [SocialNetworks block] No Instagram URL found for restaurant', {
                restaurantId: storeLocatorRestaurantPage.restaurantId.toString(),
            });
            return undefined;
        }

        const instagramProfileData = instagramAccounts[getInstagramUserName(instagramUrl) ?? ''];
        if (!instagramProfileData) {
            logger.warn('[STORE_LOCATOR] [SocialNetworks block] No Instagram profile data found for restaurant', {
                restaurantId: storeLocatorRestaurantPage.restaurantId.toString(),
                instagramUrl,
            });
            return undefined;
        }

        return await this._storeLocatorInstagramDataService.mapInstagramDataToSocialNetworksBlockSection({
            restaurant,
            restaurantPage: storeLocatorRestaurantPage,
            storeLocatorOrganizationConfig,
            instagramData: instagramProfileData,
        });
    }
}
