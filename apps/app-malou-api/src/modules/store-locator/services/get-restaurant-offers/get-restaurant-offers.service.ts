import { uniq } from 'lodash';
import { singleton } from 'tsyringe';

import { IRestaurantAttributeWithAttribute } from '@malou-io/package-models';
import { isNotNil, RELEVANT_ATTRIBUTES, RestaurantAttributeValue, StoreLocatorLanguage } from '@malou-io/package-utils';

import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';

@singleton()
export class GetRestaurantOffersService {
    execute({
        restaurantAttributes,
        organizationAttributes,
        lang,
    }: {
        restaurantAttributes: IRestaurantAttributeWithAttribute[];
        organizationAttributes: Pick<
            StoreLocatorOrganizationConfiguration['aiSettings']['attributes'][number],
            'attributeId' | 'attributeName'
        >[];
        lang: StoreLocatorLanguage;
    }): string[] {
        const relevantAttributes = restaurantAttributes
            .filter((restaurantAttribute) => restaurantAttribute.attributeValue === RestaurantAttributeValue.YES)
            .map((restaurantAttribute) => {
                const relevantAttribute = RELEVANT_ATTRIBUTES.find(
                    (attribute) => restaurantAttribute.attribute.attributeId === attribute.attributeId
                );
                const organizationAttribute = organizationAttributes.find(
                    (attribute) => restaurantAttribute.attribute.attributeId === attribute.attributeId
                );
                if (!relevantAttribute || !organizationAttribute) {
                    return undefined;
                }

                return {
                    text: organizationAttribute.attributeName[lang] || relevantAttribute[lang] || organizationAttribute.attributeName.fr,
                    priority: relevantAttribute.priority,
                };
            })
            .filter(isNotNil);

        relevantAttributes.sort((a, b) => a.priority - b.priority);
        return uniq(relevantAttributes.map((attribute) => attribute.text));
    }
}
