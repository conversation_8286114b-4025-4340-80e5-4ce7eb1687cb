import { singleton } from 'tsyringe';

import { GetStoreLocatorStorePageDto, storeLocatorStorePageDescriptionsBlockValidator } from '@malou-io/package-dto';
import { IStoreLocatorRestaurantPage } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
export class FetchStoreLocatorDescriptionsBlockService {
    constructor(private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository) {}

    async execute({
        storeLocatorRestaurantPage,
    }: {
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
    }): Promise<{ success: boolean; data: GetStoreLocatorStorePageDto['descriptionsBlock'] | undefined }> {
        try {
            const descriptionsBlock = {
                items: storeLocatorRestaurantPage.blocks.descriptions.items.map(({ title, image, blocks }) => ({
                    title: title.toUpperCase(),
                    imageUrl: image.url,
                    imageDescription: image.description,
                    blocks,
                })),
            };

            const parsedDescriptionsBlock = await storeLocatorStorePageDescriptionsBlockValidator.parseAsync(descriptionsBlock);

            logger.info('[STORE_LOCATOR] [Descriptions block] Descriptions block is valid, updating it as backup and returning it');
            await this._storeLocatorRestaurantPageRepository.updateOne({
                filter: { _id: storeLocatorRestaurantPage._id },
                update: { 'blocks.descriptions.backup': parsedDescriptionsBlock },
            });

            return { success: true, data: parsedDescriptionsBlock };
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Descriptions block] Failed to fetch store descriptions, try to return backup', { err });

            if (storeLocatorRestaurantPage.blocks?.descriptions?.backup) {
                try {
                    const descriptionsBlock = storeLocatorRestaurantPage.blocks.descriptions.backup;
                    const parsedDescriptionsBlock = await storeLocatorStorePageDescriptionsBlockValidator.parseAsync(descriptionsBlock);

                    return { success: false, data: parsedDescriptionsBlock };
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [Descriptions block] Failed to validate backup', { err: error });
                }
            }

            return { success: false, data: undefined };
        }
    }
}
