import { uniq } from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IStoreLocatorRestaurantPage, ReadPreferenceMode, toDbId } from '@malou-io/package-models';
import { GenerateStoreLocatorContentType, RestaurantAttributeValue, StoreLocatorLanguage } from '@malou-io/package-utils';

import {
    AiStoreLocatorContentService,
    AiStoreLocatorContentType,
    AiStoreLocatorDescriptionsContent,
    GenerateStoreLocatorContentPayload,
} from ':microservices/ai-store-locator-content-generator.service';
import { RestaurantAttributesRepository } from ':modules/restaurant-attributes/restaurant-attributes.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { GetRestaurantOffersService } from ':modules/store-locator/services/get-restaurant-offers/get-restaurant-offers.service';

interface WholeStorePageContent {
    pageUrl: string;
    title: string;
    metaDescription: string;
    twitterDescription: string;
    descriptions: AiStoreLocatorDescriptionsContent['blocks'];
    ctaTitle: string;
    reviewsTitle: string;
    galleryTitle: string;
    gallerySubtitle: string;
    socialNetworksTitle: string;
}

@singleton()
export class GenerateStorePageContentService {
    constructor(
        private readonly _aiStoreLocatorContentService: AiStoreLocatorContentService,
        private readonly _getRestaurantOffersService: GetRestaurantOffersService,
        private readonly _restaurantAttributesRepository: RestaurantAttributesRepository,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async generateWholePageContent({
        restaurantId,
        storeLocatorOrganizationConfig,
        lang,
    }: {
        restaurantId: string;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        lang: StoreLocatorLanguage;
    }): Promise<WholeStorePageContent> {
        const payload = await this._getLambdaPayload({
            restaurantId,
            storeLocatorOrganizationConfig,
            lang,
        });

        const [
            { text: pageUrl },
            { text: title },
            { text: twitterDescription },
            { blocks: descriptions },
            { text: ctaTitle },
            { text: reviewsTitle },
        ] = await Promise.all([
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.RESTAURANT_PAGE_URL_GENERATION,
                payload,
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.H1_TITLE_GENERATION,
                payload,
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.HEAD_META_TWITTER_DESCRIPTION_GENERATION,
                payload,
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_GENERATION,
                payload,
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.CTA_BLOCK_TITLE_GENERATION,
                payload,
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.REVIEWS_BLOCK_TITLE_GENERATION,
                payload,
            }),
        ]);

        const [{ text: metaDescription }, { text: galleryTitle }, { text: socialNetworksTitle }] = await Promise.all([
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.HEAD_META_DESCRIPTION_GENERATION,
                payload: {
                    ...payload,
                    context: [{ [GenerateStoreLocatorContentType.H1_TITLE_GENERATION]: title }],
                },
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.GALLERY_BLOCK_TITLE_GENERATION,
                payload: {
                    ...payload,
                    context: [{ [GenerateStoreLocatorContentType.H1_TITLE_GENERATION]: title }],
                },
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.SOCIAL_MEDIA_BLOCK_TITLE_GENERATION,
                payload: {
                    ...payload,
                    context: [{ [GenerateStoreLocatorContentType.REVIEWS_BLOCK_TITLE_GENERATION]: reviewsTitle }],
                },
            }),
        ]);

        const [{ text: gallerySubtitle }] = await Promise.all([
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.GALLERY_BLOCK_SUBTITLE_GENERATION,
                payload: {
                    ...payload,
                    context: [{ [GenerateStoreLocatorContentType.GALLERY_BLOCK_TITLE_GENERATION]: galleryTitle }],
                },
            }),
        ]);

        return {
            pageUrl,
            title,
            metaDescription,
            twitterDescription,
            descriptions,
            ctaTitle,
            reviewsTitle,
            galleryTitle,
            gallerySubtitle,
            socialNetworksTitle,
        };
    }

    async generateSpecificPageContent<T extends GenerateStoreLocatorContentType>({
        type,
        storeLocatorRestaurantPage,
        storeLocatorOrganizationConfig,
        lang,
        currentContent,
    }: {
        type: T;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
        lang: StoreLocatorLanguage;
        currentContent?: string;
    }): Promise<AiStoreLocatorContentType<T>> {
        let payload = await this._getLambdaPayload({
            restaurantId: storeLocatorRestaurantPage.restaurantId.toString(),
            storeLocatorOrganizationConfig,
            lang,
        });
        const headMetaDescriptionPayload = {
            ...payload,
            context: [{ [GenerateStoreLocatorContentType.H1_TITLE_GENERATION]: storeLocatorRestaurantPage.blocks.information.title }],
        };
        const galleryTitlePayload = {
            ...payload,
            context: [{ [GenerateStoreLocatorContentType.H1_TITLE_GENERATION]: storeLocatorRestaurantPage.blocks.information.title }],
        };
        const gallerySubtitlePayload = {
            ...payload,
            context: [
                {
                    [GenerateStoreLocatorContentType.GALLERY_BLOCK_TITLE_GENERATION]: storeLocatorRestaurantPage.blocks.gallery.title,
                },
            ],
        };
        const socialNetworksTitlePayload = {
            ...payload,
            context: [
                { [GenerateStoreLocatorContentType.REVIEWS_BLOCK_TITLE_GENERATION]: storeLocatorRestaurantPage.blocks.reviews.title },
            ],
        };

        switch (type) {
            case GenerateStoreLocatorContentType.HEAD_META_DESCRIPTION_GENERATION:
                payload = headMetaDescriptionPayload;
                break;
            case GenerateStoreLocatorContentType.GALLERY_BLOCK_TITLE_GENERATION:
                payload = galleryTitlePayload;
                break;
            case GenerateStoreLocatorContentType.GALLERY_BLOCK_SUBTITLE_GENERATION:
                payload = gallerySubtitlePayload;
                break;
            case GenerateStoreLocatorContentType.SOCIAL_MEDIA_BLOCK_TITLE_GENERATION:
                payload = socialNetworksTitlePayload;
                break;
            case GenerateStoreLocatorContentType.RESTAURANT_PAGE_URL_GENERATION:
            case GenerateStoreLocatorContentType.H1_TITLE_GENERATION:
            case GenerateStoreLocatorContentType.CTA_BLOCK_TITLE_GENERATION:
            case GenerateStoreLocatorContentType.REVIEWS_BLOCK_TITLE_GENERATION:
            case GenerateStoreLocatorContentType.HEAD_META_TWITTER_DESCRIPTION_GENERATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_GENERATION:
            case GenerateStoreLocatorContentType.MAP_BLOCK_TITLE_GENERATION:
            case GenerateStoreLocatorContentType.MAP_DESCRIPTION_GENERATION:
            case GenerateStoreLocatorContentType.MAP_TWITTER_DESCRIPTION_GENERATION:
            case GenerateStoreLocatorContentType.MAP_KEYWORDS_GENERATION:
                break;

            // Optimization
            case GenerateStoreLocatorContentType.HEAD_META_DESCRIPTION_OPTIMIZATION:
                payload = {
                    ...headMetaDescriptionPayload,
                    ...(currentContent && { previousGeneration: currentContent }),
                };
                break;
            case GenerateStoreLocatorContentType.GALLERY_BLOCK_TITLE_OPTIMIZATION:
                payload = {
                    ...galleryTitlePayload,
                    ...(currentContent && { previousGeneration: currentContent }),
                };
                break;
            case GenerateStoreLocatorContentType.GALLERY_BLOCK_SUBTITLE_OPTIMIZATION:
                payload = {
                    ...gallerySubtitlePayload,
                    ...(currentContent && { previousGeneration: currentContent }),
                };
                break;
            case GenerateStoreLocatorContentType.SOCIAL_MEDIA_BLOCK_TITLE_OPTIMIZATION:
                payload = {
                    ...socialNetworksTitlePayload,
                    ...(currentContent && { previousGeneration: currentContent }),
                };
                break;
            case GenerateStoreLocatorContentType.H1_TITLE_OPTIMIZATION:
            case GenerateStoreLocatorContentType.CTA_BLOCK_TITLE_OPTIMIZATION:
            case GenerateStoreLocatorContentType.REVIEWS_BLOCK_TITLE_OPTIMIZATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_TITLE_OPTIMIZATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_SUBTITLE_OPTIMIZATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_CONTENT_OPTIMIZATION:
                payload = {
                    ...payload,
                    ...(currentContent && { previousGeneration: currentContent }),
                };
                break;
            default:
                break;
        }

        const response = await this.generateStoreLocatorContent({ type, payload });

        return response;
    }

    async generateStoreLocatorContent<T extends GenerateStoreLocatorContentType>({
        type,
        payload,
    }: {
        type: T;
        payload: GenerateStoreLocatorContentPayload['restaurantData'];
    }): Promise<AiStoreLocatorContentType<T>> {
        const response = await this._aiStoreLocatorContentService.generateStoreLocatorContent(type, payload);

        return response.aiResponse;
    }

    private async _getLambdaPayload({
        restaurantId,
        storeLocatorOrganizationConfig,
        lang,
    }: {
        restaurantId: string;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        lang: StoreLocatorLanguage;
    }): Promise<GenerateStoreLocatorContentPayload['restaurantData']> {
        const organizationKeywords = storeLocatorOrganizationConfig.aiSettings.keywords.map(({ text }) => text);

        return await this.getLambdaPayloadFromOrganizationParams({
            restaurantId,
            organizationConfig: {
                organizationName: storeLocatorOrganizationConfig.organization.name,
                keywords: organizationKeywords,
                tone: storeLocatorOrganizationConfig.aiSettings.tone,
                languageStyle: storeLocatorOrganizationConfig.aiSettings.languageStyle,
                specialAttributes: storeLocatorOrganizationConfig.aiSettings.specialAttributes,
                attributes: storeLocatorOrganizationConfig.aiSettings.attributes,
            },
            lang,
        });
    }

    async getLambdaPayloadFromOrganizationParams({
        restaurantId,
        organizationConfig,
        lang,
    }: {
        restaurantId: string;
        organizationConfig: {
            organizationName: string;
            keywords: string[];
            attributes: Pick<StoreLocatorOrganizationConfiguration['aiSettings']['attributes'][number], 'attributeId' | 'attributeName'>[];
            tone: string[];
            languageStyle: string;
            specialAttributes: { restaurantId: string; text: string }[];
        };
        lang: StoreLocatorLanguage;
    }): Promise<GenerateStoreLocatorContentPayload['restaurantData']> {
        const {
            keywords: organizationKeywords,
            languageStyle,
            organizationName,
            specialAttributes,
            tone: organizationTone,
            attributes: organizationAttributes,
        } = organizationConfig;

        const [restaurant, restaurantAttributes] = await Promise.all([
            this._restaurantsRepository.findOne({
                filter: { _id: toDbId(restaurantId) },
                projection: { _id: 1, name: 1, address: 1 },
                options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY },
            }),
            this._restaurantAttributesRepository.find({
                filter: {
                    restaurantId: toDbId(restaurantId),
                    attributeValue: RestaurantAttributeValue.YES,
                },
                options: {
                    lean: true,
                    populate: [{ path: 'attribute' }],
                    readPreference: ReadPreferenceMode.SECONDARY,
                },
            }),
        ]);
        assert(restaurant);

        const keywords = uniq(organizationKeywords.map((keyword) => keyword.toLowerCase().trim()));
        const brandTone = uniq([...organizationTone, languageStyle].map((tone) => tone.toLowerCase().trim()));
        const restaurantOffers = this._getRestaurantOffersService
            .execute({
                organizationAttributes,
                restaurantAttributes,
                lang,
            })
            .map((text) => text.toLowerCase().trim());
        const specificsDirectives = specialAttributes
            .filter((specialAttribute) => specialAttribute.restaurantId.toString() === restaurantId)
            .map(({ text }) => text.toLowerCase().trim());

        return {
            restaurantName: restaurant.name,
            organizationName,
            address: {
                locality: restaurant.address?.locality,
                postalCode: restaurant.address?.postalCode,
            },
            keywords,
            brandTone,
            restaurantOffers,
            specificsDirectives,
            language: lang,
        };
    }
}
