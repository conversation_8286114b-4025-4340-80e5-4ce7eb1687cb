import { singleton } from 'tsyringe';

import { GetStoreLocatorStorePageDto, storeLocatorStorePageCallToActionsBlockValidator } from '@malou-io/package-dto';
import { IStoreLocatorRestaurantPage } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import { getCtaTrackingEventProps } from ':modules/store-locator/shared/utils';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
export class FetchStoreLocatorCallToActionsBlockService {
    constructor(private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository) {}

    async execute({
        storeLocatorRestaurantPage,
    }: {
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
    }): Promise<{ success: boolean; data: GetStoreLocatorStorePageDto['callToActionsBlock'] | undefined }> {
        try {
            const callToActionsBlock = {
                title: storeLocatorRestaurantPage.blocks.callToActions.title.toUpperCase(),
                links: storeLocatorRestaurantPage.blocks.callToActions.ctas.map((cta) => ({
                    ...cta,
                    tracker: {
                        ...getCtaTrackingEventProps(cta),
                        eventCategory: 'call-to-actions-block',
                    },
                })),
            };
            const parsedCallToActionsBlock = await storeLocatorStorePageCallToActionsBlockValidator.parseAsync(callToActionsBlock);

            logger.info('[STORE_LOCATOR] [CallToActions block] CallToActions block is valid, updating it as backup and returning it');
            await this._storeLocatorRestaurantPageRepository.updateOne({
                filter: { _id: storeLocatorRestaurantPage._id },
                update: { 'blocks.callToActions.backup': parsedCallToActionsBlock },
            });

            return { success: true, data: parsedCallToActionsBlock };
        } catch (err) {
            logger.error('[STORE_LOCATOR] [CallToActions block] Failed to fetch store callToActions, try to return backup', { err });

            if (storeLocatorRestaurantPage.blocks?.callToActions?.backup) {
                try {
                    const callToActionsBlock = storeLocatorRestaurantPage.blocks.callToActions.backup;
                    const parsedCallToActionsBlock = await storeLocatorStorePageCallToActionsBlockValidator.parseAsync(callToActionsBlock);

                    return { success: false, data: parsedCallToActionsBlock };
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [CallToActions block] Failed to validate backup', { err: error });
                }
            }

            return { success: false, data: undefined };
        }
    }
}
