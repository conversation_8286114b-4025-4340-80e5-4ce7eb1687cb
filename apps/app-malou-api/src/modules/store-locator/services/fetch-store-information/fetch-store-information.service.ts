import { isEqual } from 'lodash';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { GetStoreLocatorStorePageDto, storeLocatorStorePageInformationBlockValidator } from '@malou-io/package-dto';
import { IRestaurant, IStoreLocatorRestaurantPage, toDbId } from '@malou-io/package-models';
import {
    Day,
    EntityConstructor,
    formatPhoneForDisplay,
    isNotNil,
    MalouAttributesEnum,
    MalouErrorCode,
    RestaurantAttributeValue,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { RestaurantAttributesRepository } from ':modules/restaurant-attributes/restaurant-attributes.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { TRANSLATED_DAY_OF_THE_WEEK } from ':modules/store-locator/services/fetch-store-information/store-locator-information.constants';
import { GetPaymentMethodsService } from ':modules/store-locator/services/get-payment-methods/get-payment-methods.service';
import { GetRestaurantOffersService } from ':modules/store-locator/services/get-restaurant-offers/get-restaurant-offers.service';
import { getCtaTrackingEventProps } from ':modules/store-locator/shared/utils';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

class Period {
    openTime?: string | null;
    closeTime?: string | null;
    isClosed: boolean;
    openDay: Day;
    closeDay: Day;

    constructor(p: EntityConstructor<Period>) {
        this.isClosed = p.isClosed;
        this.openTime = p.openTime;
        this.closeTime = p.closeTime;
        this.openDay = p.openDay;
        this.closeDay = p.closeDay;
    }

    equals(o: any): boolean {
        return isEqual(this, o);
    }

    compare(o: any): number {
        if (this.openDay < o.openDay) {
            return -1;
        }
        if (this.openDay > o.openDay) {
            return 1;
        }
        if (!this.openTime) {
            return o.openTime ? -1 : 0;
        }
        if (!o.openTime) {
            return 1;
        }
        if (this.openTime < o.openTime) {
            return -1;
        }
        if (this.openTime > o.openTime) {
            return 1;
        }
        return 0;
    }
}

@singleton()
export class FetchStoreLocatorInformationBlockService {
    private readonly _MAX_ATTRIBUTE_COUNT = 7;

    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _restaurantAttributesRepository: RestaurantAttributesRepository,
        private readonly _getPaymentMethodsService: GetPaymentMethodsService,
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _getRestaurantOffersService: GetRestaurantOffersService
    ) {}

    async execute({
        restaurantId,
        storeLocatorOrganizationConfig,
        storeLocatorRestaurantPage,
    }: {
        restaurantId: string;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
    }): Promise<{ success: boolean; data: GetStoreLocatorStorePageDto['informationBlock'] | undefined }> {
        try {
            const [restaurant, restaurantAttributes] = await Promise.all([
                this._restaurantsRepository.findOneOrFail({
                    filter: { _id: toDbId(restaurantId) },
                    projection: {
                        name: 1,
                        phone: 1,
                        address: 1,
                        regularHours: 1,
                        specialHours: 1,
                        openingDate: 1,
                        latlng: 1,
                    },
                    options: { lean: true },
                }),
                this._restaurantAttributesRepository.find({
                    filter: {
                        restaurantId: toDbId(restaurantId),
                        attributeValue: RestaurantAttributeValue.YES,
                    },
                    options: {
                        lean: true,
                        populate: [
                            {
                                path: 'attribute',
                            },
                        ],
                    },
                }),
            ]);

            const isNotOpenedYet =
                isNotNil(restaurant.openingDate) &&
                DateTime.fromJSDate(restaurant.openingDate).startOf('day') > DateTime.now().startOf('day');
            const fullAddress = this._formatRestaurantAddress({
                address: restaurant.address,
                restaurantId,
            });
            const attributesNames = this._getRestaurantOffersService
                .execute({
                    organizationAttributes: storeLocatorOrganizationConfig.aiSettings.attributes,
                    restaurantAttributes,
                    lang: storeLocatorRestaurantPage.lang,
                })
                .slice(0, this._MAX_ATTRIBUTE_COUNT);
            const paymentMethodAttributes = this._getPaymentMethodsService.execute(restaurantAttributes);
            const paymentMethods = [
                ...paymentMethodAttributes
                    // todo store-locator: make this customizable per restaurant
                    // Can't change it in _getPaymentMethodsService because it is also used for head schema.org and should be displayed there
                    .filter((paymentMethod) => paymentMethod.attributeId !== MalouAttributesEnum.PAY_MOBILE_NFC)
                    .map((pM) => this._translatePaymentMethod(pM.attributeId as MalouAttributesEnum)),
                // todo store-locator: Add this attribute to the restaurant attributes
                'Espèces',
            ];
            const hours = this._formatRegularHours(restaurant.regularHours, restaurant.specialHours);
            const restaurantName = storeLocatorRestaurantPage.blocks.information.title;
            const phone = formatPhoneForDisplay({
                prefix: restaurant.phone?.prefix ?? undefined,
                digits: restaurant.phone?.digits ?? undefined,
            });

            const informationBlock = {
                restaurantName,
                imageUrl: storeLocatorRestaurantPage.blocks.information.image.url,
                imageDescription: storeLocatorRestaurantPage.blocks.information.image.description,
                isNotOpenedYet,
                ...(phone && { phone }),
                // itineraryUrl: `https://maps.google.com/?q=${restaurant.latlng.lat},${restaurant.latlng.lng}`,
                itineraryUrl: `https://www.google.com/maps/dir/?api=1&destination=${fullAddress}`,
                coordinates: restaurant.latlng,
                fullAddress,
                attributesNames,
                paymentMethods,
                hours,
                ctas: storeLocatorRestaurantPage.blocks.information.ctas?.map((cta) => ({
                    ...cta,
                    tracker: {
                        ...getCtaTrackingEventProps(cta),
                        eventCategory: 'information-block',
                    },
                })),
            };

            const parsedInformationBlock = await storeLocatorStorePageInformationBlockValidator.parseAsync(informationBlock);

            logger.info('[STORE_LOCATOR] [Information block] Information block is valid, updating it as backup and returning it');
            await this._storeLocatorRestaurantPageRepository.updateOne({
                filter: { _id: storeLocatorRestaurantPage._id },
                update: { 'blocks.information.backup': parsedInformationBlock },
            });

            return { success: true, data: parsedInformationBlock };
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Information block] Failed to fetch store information, try to return backup', { err });

            if (storeLocatorRestaurantPage.blocks?.information?.backup) {
                try {
                    const informationBlock = storeLocatorRestaurantPage.blocks.information.backup;
                    const parsedInformationBlock = await storeLocatorStorePageInformationBlockValidator.parseAsync(informationBlock);

                    return { success: false, data: parsedInformationBlock };
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [Information block] Failed to validate backup', { err: error });
                }
            }

            return { success: false, data: undefined };
        }
    }

    private _formatRegularHours(
        regularHours: IRestaurant['regularHours'],
        specialHours: IRestaurant['specialHours']
    ): { day: Day; periods: Period[]; formattedHour: string }[] {
        const periods = regularHours?.map((h) => new Period(h)) ?? [];

        if (periods.length === 0) {
            return [];
        }

        const restaurantHours = {} as Record<Day, Period[]>;
        const businessHours = [...periods].sort((a, b) => a.compare(b));
        for (const timePeriod of businessHours) {
            if (!(timePeriod.openDay in restaurantHours)) {
                restaurantHours[timePeriod.openDay] = [];
            }
            restaurantHours[timePeriod.openDay].push(timePeriod);
        }

        const formattedHours = [] as { day: Day; periods: Period[]; formattedHour: string }[];
        // sort in order of days
        const daysOfTheWeek = Object.values(Day);
        for (const day of daysOfTheWeek) {
            if (restaurantHours[day]) {
                formattedHours.push({
                    day,
                    periods: restaurantHours[day],
                    formattedHour: this._formatDay(restaurantHours, day),
                });
            }
        }

        const onlyThisWeekSpecialSchedule = specialHours.filter((sH) => {
            const startDate = DateTime.fromObject({
                day: sH.startDate.day,
                month: sH.startDate.month + 1, // because month is 0 indexed
                year: sH.startDate.year,
            }).toJSDate();
            const dayEndOfWeek = DateTime.now().plus({ days: 7 }).endOf('week').toJSDate();
            const now = DateTime.now().startOf('day').toJSDate();
            return startDate >= now && dayEndOfWeek >= startDate;
        });

        for (const specialHour of onlyThisWeekSpecialSchedule) {
            const dayOfTheWeek =
                DateTime.fromObject({
                    day: specialHour.startDate.day,
                    month: specialHour.startDate.month + 1, // because month is 0 indexed
                    year: specialHour.startDate.year,
                }).weekday - 1; // because week starts at 1
            const day = Object.values(Day)[dayOfTheWeek];
            formattedHours[dayOfTheWeek] = {
                day: day,
                periods: [
                    new Period({
                        isClosed: specialHour.isClosed,
                        openTime: specialHour.openTime,
                        closeTime: specialHour.closeTime,
                        openDay: day,
                        closeDay: day,
                    }),
                ],
                formattedHour:
                    TRANSLATED_DAY_OF_THE_WEEK[day] +
                    ' : ' +
                    (specialHour.isClosed ? 'Fermé' : `${specialHour.openTime} - ${specialHour.closeTime}`),
            };
        }

        return formattedHours;
    }

    private _formatRestaurantAddress({ address, restaurantId }: { address: IRestaurant['address']; restaurantId: string }): string {
        // Every restaurant should have an address with at minimum a formattedAddress and a locality
        // example: Emirates restaurants have no postal code
        if (!address || (!address.formattedAddress && !address.locality)) {
            throw new MalouError(MalouErrorCode.STORE_LOCATOR_ADDRESS_NOT_FOUND, { metadata: { restaurantId } });
        }

        // because postal code can be undefined, we filter it out
        // and we always want a format with only one coma
        // example : "2 Allée de la Pacific, 33800 Bordeaux"
        const fullAddress = `${address.formattedAddress}, ${address.postalCode ?? ''} ${address.locality}`;
        return fullAddress;
    }

    private _formatDay(hours: Record<Day, Period[]>, day: Day) {
        return (
            TRANSLATED_DAY_OF_THE_WEEK[day] +
            ' : ' +
            hours[day].map((p) => `${p.isClosed ? 'Fermé' : `${p.openTime} - ${p.closeTime}`}`).join(', ')
        );
    }

    private _translatePaymentMethod(paymentMethod: MalouAttributesEnum): string | undefined {
        switch (paymentMethod) {
            case MalouAttributesEnum.PAY_CREDIT_CARD:
            case MalouAttributesEnum.PAY_CREDIT_CARD_TYPES_ACCEPTED:
            case MalouAttributesEnum.PAY_DEBIT_CARD:
            case MalouAttributesEnum.AMERICAN_EXPRESS:
                return 'Cartes bleues';
            case MalouAttributesEnum.REQUIRES_CASH_ONLY:
                return 'Espèces seulement';
            case MalouAttributesEnum.ACCEPTS_CHEQUE_DEJEUNER_MEAL_VOUCHER:
            case MalouAttributesEnum.ACCEPTS_TICKET_RESTAURANT_MEAL_VOUCHER:
            case MalouAttributesEnum.ACCEPTS_MEAL_COUPONS:
                return 'Titres restaurants';
            case MalouAttributesEnum.ACCEPTS_SODEXO_MEAL_VOUCHER:
                return 'Sodexo';
            case MalouAttributesEnum.PAY_MOBILE_NFC:
                return 'NFC';
            case MalouAttributesEnum.PAY_CHECK:
                return 'Chèques';
            default:
                break;
        }
    }
}
