import { fromBuff<PERSON> } from 'file-type';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import {
    GetStoreLocatorDraftStoreDto,
    GetStoreLocatorReviewDto,
    GetStoreLocatorStorePageDto,
    storeLocatorStorePageDraftReviewsBlockValidator,
    storeLocatorStorePageReviewsBlockValidator,
} from '@malou-io/package-dto';
import { IReview, IStoreLocatorRestaurantPage, toDbId } from '@malou-io/package-models';
import { filterByRequiredKeys, getDefaultReviewAvatar, PlatformKey } from '@malou-io/package-utils';

import { fetchImage } from ':helpers/fetch-image-from-remote';
import { logger } from ':helpers/logger';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { getCtaTrackingEventProps } from ':modules/store-locator/shared/utils';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';
import { AwsS3 } from ':plugins/cloud-storage/s3';

interface ReviewFromDB extends Pick<IReview, '_id' | 'reviewer' | 'text' | 'rating' | 'key' | 'socialSortDate'> {}

type FetchStoreLocatorReviewsBlockResult<T extends boolean | undefined> = {
    success: boolean;
    data: T extends true
        ? GetStoreLocatorDraftStoreDto['reviewsBlock'] | undefined
        : GetStoreLocatorStorePageDto['reviewsBlock'] | undefined;
};
@singleton()
export class FetchStoreLocatorReviewsBlockService {
    private readonly _MINIMUM_REVIEWS_COUNT = 8;
    private readonly _REVIEWS_MAX_AGE_IN_DAYS = 60;
    private readonly _REVIEWS_CONTENT_MIN_LENGTH = 130;

    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _cloudStorageService: AwsS3
    ) {}

    async execute<T extends boolean | undefined = false>(params: {
        restaurantId: string;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
        isForEdit?: T;
    }): Promise<FetchStoreLocatorReviewsBlockResult<T>> {
        const { isForEdit = false, storeLocatorRestaurantPage, restaurantId } = params;

        try {
            let reviews: GetStoreLocatorReviewDto[] | undefined;

            if (isForEdit && storeLocatorRestaurantPage.blocks.reviews.backup) {
                try {
                    await storeLocatorStorePageDraftReviewsBlockValidator.parseAsync(storeLocatorRestaurantPage.blocks.reviews.backup);

                    reviews = storeLocatorRestaurantPage.blocks.reviews.backup.reviews as GetStoreLocatorReviewDto[] | undefined;
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [Reviews block] Failed to validate backup', { err: error });
                }
            }

            if (!reviews) {
                reviews = await this._getReviews({ restaurantId, storeLocatorRestaurantPage });
            }

            const reviewsBlock = {
                title: storeLocatorRestaurantPage.blocks.reviews.title.toUpperCase(),
                cta: {
                    ...storeLocatorRestaurantPage.blocks.reviews.cta,
                    tracker: {
                        ...getCtaTrackingEventProps(storeLocatorRestaurantPage.blocks.reviews.cta),
                        eventCategory: 'reviews-block',
                    },
                },
                ...(reviews && { reviews }),
            };

            const parsedReviewsBlock = isForEdit
                ? await storeLocatorStorePageDraftReviewsBlockValidator.parseAsync(reviewsBlock)
                : await storeLocatorStorePageReviewsBlockValidator.parseAsync(reviewsBlock);

            logger.info('[STORE_LOCATOR] [Reviews block] Reviews block is valid, updating it as backup and returning it');
            await this._storeLocatorRestaurantPageRepository.updateOne({
                filter: { _id: storeLocatorRestaurantPage._id },
                update: { 'blocks.reviews.backup': parsedReviewsBlock },
            });

            return { success: true, data: parsedReviewsBlock } as FetchStoreLocatorReviewsBlockResult<T>;
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Reviews block] Failed to fetch store information, try to return backup', { err });

            if (storeLocatorRestaurantPage.blocks?.reviews?.backup) {
                try {
                    const reviewsBlock = storeLocatorRestaurantPage.blocks.reviews.backup;
                    const parsedReviewsBlock = await storeLocatorStorePageReviewsBlockValidator.parseAsync(reviewsBlock);

                    return { success: false, data: parsedReviewsBlock } as FetchStoreLocatorReviewsBlockResult<T>;
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [Reviews block] Failed to validate backup', { err: error });
                }
            }

            return { success: false, data: undefined } as FetchStoreLocatorReviewsBlockResult<T>;
        }
    }

    private async _getReviews({
        restaurantId,
        storeLocatorRestaurantPage,
    }: {
        restaurantId: string;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
    }): Promise<GetStoreLocatorReviewDto[] | undefined> {
        const reviews = await this._reviewsRepository.find({
            filter: {
                restaurantId: toDbId(restaurantId),
                key: PlatformKey.GMB,
                rating: 5,
                reviewer: { $exists: true },
                lang: storeLocatorRestaurantPage.lang,
                text: { $exists: true, $ne: '' },
                $expr: { $gt: [{ $strLenCP: { $ifNull: ['$text', ''] } }, this._REVIEWS_CONTENT_MIN_LENGTH] },
            },
            projection: { _id: 1, reviewer: 1, text: 1, rating: 1, key: 1, socialSortDate: 1 },
            options: { sort: { socialSortDate: -1 }, limit: this._MINIMUM_REVIEWS_COUNT, lean: true },
        });

        if (reviews.length < this._MINIMUM_REVIEWS_COUNT) {
            return undefined;
        }

        const reviewerProfilePictureProperties = await this._handleReviewerProfilePictures({
            reviews,
            restaurantId,
            organizationId: storeLocatorRestaurantPage.organizationId.toString(),
        });

        // Check if one of the reviews is older than 30 days
        const shouldDisplayPublishedAt = reviews.every((review) => {
            const reviewDate = DateTime.fromJSDate(new Date(review.socialSortDate));
            return DateTime.now().diff(reviewDate, 'days').days <= this._REVIEWS_MAX_AGE_IN_DAYS;
        });

        return filterByRequiredKeys(reviews, ['text', 'rating', 'reviewer']).map((review) => ({
            picture: reviewerProfilePictureProperties[review._id.toString()],
            starsCount: review.rating,
            platformKey: review.key,
            ...(shouldDisplayPublishedAt && {
                publishedAt: review.socialSortDate.toLocaleDateString('fr-FR', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric',
                }),
            }),
            userName: this._capitalizeUserName(review.reviewer.displayName),
            content: review.text,
        }));
    }

    private async _handleReviewerProfilePictures({
        reviews,
        restaurantId,
        organizationId,
    }: {
        reviews: ReviewFromDB[];
        restaurantId: string;
        organizationId: string;
    }): Promise<Record<string, GetStoreLocatorReviewDto['picture']>> {
        // Clear profile pictures folder to avoid infinite growth
        try {
            await this._cloudStorageService.emptyDirectory(
                `store-locator/organization/${organizationId}/restaurants/${restaurantId}/reviews/profile-pictures`
            );
        } catch (err) {
            logger.warn('[STORE_LOCATOR] [Reviews block] Failed to empty profile pictures folder', { err });
        }

        const profilePictures = {};
        await Promise.all(
            reviews.map(async (review) => {
                profilePictures[review._id.toString()] = await this._handleReviewerPicture({ review, restaurantId, organizationId });
            })
        );

        return profilePictures;
    }

    private async _handleReviewerPicture({
        restaurantId,
        organizationId,
        review,
    }: {
        restaurantId: string;
        organizationId: string;
        review: ReviewFromDB;
    }): Promise<GetStoreLocatorReviewDto['picture']> {
        try {
            const pictureUrl = review?.reviewer?.profilePhotoUrl;
            const defaultAvatar = getDefaultReviewAvatar(review.reviewer?.displayName);

            if (!pictureUrl) {
                return defaultAvatar;
            }

            const imageBuffer = await fetchImage(pictureUrl);
            if (!imageBuffer) {
                return defaultAvatar;
            }

            const fileTypeResult = await fromBuffer(imageBuffer);
            if (!fileTypeResult) {
                return defaultAvatar;
            }

            const { ext, mime } = fileTypeResult;
            const s3Key = `store-locator/organization/${organizationId}/restaurants/${restaurantId}/reviews/profile-pictures/${review._id.toString()}.${ext}`;
            const uploadedUrl = await this._cloudStorageService.uploadBuffer({ buffer: imageBuffer, fileKey: s3Key, mimeType: mime });

            return { url: uploadedUrl };
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Reviews block] Error handling reviewer picture', { err });
            return getDefaultReviewAvatar(review.reviewer?.displayName);
        }
    }

    private _capitalizeUserName(userName: string): string {
        return userName
            .split(' ')
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');
    }
}
