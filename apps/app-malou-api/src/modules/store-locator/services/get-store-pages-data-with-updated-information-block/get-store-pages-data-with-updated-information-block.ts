import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { GetStoreLocatorStorePageDto } from '@malou-io/package-dto';
import { IStoreLocatorRestaurantPage, toDbId } from '@malou-io/package-models';
import { MalouErrorCode, StoreLocatorLanguage, StoreLocatorPageStatus } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { FetchStoreLocatorInformationBlockService } from ':modules/store-locator/services/fetch-store-information/fetch-store-information.service';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
export class GetStorePagesDataWithUpdatedInformationBlockService {
    constructor(
        private readonly _fetchStoreLocatorInformationBlockService: FetchStoreLocatorInformationBlockService,
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository
    ) {}

    async execute(
        organizationId: string,
        options: {
            lang?: StoreLocatorLanguage;
            isForEdit?: boolean;
        } = {}
    ): Promise<
        { storeLocatorRestaurantPage: IStoreLocatorRestaurantPage; informationBlock: GetStoreLocatorStorePageDto['informationBlock'] }[]
    > {
        const [storeLocatorOrganizationConfig, storeLocatorRestaurantPages] = await Promise.all([
            this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(organizationId),
            this._storeLocatorRestaurantPageRepository.find({
                filter: {
                    organizationId: toDbId(organizationId),
                    ...(options.lang && { lang: options.lang }),
                    status: options.isForEdit ? StoreLocatorPageStatus.DRAFT : StoreLocatorPageStatus.PUBLISHED,
                },
                options: { lean: true },
            }),
        ]);

        const storesWithInformationBlock = await Promise.all(
            storeLocatorRestaurantPages.map(async (storeLocatorRestaurantPage) => {
                const storeInformationBlock = await this._fetchStoreLocatorInformationBlockService.execute({
                    restaurantId: storeLocatorRestaurantPage.restaurantId.toString(),
                    storeLocatorOrganizationConfig,
                    storeLocatorRestaurantPage,
                });
                assert(
                    storeInformationBlock.data,
                    new MalouError(MalouErrorCode.STORE_LOCATOR_DATA_FETCH_FAILED, {
                        message: 'Failed to fetch store information block',
                        metadata: { organizationId, restaurantId: storeLocatorRestaurantPage.restaurantId.toString() },
                    })
                );
                return {
                    storeLocatorRestaurantPage,
                    informationBlock: storeInformationBlock.data,
                };
            })
        );

        return storesWithInformationBlock;
    }
}
