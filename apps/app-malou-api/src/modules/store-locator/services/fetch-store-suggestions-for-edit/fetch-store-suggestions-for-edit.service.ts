import { singleton } from 'tsyringe';

import { GetStoreLocatorDraftStoreDto } from '@malou-io/package-dto';
import { IStoreLocatorRestaurantPage } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import { GetStoreCallToActionsSuggestionsService } from ':modules/store-locator/services/get-store-call-to-actions-suggestions/get-store-call-to-actions-suggestions.service';

@singleton()
export class FetchStoreLocatorSuggestionsForEditService {
    constructor(private readonly _getStoreCallToActionsSuggestionsService: GetStoreCallToActionsSuggestionsService) {}

    async execute({
        storeLocatorRestaurantPage,
    }: {
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
    }): Promise<GetStoreLocatorDraftStoreDto['suggestionsForEdit'] | undefined> {
        try {
            const [callToActions] = await Promise.all([
                this._getStoreCallToActionsSuggestionsService.execute(storeLocatorRestaurantPage.restaurantId.toString()),
            ]);

            return { callToActions };
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Suggestions] Failed to fetch store suggestions for edit', { err });
            return undefined;
        }
    }
}
