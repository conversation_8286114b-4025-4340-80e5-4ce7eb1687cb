import { singleton } from 'tsyringe';

import { GetStoreLocatorStorePageDto, storeLocatorStorePageGalleryBlockValidator } from '@malou-io/package-dto';
import { IStoreLocatorRestaurantPage } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
export class FetchStoreLocatorGalleryBlockService {
    constructor(private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository) {}

    async execute({
        storeLocatorRestaurantPage,
    }: {
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
    }): Promise<{ success: boolean; data: GetStoreLocatorStorePageDto['galleryBlock'] | undefined }> {
        try {
            const galleryBlock = {
                title: storeLocatorRestaurantPage.blocks.gallery.title.toUpperCase(),
                subtitle: storeLocatorRestaurantPage.blocks.gallery.subtitle,
                images: storeLocatorRestaurantPage.blocks.gallery.images,
            };

            const parsedGalleryBlock = await storeLocatorStorePageGalleryBlockValidator.parseAsync(galleryBlock);

            logger.info('[STORE_LOCATOR] [Gallery block] Gallery block is valid, updating it as backup and returning it');
            await this._storeLocatorRestaurantPageRepository.updateOne({
                filter: { _id: storeLocatorRestaurantPage._id },
                update: { 'blocks.gallery.backup': parsedGalleryBlock },
            });

            return { success: true, data: parsedGalleryBlock };
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Gallery block] Failed to fetch store gallery, try to return backup', { err });

            if (storeLocatorRestaurantPage.blocks?.gallery?.backup) {
                try {
                    const galleryBlock = storeLocatorRestaurantPage.blocks.gallery.backup;
                    const parsedGalleryBlock = await storeLocatorStorePageGalleryBlockValidator.parseAsync(galleryBlock);

                    return { success: false, data: parsedGalleryBlock };
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [Gallery block] Failed to validate backup', { err: error });
                }
            }

            return { success: false, data: undefined };
        }
    }
}
