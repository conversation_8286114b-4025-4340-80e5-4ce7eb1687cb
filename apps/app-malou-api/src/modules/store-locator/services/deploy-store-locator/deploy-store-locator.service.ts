import { Job } from 'agenda';
import { singleton } from 'tsyringe';

import { StoreLocatorJobStatus } from '@malou-io/package-utils';

import { Config } from ':config';
import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import { StoreLocatorDeploymentDataAttributes } from ':modules/store-locator/jobs/deploy-store-locator';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';
import { GithubWorkflowName } from ':providers/github/github.interfaces';
import { GithubProvider } from ':providers/github/github.provider';

@singleton()
export class DeployStoreLocatorService {
    constructor(
        private readonly _githubProvider: GithubProvider,
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _agendaSingleton: AgendaSingleton
    ) {}

    async execute({ job, organizationId }: { job: Job<StoreLocatorDeploymentDataAttributes>; organizationId: string }): Promise<void> {
        const organizationStoreLocatorConfiguration = await this._storeLocatorOrganizationConfigRepository.findOne({
            filter: { organizationId },
            options: { lean: true },
        });

        if (!organizationStoreLocatorConfiguration) {
            logger.warn('[STORE_LOCATOR] [Deployment] No configuration found for this organization', {
                organizationId,
            });
            return;
        }

        // Delete scheduled deployment jobs
        const deletedJobsCount = await this._agendaSingleton.deleteJobs({
            name: AgendaJobName.STORE_LOCATOR_DEPLOYMENT,
            'data.organizationId': organizationId,
            'data.status': StoreLocatorJobStatus.PENDING,
            nextRunAt: { $gte: new Date() },
        });
        if (deletedJobsCount ?? 0 > 0) {
            logger.info('[STORE_LOCATOR] [Deployment] Deleted scheduled deployment jobs', {
                organizationId,
                deletedJobsCount,
            });
        }

        // Any ongoing deployments will be cancelled by the github workflow concurrency
        // and their status will be updated after the workflow run is cancelled
        logger.info('[STORE_LOCATOR] [Deployment] About to trigger deployment', {
            organizationId,
        });

        // This is used only for monitoring purposes, status update will effectively be handled
        // through a webhook called after the workflow run is completed
        const jobs = await this._agendaSingleton.jobs({
            name: AgendaJobName.STORE_LOCATOR_DEPLOYMENT,
            'data.organizationId': organizationId,
            'data.status': StoreLocatorJobStatus.PENDING,
            _id: { $ne: job.attrs._id },
        });
        if (jobs.length > 0) {
            logger.info('[STORE_LOCATOR] [Deployment] Ongoing deployments will be cancelled', {
                organizationId,
                ongoingJobsCount: jobs.length,
                jobIds: jobs.map((j) => j.attrs?._id?.toString()),
            });
        }

        await this._githubProvider.triggerWorkflow({
            workflowName: GithubWorkflowName.DEPLOY_STORE_LOCATOR,
            inputs: {
                organizationId,
                ...(job.attrs._id && { jobId: job.attrs._id.toString() }),
                environment: Config.env,
            },
        });

        logger.info('[STORE_LOCATOR] [Deployment] Triggered', {
            organizationId,
            branchName: Config.branchName,
        });
    }
}
