import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { SlackService } from ':services/slack.service';

import { ScheduleStoreLocatorDeploymentService } from './schedule-store-locator-deployment.service';

describe('ScheduleStoreLocatorDeploymentService', () => {
    let service: ScheduleStoreLocatorDeploymentService;
    let agendaSingleton: AgendaSingleton;
    let restaurantsRepository: RestaurantsRepository;
    let slackService: SlackService;

    beforeEach(() => {
        container.reset();
        agendaSingleton = container.resolve(AgendaSingleton);
        restaurantsRepository = container.resolve(RestaurantsRepository);
        slackService = container.resolve(SlackService);
        service = new ScheduleStoreLocatorDeploymentService(agendaSingleton, restaurantsRepository, slackService);
    });

    it('should not schedule a job if one already exists for the organization', async () => {
        const restaurantId = newDbId();
        const orgId = newDbId();
        const restaurant = {
            _id: restaurantId,
            organizationId: orgId,
            storeLocatorPages: [{}],
            organization: { _id: orgId, storeLocatorConfig: {} },
        };
        jest.spyOn(restaurantsRepository, 'findOneOrFail').mockResolvedValueOnce(restaurant as any);
        jest.spyOn(agendaSingleton, 'jobs').mockResolvedValueOnce([{}] as any);
        const scheduleSpy = jest.spyOn(agendaSingleton, 'schedule');
        await service.execute({ restaurantId: restaurantId.toString() });
        expect(scheduleSpy).not.toHaveBeenCalled();
    });

    it('should schedule a job if none exists', async () => {
        const restaurantId = newDbId();
        const orgId = newDbId();
        const restaurant = {
            _id: restaurantId,
            organizationId: orgId,
            storeLocatorPages: [{}],
            organization: { _id: orgId, storeLocatorConfig: {} },
        };
        jest.spyOn(restaurantsRepository, 'findOneOrFail').mockResolvedValueOnce(restaurant as any);
        jest.spyOn(agendaSingleton, 'jobs').mockResolvedValueOnce([]);
        const scheduleSpy = jest.spyOn(agendaSingleton, 'schedule').mockResolvedValueOnce(undefined as any);
        await service.execute({ restaurantId: restaurantId.toString() });
        expect(scheduleSpy).toHaveBeenCalled();
    });
});
