import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { StoreLocatorJobStatus } from '@malou-io/package-utils';

import { Config } from ':config';
import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class ScheduleStoreLocatorDeploymentService {
    private readonly _DELAY_IN_MINUTES_BEFORE_DEPLOY = 30;

    constructor(
        private readonly _agendaSingleton: AgendaSingleton,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _slackService: SlackService
    ) {}

    async execute({ restaurantId }: { restaurantId: string }): Promise<void> {
        try {
            logger.info('[STORE_LOCATOR] Checking for deployment', {
                restaurantId,
            });

            const restaurant = await this._restaurantsRepository.findOneOrFail({
                filter: {
                    _id: toDbId(restaurantId),
                },
                options: {
                    lean: true,
                    populate: [
                        { path: 'organization', populate: [{ path: 'storeLocatorConfig' }] },
                        {
                            path: 'storeLocatorPages',
                        },
                    ],
                },
            });

            // Check if organization has a store locator configured
            if (!restaurant.storeLocatorPages?.length || !restaurant.organization?.storeLocatorConfig) {
                logger.warn('[STORE_LOCATOR] Not configured for this restaurant / organization', {
                    restaurantId,
                    ...(restaurant.organization?._id && { organizationId: restaurant.organization._id.toString() }),
                });
                return;
            }

            const organizationId = restaurant.organizationId.toString();
            const jobs = await this._agendaSingleton.jobs({
                name: AgendaJobName.STORE_LOCATOR_DEPLOYMENT,
                'data.organizationId': organizationId,
                nextRunAt: { $gte: new Date() },
            });
            if (jobs?.length > 0) {
                logger.info('[STORE_LOCATOR] Deployment already scheduled, skipping', {
                    restaurantId,
                    organizationId,
                });
                return;
            }

            const scheduledDate = this._getDeploymentDate();
            logger.info('[STORE_LOCATOR] Scheduling deployment', {
                restaurantId,
                organizationId,
                scheduledDate,
            });
            await this._agendaSingleton.schedule(scheduledDate, AgendaJobName.STORE_LOCATOR_DEPLOYMENT, {
                organizationId,
                status: StoreLocatorJobStatus.PENDING,
            });

            logger.info('[STORE_LOCATOR] Deployment scheduled', {
                restaurantId,
                organizationId,
                scheduledDate,
            });
        } catch (err) {
            logger.error('[STORE_LOCATOR] Error scheduling deployment', {
                restaurantId,
                err,
            });

            this._slackService.sendAlert({ data: { err }, channel: SlackChannel.STORE_LOCATOR_ALERTS, shouldPing: true });
        }
    }

    handleSchedulingError(err: Error): void {
        logger.error('[STORE_LOCATOR] Error scheduling deployment', {
            err,
        });

        this._slackService.sendAlert({ data: { err }, channel: SlackChannel.STORE_LOCATOR_ALERTS, shouldPing: true });
    }

    private _getDeploymentDate(): Date {
        const minutes = Config.env === 'production' ? this._DELAY_IN_MINUTES_BEFORE_DEPLOY : 5;

        return DateTime.now().plus({ minutes }).toJSDate();
    }
}
