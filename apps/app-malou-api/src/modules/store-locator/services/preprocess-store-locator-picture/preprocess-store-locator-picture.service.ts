import { from<PERSON><PERSON><PERSON> } from 'file-type';
import sharp from 'sharp';
import { singleton } from 'tsyringe';

import { isNotNil, StoreLocatorLanguage } from '@malou-io/package-utils';

import { fetchImage } from ':helpers/fetch-image-from-remote';
import { logger } from ':helpers/logger';
import { AiMediaDescriptionService } from ':microservices/ai-media-description.service';
import { GenerateMediaDescriptionImageType } from ':modules/ai/interfaces/ai.interfaces';
import { AwsS3 } from ':plugins/cloud-storage/s3';

@singleton()
export class PreprocessStoreLocatorPictureService {
    private readonly _MAX_SIZE = 3000;
    private readonly _MAX_SIZE_FOR_AI = 2048;

    constructor(
        private readonly _cloudStorageService: AwsS3,
        private readonly _aiMediaDescriptionService: AiMediaDescriptionService
    ) {}

    async execute({
        image: { buffer, url, s3Key },
        metadata: { imageType, restaurantName, keywords, lang },
    }: {
        image: { buffer?: Buffer; url?: string; s3Key: string };
        metadata: {
            imageType: GenerateMediaDescriptionImageType;
            restaurantName: string;
            keywords: string[];
            lang: StoreLocatorLanguage;
        };
    }): Promise<{ url: string; text: string } | undefined> {
        try {
            const bufferToProcess = buffer || (url ? await fetchImage(url) : undefined);
            if (!bufferToProcess) {
                logger.error('[STORE_LOCATOR] No image buffer or URL provided', { s3Key });
                return undefined;
            }

            // Resize to max 2048px for AI analysis
            const [resizedBuffer, bufferForAi] = await Promise.all([
                this._resizeImage({ buffer: bufferToProcess, maxSize: this._MAX_SIZE }),
                this._resizeImage({ buffer: bufferToProcess, maxSize: this._MAX_SIZE_FOR_AI, toJpeg: true }),
            ]);

            // Upload to S3
            const uploadResult = await this._uploadPicture({
                buffer: resizedBuffer,
                bufferForAi,
                s3Key,
            });

            if (!uploadResult) {
                logger.error('[STORE_LOCATOR] Failed to upload picture', {
                    s3Key,
                });
                return undefined;
            }
            const { uploadedUrl, uploadedUrlForAi } = uploadResult;

            // Get description alt
            const shouldGenerateDescription = isNotNil(imageType);
            let text;
            if (shouldGenerateDescription) {
                const data = await this._aiMediaDescriptionService.generateMediaDescription({
                    imageLink: uploadedUrlForAi,
                    imageType,
                    language: lang,
                    keywords,
                    restaurantName,
                });
                text = data.aiResponse.description;
            }

            return {
                url: uploadedUrl,
                ...(text && { text }),
            };
        } catch (err) {
            logger.error('[STORE_LOCATOR] Failed to upload picture', {
                s3Key,
                err,
            });

            return undefined;
        }
    }

    async uploadImage({
        buffer,
        s3Key,
    }: {
        buffer: Buffer;
        s3Key: string;
    }): Promise<{ uploadedUrl: string; uploadedUrlForAi: string } | undefined> {
        try {
            // Resize to max 2048px for AI analysis
            const [resizedBuffer, bufferForAi] = await Promise.all([
                this._resizeImage({ buffer, maxSize: this._MAX_SIZE }),
                this._resizeImage({ buffer, maxSize: this._MAX_SIZE_FOR_AI, toJpeg: true }),
            ]);

            // Upload to S3
            const uploadResult = await this._uploadPicture({
                buffer: resizedBuffer,
                bufferForAi,
                s3Key,
            });

            if (!uploadResult) {
                logger.error('[STORE_LOCATOR] Failed to upload picture', {
                    s3Key,
                });
                return undefined;
            }

            return uploadResult;
        } catch (err) {
            logger.error('[STORE_LOCATOR] Failed to upload image', {
                s3Key,
                err,
            });
            return undefined;
        }
    }

    private async _uploadPicture({
        buffer,
        bufferForAi,
        s3Key,
    }: {
        buffer: Buffer;
        bufferForAi: Buffer;
        s3Key: string;
    }): Promise<{ uploadedUrl: string; uploadedUrlForAi: string } | undefined> {
        const [fileTypeResult, fileTypeResultForAi] = await Promise.all([fromBuffer(buffer), fromBuffer(bufferForAi)]);
        if (!fileTypeResult || !fileTypeResultForAi) {
            logger.error('[STORE_LOCATOR] Failed to get file type when uploading picture', {
                s3Key,
            });
            return undefined;
        }

        const { ext, mime } = fileTypeResult;
        const { ext: extForAi, mime: mimeForAi } = fileTypeResultForAi;
        const [uploadedUrl, uploadedUrlForAi] = await Promise.all([
            this._cloudStorageService.uploadBuffer({
                buffer: buffer,
                fileKey: `${s3Key}.${ext}`,
                mimeType: mime,
            }),
            this._cloudStorageService.uploadBuffer({
                buffer: bufferForAi,
                fileKey: `${s3Key}-for-ai.${extForAi}`,
                mimeType: mimeForAi,
            }),
        ]);

        logger.info('[STORE_LOCATOR] Uploaded image to S3', {
            s3Key: `${s3Key}.${ext}`,
            s3KeyForAi: `${s3Key}-for-ai.${extForAi}`,
        });

        return { uploadedUrl, uploadedUrlForAi };
    }

    private async _resizeImage({ buffer, maxSize, toJpeg }: { buffer: Buffer; maxSize: number; toJpeg?: boolean }): Promise<Buffer> {
        // Get metadata to check dimensions
        const metadata = await sharp(buffer).metadata();
        const isPortrait = (metadata.height || 0) > (metadata.width || 0);

        // Resize based on the longest side
        const transform = sharp(buffer)
            .rotate() // Prevents EXIF-based auto-rotation
            .resize({
                width: isPortrait ? undefined : maxSize,
                height: isPortrait ? maxSize : undefined,
                fit: 'inside', // maintain aspect ratio
                withoutEnlargement: true, // prevent upsizing if smaller
            });

        // Convert to JPEG format
        if (toJpeg) {
            transform.jpeg({
                quality: 80, // reasonable balance between quality and size
                chromaSubsampling: '4:2:0', // standard for better compression
            });
        }

        return await transform.toBuffer();
    }
}
