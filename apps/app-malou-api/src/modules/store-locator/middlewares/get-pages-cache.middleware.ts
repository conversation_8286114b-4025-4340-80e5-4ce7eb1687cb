import { TimeInSeconds } from '@malou-io/package-utils';

import { cacheMiddleware, CachePrefixKey } from ':plugins/cache-middleware';

export function getPagesFromCacheMiddleware(req: any, res: any, next: any) {
    const isDevMode = req.query.isDevMode === 'true';

    if (!isDevMode) {
        return next();
    }

    return cacheMiddleware(CachePrefixKey.GET_STORE_LOCATOR_PAGES, 20 * TimeInSeconds.MINUTE)(req, res, next);
}
