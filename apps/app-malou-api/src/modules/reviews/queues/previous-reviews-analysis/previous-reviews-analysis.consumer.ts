import { isNil } from 'lodash';
import { SQSMessage } from 'sqs-consumer';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import { MalouErrorCode } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { AiPayloadOptions } from ':modules/ai/interfaces/ai.interfaces';
import { PreviousReviewsAnalysisService } from ':modules/ai/services/previous-reviews-analysis/previous-reviews-analysis.service';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { UseCaseQueueTag } from ':queues/sqs-template/constant';
import { GenericSqsConsumer } from ':queues/sqs-template/generic-sqs-consumer';

const previousReviewsAnalysisMessageValidator = z.object({
    reviewId: z.string(),
    lang: z.string().optional(),
});

@singleton()
export class PreviousReviewsAnalysisConsumer extends GenericSqsConsumer {
    constructor(
        private readonly _previousReviewsAnalysisService: PreviousReviewsAnalysisService,
        private readonly _reviewsRepository: ReviewsRepository
    ) {
        super({
            useCaseQueueTag: UseCaseQueueTag.PREVIOUS_REVIEWS_ANALYSIS,
            queueUrl: Config.services.sqs.previousReviewsAnalysisFifoQueueUrl,
        });
    }

    async handleMessage(msg: SQSMessage): Promise<void> {
        const body = msg.Body ? JSON.parse(msg.Body) : null;
        if (isNil(body)) {
            throw new MalouError(MalouErrorCode.SQS_MESSAGE_NOT_FOUND, { message: 'body is nil' });
        }

        const { reviewId, lang } = previousReviewsAnalysisMessageValidator.parse(JSON.parse(msg.Body!));
        const review = await this._reviewsRepository.getReviewById(reviewId);
        if (!review) {
            throw new MalouError(MalouErrorCode.REVIEW_NOT_FOUND, { message: `Review with id ${reviewId} not found` });
        }
        const previousReviews = await this._previousReviewsAnalysisService.getReviewCommentsSample(
            review,
            AiPayloadOptions.previousReviewsSampleSize,
            lang
        );
        await this._previousReviewsAnalysisService.analyzePreviousReviews({
            review,
            restaurantId: review.restaurantId.toString(),
            previousReviews,
        });
    }
}
