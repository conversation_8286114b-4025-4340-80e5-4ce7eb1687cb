import { singleton } from 'tsyringe';

import { Config } from ':config';
import { UseCaseQueueTag } from ':queues/sqs-template/constant';
import { GenericSqsProducer } from ':queues/sqs-template/generic-sqs-producer';

export type StartPreviousReviewsAnalysisProducerPayload = {
    reviewId: string;
    lang?: string;
    restaurantId: string;
};

@singleton()
export class PreviousReviewsAnalysisProducer extends GenericSqsProducer<StartPreviousReviewsAnalysisProducerPayload> {
    constructor() {
        super({
            useCaseQueueTag: UseCaseQueueTag.PREVIOUS_REVIEWS_ANALYSIS,
            queueUrl: Config.services.sqs.previousReviewsAnalysisFifoQueueUrl,
        });
    }
    async execute(payload: StartPreviousReviewsAnalysisProducerPayload): Promise<void> {
        await this.sendMessage(payload, { groupId: payload.restaurantId });
    }
}
