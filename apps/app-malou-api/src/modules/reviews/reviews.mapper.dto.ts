import { autoInjectable } from 'tsyringe';

import {
    CommentReviewDto,
    CreatePrivateReviewDto,
    GetRestaurantsReviewsResponseDto,
    GetRestaurantsReviewsV2ResponseDto,
    LightReviewForInsightDto,
    PaginationDto,
    ReviewResponseDto,
    ReviewWithTranslationsResponseDto,
    SemanticAnalysisInsightsReviewDto,
} from '@malou-io/package-dto';
import { IPrivateReview, IReview, IReviewComment } from '@malou-io/package-models';
import { PlatformKey, ReviewType } from '@malou-io/package-utils';

import { ReviewPagination } from ':helpers/pagination';
import { KeywordsDtoMapper } from ':modules/keywords/keywords.mapper.dto';
import { PrivateReviewsDtoMapper } from ':modules/private-reviews/private-reviews.dto-mapper';
import {
    PrivateReviewWithScanWithNfc,
    PrivateReviewWithSemanticAnalysisAndSentiment,
    PrivateReviewWithTranslations,
} from ':modules/private-reviews/private-reviews.interface';
import { ReviewAnalysesDtoMapper } from ':modules/review-analyses/mappers/review-analyses.mapper.dto';
import {
    ReviewWithSemanticAnalysis,
    ReviewWithSemanticAnalysisAndSentiment,
    ReviewWithTranslations,
} from ':modules/reviews/reviews.interfaces';
import { Translations } from ':modules/translations/entities/translations.entity';

@autoInjectable()
export class ReviewsDtoMapper {
    constructor(
        private readonly _keywordsMapper: KeywordsDtoMapper,
        private readonly _reviewAnalysesDtoMapper: ReviewAnalysesDtoMapper,
        private readonly _privateReviewsDtoMapper: PrivateReviewsDtoMapper
    ) {}

    toReviewResponseDto(model: IReview | IPrivateReview): ReviewResponseDto {
        return model.key === PlatformKey.PRIVATE
            ? this._privateReviewsDtoMapper.toPrivateReviewResponseDto(model as IPrivateReview)
            : this._toReviewResponseDto(model as IReview);
    }

    toReviewWithTranslationsResponseDto(model: ReviewWithTranslations | PrivateReviewWithTranslations): ReviewWithTranslationsResponseDto {
        return this._isPrivateReviewWithTranslations(model)
            ? this._privateReviewsDtoMapper.toPrivateReviewWithTranslationsResponseDto(model)
            : this._toReviewWithTranslationsResponseDto(model);
    }

    toGetRestaurantsReviewsResponseDto(
        model: (IReview | PrivateReviewWithScanWithNfc)[],
        pagination: ReviewPagination
    ): GetRestaurantsReviewsResponseDto {
        const reviewsDto: ReviewResponseDto[] = model.map((review) =>
            review.key === PlatformKey.PRIVATE
                ? this._privateReviewsDtoMapper.toPrivateReviewWithScanResponseDto(review as PrivateReviewWithScanWithNfc)
                : this._toReviewResponseDto(review as IReview)
        );

        const paginationDto: PaginationDto = {
            pageNumber: pagination.pageNumber,
            pageSize: pagination.pageSize,
            total: pagination.total,
            skip: pagination.skip,
        };
        return { reviews: reviewsDto, pagination: paginationDto };
    }

    toGetRestaurantsReviewsV2ResponseDto(
        reviews: (ReviewWithSemanticAnalysis | PrivateReviewWithScanWithNfc)[],
        pagination: { pageSize: number; skip: number }
    ): GetRestaurantsReviewsV2ResponseDto {
        const reviewsDto: ReviewWithTranslationsResponseDto[] = reviews.map((review) =>
            this._isPrivateReview(review)
                ? this._privateReviewsDtoMapper.fromPrivateReviewWithScanWithNfcToReviewWithTranslationsResponseDto(review)
                : this._toReviewWithTranslationsResponseDto(review)
        );

        return { reviews: reviewsDto, pagination };
    }

    toCreatePrivateReview(dto: CreatePrivateReviewDto['privateReview']): Partial<IPrivateReview> {
        return this._privateReviewsDtoMapper.toCreatePrivateReview(dto);
    }

    toSemanticAnalysisInsightsReviewDto(
        model: ReviewWithSemanticAnalysisAndSentiment | PrivateReviewWithSemanticAnalysisAndSentiment
    ): SemanticAnalysisInsightsReviewDto {
        return this._isPrivateReviewWithTranslations(model)
            ? this._privateReviewsDtoMapper.toSemanticAnalysisInsightsPrivateReviewDto(model)
            : this._toSemanticAnalysisInsightsReviewDto(model);
    }

    toLightReviewForInsightsDto(model: IPrivateReview | IReview): LightReviewForInsightDto {
        return {
            id: model._id.toString(),
            key: model.key as PlatformKey,
            restaurantId: model.restaurantId.toString(),
            rating: model.rating ? Math.round(model.rating) : undefined,
        };
    }

    private _toReviewResponseDto(model: IReview): ReviewResponseDto {
        const {
            socialId,
            socialLink,
            businessSocialLink,
            text,
            title,
            socialTranslatedText,
            lang,
            rating,
            socialRating,
            reviewer,
            archived,
            wasAnsweredAutomatically,
            platformPresenceStatus,
            keywordsLang,
            intelligentSubjects,
        } = model;

        const dto: ReviewResponseDto = {
            id: model._id?.toString(),
            _id: model._id?.toString(),
            platformId: model.platformId?.toString(),
            restaurantId: model.restaurantId?.toString(),
            key: model.key as PlatformKey,
            socialId,
            socialLink: socialLink ?? undefined,
            businessSocialLink,
            type: model.type as ReviewType,
            text: text ?? undefined,
            title,
            socialTranslatedText: socialTranslatedText ?? undefined,
            lang: lang ?? undefined,
            rating: rating ? Math.round(rating) : undefined,
            socialRating: socialRating ? Math.round(socialRating) : undefined,
            reviewer: reviewer
                ? {
                      displayName: reviewer.displayName ?? undefined,
                      email: reviewer.email ?? undefined,
                      profilePhotoUrl: reviewer.profilePhotoUrl ?? undefined,
                      socialId: reviewer.socialId ?? undefined,
                      socialUrl: reviewer.socialUrl ?? undefined,
                  }
                : undefined,
            archived: archived ?? false,
            wasAnsweredAutomatically: wasAnsweredAutomatically ?? false,
            platformPresenceStatus,
            keywordsLang,
            socialCreatedAt: model.socialCreatedAt?.toISOString(),
            socialUpdatedAt: model.socialUpdatedAt?.toISOString(),
            comments: model.comments.filter((comment) => !!comment).map((comment) => this._toCommentReviewDto(comment)),
            socialAttachments: model.socialAttachments ?? [],
            semanticAnalysisFetchStatus: model.semanticAnalysisFetchStatus ?? null,
            aiRelevantBricks:
                model.aiRelevantBricks?.map((brick) => ({
                    ...brick,
                    translationsId: brick.translationsId?.toString(),
                })) ?? undefined,
            aiRelatedBricksCount: model.aiRelatedBricksCount,
            ratingTags: model.ratingTags,
            intelligentSubjects: intelligentSubjects ?? [],
            publicBusinessId: model.publicBusinessId,
            reviewerNameValidation: model.reviewerNameValidation ?? undefined,
            responseStyle: model.responseStyle ?? undefined,
        };

        if ([PlatformKey.UBEREATS, PlatformKey.DOORDASH].includes(model.key)) {
            dto.menuItemReviews = model.menuItemReviews ?? [];
            dto.eaterTotalOrders = model.eaterTotalOrders;
            dto.order = model.order
                ? {
                      workflowId: model.order?.workflowId ?? undefined,
                      deliveredAt: model.order?.deliveredAt ?? undefined,
                      orderTotal: model.order?.orderTotal ?? undefined,
                      currencyCode: model.order?.currencyCode ?? undefined,
                      appVariant: model.order?.appVariant,
                  }
                : undefined;
        }
        return dto;
    }

    private _toReviewWithTranslationsResponseDto(
        model: ReviewWithSemanticAnalysis | ReviewWithTranslations
    ): ReviewWithTranslationsResponseDto {
        const reviewResponseDto = this._toReviewResponseDto(model as any);
        const translations = model.translations
            ? new Translations({ ...model.translations, id: model.translations._id.toString() })
            : undefined;

        const aiRelevantBricks = model.aiRelevantBricks?.map((brick) => ({
            ...brick,
            translations: brick.translations
                ? new Translations({ ...brick.translations, id: brick.translations._id.toString() })?.toDto()
                : undefined,
        }));

        return {
            ...reviewResponseDto,
            translations: translations?.toDto(),
            aiRelevantBricks: aiRelevantBricks,
            semanticAnalysis:
                this._isReviewWithSemanticAnalysis(model) && model.semanticAnalysis
                    ? (this._reviewAnalysesDtoMapper.fromSemanticAnalysisToReviewAnalysisDto(model.semanticAnalysis) ?? undefined)
                    : undefined,
            semanticAnalysisSegments:
                this._isReviewWithSemanticAnalysis(model) && model.semanticAnalysisSegments
                    ? this._reviewAnalysesDtoMapper.fromSemanticAnalysisToReviewAnalysisDtoV2(
                          model.semanticAnalysisSegments,
                          model.restaurantId.toString()
                      )
                    : undefined,
        };
    }

    private _toCommentReviewDto(comment: IReviewComment): CommentReviewDto {
        const { socialId, text, socialTranslatedText, user, posted, isMalou, retries } = comment;

        return {
            ...comment,
            _id: comment._id.toString(),
            id: comment._id.toString(),
            socialId: socialId ?? undefined,
            text,
            socialTranslatedText: socialTranslatedText ?? undefined,
            keywordAnalysis: this._keywordsMapper.toKeywordAnalysisDto(comment.keywordAnalysis ?? undefined),
            socialUpdatedAt: comment.socialUpdatedAt?.toISOString(),
            user: user?.socialId ? { socialId: user.socialId, displayName: user.displayName ?? undefined } : undefined,
            posted,
            isMalou: isMalou ?? false,
            author: comment.author
                ? {
                      _id: comment.author._id?.toString(),
                      name: comment.author.name ?? undefined,
                      picture: comment.author.picture ?? undefined,
                  }
                : undefined,
            templateIdUsed: comment.templateIdUsed?.toString(),
            aiInteractionIdUsed: comment.aiInteractionIdUsed?.toString(),
            isRepliedFromAggregatedView: comment.isRepliedFromAggregatedView,
            retries,
            content: undefined,
        };
    }

    private _toSemanticAnalysisInsightsReviewDto(model: ReviewWithSemanticAnalysisAndSentiment): SemanticAnalysisInsightsReviewDto {
        return {
            id: model._id.toString(),
            key: model.key as PlatformKey,
            socialId: model.socialId,
            restaurantId: model.restaurantId.toString(),
            text: model.text ?? undefined,
            rating: model.rating ?? undefined,
            lang: model.lang ?? undefined,
            reviewer: {
                displayName: model.reviewer?.displayName ?? undefined,
                profilePhotoUrl: model.reviewer?.profilePhotoUrl ?? undefined,
            },
            socialCreatedAt: model.socialCreatedAt.toISOString(),
            sentiment: model.sentiment,
            semanticAnalysisSegments:
                this._isReviewWithSemanticAnalysis(model) && model.semanticAnalysisSegments
                    ? this._reviewAnalysesDtoMapper.fromSemanticAnalysisToReviewAnalysisDtoV2(
                          model.semanticAnalysisSegments,
                          model.restaurantId.toString()
                      )
                    : undefined,
            translations: model.translations
                ? new Translations({
                      id: model.translations._id.toString(),
                      fr: model.translations.fr,
                      en: model.translations.en,
                      es: model.translations.es,
                      it: model.translations.it,
                      language: model.translations.language,
                      source: model.translations.source,
                  }).toDto()
                : undefined,
            ratingTags: model.ratingTags ?? undefined,
            menuItemReviews: model.menuItemReviews,
        };
    }

    private _isPrivateReview(review: ReviewWithSemanticAnalysis | PrivateReviewWithScanWithNfc): review is PrivateReviewWithScanWithNfc {
        return review.key === PlatformKey.PRIVATE;
    }

    private _isPrivateReviewWithTranslations(
        review: { key: PlatformKey } | PrivateReviewWithTranslations
    ): review is PrivateReviewWithTranslations {
        return review.key === PlatformKey.PRIVATE;
    }

    private _isReviewWithSemanticAnalysis(model: ReviewWithSemanticAnalysis | ReviewWithTranslations): model is ReviewWithSemanticAnalysis {
        return 'semanticAnalysis' in model || 'semanticAnalysisSegments' in model; // TODO: Remove semanticAnalysis condition when feature toggle 'release-new-semantic-analysis' is removed
    }
}
