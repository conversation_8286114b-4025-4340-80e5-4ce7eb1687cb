import { container } from 'tsyringe';

import { IReview, newDbId } from '@malou-io/package-models';
import { Civility } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { PreviousReviewsAnalysisProducer } from ':modules/reviews/queues/previous-reviews-analysis/previous-reviews-analysis.producer';
import { StartPreviousReviewsAnalysisService } from ':modules/reviews/services/start-previous-reviews-analysis/start-previous-reviews-analysis.service';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';

let startPreviousReviewsAnalysisService: StartPreviousReviewsAnalysisService;
let previousReviewsAnalysisProducer: PreviousReviewsAnalysisProducer;
let previousReviewsAnalysisProducerSpy: jest.SpyInstance;

describe('StartPreviousReviewsAnalysisService', () => {
    beforeEach(() => {
        container.clearInstances();

        registerRepositories(['ReviewsRepository']);

        startPreviousReviewsAnalysisService = container.resolve(StartPreviousReviewsAnalysisService);

        previousReviewsAnalysisProducer = container.resolve(PreviousReviewsAnalysisProducer);
        previousReviewsAnalysisProducer.execute = jest.fn().mockResolvedValue(undefined);
        previousReviewsAnalysisProducerSpy = jest.spyOn(previousReviewsAnalysisProducer, 'execute');
    });

    describe('execute', () => {
        it('should not send message if review already has previous reviews analysis data on review', async () => {
            const restaurantId = newDbId();
            const testCase = new TestCaseBuilderV2<'reviews'>({
                seeds: {
                    reviews: {
                        data() {
                            return [
                                getDefaultReview()
                                    .text('Hello')
                                    .socialCreatedAt(new Date())
                                    .restaurantId(restaurantId)
                                    .matchedReviewsIds([newDbId()])
                                    .reviewerNameValidation({
                                        gender: Civility.FEMALE,
                                        firstName: 'Jane',
                                        lastName: 'Doe',
                                        isFirstNameValid: true,
                                        isLastNameValid: true,
                                    })
                                    .responseStyle({
                                        responseStructure: 'short',
                                        toneOfVoice: 'friendly',
                                        style: 'casual',
                                    })
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const review = seededObjects.reviews[0] as IReview;

            await startPreviousReviewsAnalysisService.execute({
                review,
            });

            expect(previousReviewsAnalysisProducerSpy).not.toHaveBeenCalled();
        });

        it('should send message if review passed all conditions', async () => {
            const restaurantId = newDbId();
            const testCase = new TestCaseBuilderV2<'reviews'>({
                seeds: {
                    reviews: {
                        data() {
                            return [getDefaultReview().text('Hello').socialCreatedAt(new Date()).restaurantId(restaurantId).build()];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const review = seededObjects.reviews[0] as IReview;

            await startPreviousReviewsAnalysisService.execute({
                review,
            });

            expect(previousReviewsAnalysisProducerSpy).toHaveBeenCalledOnce();
        });
    });
});
