import { DbId, ICredential, IPlatform, IReview } from '@malou-io/package-models';
import {
    MediaType,
    PlatformKey,
    PlatformPresenceStatus,
    PostedStatus,
    SemanticAnalysisFetchStatus,
    UbereatsPromotionValue,
} from '@malou-io/package-utils';

import { AIResponseStyle, AiReviewerNameValidation } from ':microservices/ai-previous-review-analysis.service';
import { Platform } from ':modules/platforms/platforms.entity';
import { PlatformHeaderConfigOptions } from ':modules/providers/use-cases';
import { DeliverooReply, DeliverooReplyPayload, DeliverooReview } from ':modules/reviews/platforms/deliveroo/interface';
import { DoordashReplyPayload } from ':modules/reviews/platforms/doordash/interface';
import { FacebookReply, FacebookReplyPayload, FacebookReview } from ':modules/reviews/platforms/facebook/facebook-review-mapper';
import { FoursquareReply, FoursquareReplyPayload, FoursquareReview } from ':modules/reviews/platforms/foursquare/foursquare-review-mapper';
import { GmbReplyPayload, GmbReview, GmbReviewReply } from ':modules/reviews/platforms/gmb/interface';
import {
    LafourchetteReply,
    LafourchetteReplyPayload,
    LafourchetteReview,
} from ':modules/reviews/platforms/lafourchette/lafourchette-review-mapper';
import { SevenroomsReplyPayload } from ':modules/reviews/platforms/sevenrooms/interface';
import {
    TripadvisorReply,
    TripadvisorReplyPayload,
    TripadvisorReview,
} from ':modules/reviews/platforms/tripadvisor/tripadvisor-review-mapper';
import { YelpReply, YelpReplyPayload, YelpReview } from ':modules/reviews/platforms/yelp/yelp-review-mapper';
import { ZenchefReply, ZenchefReplyPayload, ZenchefReview } from ':modules/reviews/platforms/zenchef/zenchef-review-mapper';
import { SevenroomsReply, SevenroomsReview } from ':providers/sevenrooms/sevenrooms.provider.interface';

export type ReviewCommentInput = {
    /** external ID of the comment on the platform */
    socialId?: string;

    text?: string;

    socialUpdatedAt?: Date;

    posted?: PostedStatus;

    user?: {
        socialId?: string;
        displayName?: string;
    };
};

export type ReviewAttachmentInput = {
    urls: { original: string; small: string | null };
    type: MediaType.PHOTO | MediaType.VIDEO;
};

export type ReviewInputRating = 1 | 2 | 3 | 4 | 5 | null;

export type ReviewReviewer = {
    /** external ID on the platform */
    socialId?: string;

    /** external URL to the user profile on the platform */
    socialUrl?: string;

    profilePhotoUrl?: string;
    displayName?: string;
    email?: string;
    doordashConsumerId?: number; // specific to Doordash, used to reply to reviews
};

/**
 * A review fetched from the external platform and ready to be inserted in our database.
 * This is the output type of the mappers.
 */
export type ReviewInput = {
    key: PlatformKey;

    /** external ID of the platform */
    socialId: string;

    /** external URL of the review on the platform */
    socialLink: string | null;

    /** external URL of the business on the platform */
    businessSocialLink: string | null;

    text: string | null;

    socialCreatedAt: Date | null;
    socialUpdatedAt: Date | null;

    socialAttachments: ReviewAttachmentInput[];

    title?: string;

    /** The rating normalized for the Malou App */
    rating: ReviewInputRating;

    /** The original rating value on the platform (not necessarily between 1 and 5). */
    socialRating: number | null;

    lang: string | null;

    reviewer?: ReviewReviewer;

    comments: ReviewCommentInput[];

    platformPresenceStatus: PlatformPresenceStatus;

    semanticAnalysisFetchStatus?: SemanticAnalysisFetchStatus;

    aiRelatedBricksCount?: number | null;
    aiRelevantBricks?: { category: string; text: string; translationsIds?: string }[] | null;
    responseStyle?: AIResponseStyle;
    matchedReviewsIds?: string[];
    reviewerNameValidation?: AiReviewerNameValidation;
    publicBusinessId?: string;
};

export type GmbReviewCommentInput = ReviewCommentInput & {
    /** text translated by the platform (if available) */
    socialTranslatedText: string | null;
};

export type UbereatsReviewInput = ReviewInput & {
    ratingTags: string[];
    menuItemReviews: UbereatsMenuItemReview[];
    eaterTotalOrders: number;
    isReplyScheduled: boolean;
    order: UbereatsReviewOrder;
};

export type DoordashReviewInput = ReviewInput & {
    eaterTotalOrders?: number;
    order?: DoordashReviewOrder;
};

export type UbereatsReview = any; // TODO typer
export type UbereatsReply = any; // TODO typer
export type UbereatsReplyPayload = { comment: string; ubereatsPromotionValue?: UbereatsPromotionValue };
export type UbereatsMenuItemReview = {
    socialId: string;
    rating: number;
    name: string;
    comment: string;
    tags: string[];
};
export type UbereatsReviewOrder = {
    workflowId: string;
    deliveredAt: Date;
    orderTotal: number;
    currencyCode: string | null; // 'EUR'
    appVariant: string; // 'UBEREATS'
};

export type DoordashReviewOrder = {
    workflowId: string;
    deliveredAt: Date;
    currencyCode: string | null; // null
    appVariant: PlatformKey.DOORDASH;
};

export type UbereatsReviewCommentInput = ReviewCommentInput & {
    ubereatsPromotionAmountInHundredths: number | undefined;
};
export type YelpReviewCommentInput = ReviewCommentInput | { text: string };

export type TripadvisorReviewCommentInput = { text: string; posted: PostedStatus };
export type LafourchetteReviewCommentInput = { text: string; socialUpdatedAt: Date };
export type PagesJaunesReviewCommentInput = { text: string; socialUpdatedAt: Date };
export type FoursquareReviewCommentInput = { text: string; socialUpdatedAt: Date };
export type ZenchefReviewCommentInput = { text: string; socialUpdatedAt: Date; posted: PostedStatus };

/** A ReviewInput with GMB-specific fields */
export type GmbReviewInput = ReviewInput & {
    key: PlatformKey.GMB;

    comments: GmbReviewCommentInput[];

    /** text translated by the platform (if available) */
    socialTranslatedText: string | null;
};

/** A ReviewInput with Facebook-specific fields */
export type FacebookReviewInput = ReviewInput & {
    key: PlatformKey.FACEBOOK;

    /**
     * Always 'rating' for recent Facebook reviews (a while ago 'visitor_post' was
     * a possible value but it is no longer supported).
     */
    type: 'rating';
};

/** A ReviewInput with Deliveroo-specific fields */
export type DeliverooReviewInput = ReviewInput & {
    key: PlatformKey.DELIVEROO;
    ratingTags: string[];
};

export type ReviewInputWithRestaurantAndPlatformIds = ReviewInput & { restaurantId: DbId; platformId: DbId };

export interface PlatformReviewsUseCases {
    reply: ({
        review,
        comment,
        credential,
        platform,
        restaurantId,
        headerConfig,
    }: {
        review: IReview;
        comment: PlatformReplyPayload;
        credential?: ICredential;
        platform?: Platform;
        restaurantId?: string;
        headerConfig?: PlatformHeaderConfigOptions;
    }) => Promise<any>;
    pushReviewComment: ({ socialId, key, comment }) => Promise<any>;
    updateComment;
    getReviewsData: (
        {
            socialId,
            restaurantId,
            platformId,
            restaurantSocialId,
            credential,
        }: {
            socialId?: string;
            restaurantId?: string;
            platformId?: string;
            restaurantSocialId?: string;
            credential?: ICredential;
        },
        recentOnly: boolean,
        shouldForceUpdate?: boolean
    ) => Promise<PlatformReview | PlatformReview[]>;

    /**
     * Important: the return type of this function will be replaced by ReviewInput[]
     * in the future.
     */
    mapReviewsDataToMalou: (platform: IPlatform, platformReviews: PlatformReview[]) => ReviewInputWithRestaurantAndPlatformIds[];

    fetchTotalReviewCount: (restaurantId: string) => Promise<number>;
}

export type PlatformReply =
    | DeliverooReply
    | YelpReply
    | TripadvisorReply
    | ZenchefReply
    | UbereatsReply
    | LafourchetteReply
    | GmbReviewReply
    | FoursquareReply
    | FacebookReply
    | SevenroomsReply;

export type PlatformReplyPayload =
    | DeliverooReplyPayload
    | YelpReplyPayload
    | TripadvisorReplyPayload
    | ZenchefReplyPayload
    | UbereatsReplyPayload
    | LafourchetteReplyPayload
    | GmbReplyPayload
    | FoursquareReplyPayload
    | FacebookReplyPayload
    | SevenroomsReplyPayload
    | DoordashReplyPayload;

export type PlatformReview =
    | DeliverooReview
    | YelpReview
    | TripadvisorReview
    | ZenchefReview
    | UbereatsReview
    | LafourchetteReview
    | GmbReview
    | FoursquareReview
    | FacebookReview
    | SevenroomsReview;
