import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { ReviewerNameValidationDto } from '@malou-io/package-dto';

import { AiPayloadOptions } from ':modules/ai/interfaces/ai.interfaces';
import { PreviousReviewsAnalysisService } from ':modules/ai/services/previous-reviews-analysis/previous-reviews-analysis.service';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';

@singleton()
export class GetReviewerNameValidationUseCase {
    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _previousReviewsAnalysisService: PreviousReviewsAnalysisService
    ) {}

    async execute(reviewId: string, lang: string): Promise<ReviewerNameValidationDto> {
        const review = await this._reviewsRepository.getReviewById(reviewId);
        assert(review, 'Review not found for reviewer name validation');
        const previousReviews = await this._previousReviewsAnalysisService.getReviewCommentsSample(
            review,
            AiPayloadOptions.previousReviewsSampleSize,
            lang
        );
        const restaurantId = review.restaurantId.toString();
        const previousMatchedReviewsAnalysis = await this._previousReviewsAnalysisService.analyzePreviousReviews({
            review,
            restaurantId,
            previousReviews,
        });
        return previousMatchedReviewsAnalysis.reviewerNameValidation;
    }
}
