import { singleton } from 'tsyringe';

import { ReviewWithTranslationsResponseDto } from '@malou-io/package-dto';
import { PlatformPresenceStatus } from '@malou-io/package-utils';

import { ReviewsDtoMapper } from ':modules/reviews/reviews.mapper.dto';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { UpdateReviewRelevantBricksService } from ':modules/reviews/services/update-review-relevant-bricks.service';

@singleton()
export class UpdateReviewRelevantBricksUseCase {
    constructor(
        private readonly _updateReviewRelevantBricksService: UpdateReviewRelevantBricksService,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _reviewsMapper: ReviewsDtoMapper
    ) {}

    async execute({ reviewId, userId }: { reviewId: string; userId?: string }): Promise<ReviewWithTranslationsResponseDto> {
        await this._updateReviewRelevantBricksService.execute({ reviewId, userId });
        const review = (await this._reviewsRepository.findOne({
            filter: { _id: reviewId, platformPresenceStatus: PlatformPresenceStatus.FOUND },
            options: {
                lean: true,
                populate: [
                    { path: 'semanticAnalysis' },
                    { path: 'translations' },
                    {
                        path: 'aiRelevantBricks',
                        populate: [
                            {
                                path: 'translations' as any,
                            },
                        ],
                    } as any,
                ],
            },
            // need to cast because of typescript strict false that do not infer well the type
        })) as any;

        return this._reviewsMapper.toReviewWithTranslationsResponseDto(review);
    }
}
