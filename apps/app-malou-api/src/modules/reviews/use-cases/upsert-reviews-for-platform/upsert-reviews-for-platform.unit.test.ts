import { omit } from 'lodash';
import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DbId, IReview, newDbId } from '@malou-io/package-models';
import {
    CaslRole,
    IntelligentSubjectName,
    PlatformKey,
    PlatformPresenceStatus,
    PostedStatus,
    ReviewAnalysisSentiment,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { GenericAiServiceResponseType } from ':microservices/ai-lambda-template/generic-ai-service';
import { ReviewsIntelligentSubjectsDetectionService } from ':microservices/reviews-intelligent-subjects-detection.service';
import { DetectIntelligentSubjectsResponse } from ':modules/ai/interfaces/ai.interfaces';
import { getDefaultIntelligentSubjectAutomation } from ':modules/automations/tests/intelligent-subject-automation.builder';
import MailingUseCases from ':modules/mailing/use-cases';
import { getDefaultOrganization } from ':modules/organizations/organization.builder';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { GmbReview, GmbStarRating } from ':modules/reviews/platforms/gmb/interface';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { GenerateKeywordAnalysisForCommentService } from ':modules/reviews/services/generate-keyword-analysis-for-comment.service';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { UpsertReviewsForPlatformUseCase } from ':modules/reviews/use-cases/upsert-reviews-for-platform/upsert-reviews-for-platform.use-case';
import { StartReviewSemanticAnalysisService } from ':modules/segment-analyses/services/start-review-semantic-analysis.service';
import { getDefaultUser, getDefaultUserRestaurant } from ':modules/users/tests/user.builder';
import * as experimentationModule from ':services/experimentations-service/experimentation.service';
import { SlackService } from ':services/slack.service';
import { GenerateLanguageDetectionService } from ':services/text-translator/generate-language-detection.service';

const defaultKeywordAnalysis = {
    keywords: [],
    score: 1.5,
    count: 0,
};

const defaultDetectedLang = 'en';

const defaultAiResponse = [
    { intelligentSubject: IntelligentSubjectName.HYGIENE, isDetected: true, sentiment: ReviewAnalysisSentiment.POSITIVE },
    { intelligentSubject: IntelligentSubjectName.DISCRIMINATION, isDetected: false, sentiment: undefined },
];

let generateKeywordAnalysisForCommentService: GenerateKeywordAnalysisForCommentService;
let startSemanticAnalysisService: StartReviewSemanticAnalysisService;
let generateLanguageDetectionService: GenerateLanguageDetectionService;

describe('UpsertReviewsForPlatform', () => {
    class ReviewsIntelligentSubjectsDetectionServiceMock {
        async detectIntelligentSubjects(_params: any): Promise<GenericAiServiceResponseType<DetectIntelligentSubjectsResponse>> {
            return Promise.resolve({ aiResponse: defaultAiResponse, aiInteractionDetails: [] });
        }
    }

    beforeEach(() => {
        container.clearInstances();
        registerRepositories([
            'ReviewsRepository',
            'OrganizationsRepository',
            'RestaurantsRepository',
            'PlatformsRepository',
            'UsersRepository',
            'UserRestaurantsRepository',
            'IntelligentSubjectAutomationsRepository',
        ]);

        startSemanticAnalysisService = {} as StartReviewSemanticAnalysisService;
        startSemanticAnalysisService.execute = jest.fn().mockResolvedValue(null);
        container.registerInstance(StartReviewSemanticAnalysisService, startSemanticAnalysisService);

        generateKeywordAnalysisForCommentService = {} as GenerateKeywordAnalysisForCommentService;
        generateKeywordAnalysisForCommentService.execute = jest.fn().mockResolvedValue(defaultKeywordAnalysis);
        container.registerInstance(GenerateKeywordAnalysisForCommentService, generateKeywordAnalysisForCommentService);

        generateLanguageDetectionService = {} as GenerateLanguageDetectionService;
        generateLanguageDetectionService.execute = jest.fn().mockResolvedValue(defaultDetectedLang);
        container.registerInstance(GenerateLanguageDetectionService, generateLanguageDetectionService);

        const slackServiceMock = {
            sendMessage: jest.fn(),
            createContextForSlack: jest.fn(),
            sendAlert: jest.fn(),
        } as unknown as SlackService;
        container.register(SlackService, { useValue: slackServiceMock });

        container.register(ReviewsIntelligentSubjectsDetectionService, {
            useValue: new ReviewsIntelligentSubjectsDetectionServiceMock() as any,
        });

        jest.spyOn(experimentationModule, 'isFeatureAvailableForRestaurant').mockResolvedValue(true);
    });

    describe('execute', () => {
        it('should create some reviews in the database', async () => {
            const restaurantId = newDbId();
            const randomPlatformKey = PlatformKey.GMB;

            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants' | 'platforms' | 'organizations'>({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().build()];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [getDefaultRestaurant()._id(restaurantId).organizationId(dependencies.organizations()[0]._id).build()];
                        },
                    },
                    platforms: {
                        data() {
                            return [getDefaultPlatform().restaurantId(restaurantId).key(randomPlatformKey).build()];
                        },
                    },

                    reviews: {
                        data() {
                            return [];
                        },
                    },
                },
            });

            await testCase.build();
            const upsertReviewsForPlatformUseCase = container.resolve(UpsertReviewsForPlatformUseCase);
            const reviewsRepository = container.resolve(ReviewsRepository);

            const reviews: GmbReview[] = [
                {
                    name: 'Review 1',
                    starRating: GmbStarRating.FIVE,
                    reviewId: '1',
                    comment: 'Happy comment',
                    createTime: new Date().toISOString(),
                    updateTime: new Date().toISOString(),
                    reviewer: {
                        displayName: 'John Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                },
                {
                    name: 'Review 2',
                    starRating: GmbStarRating.FIVE,
                    reviewId: '2',
                    comment: 'Unhappy comment',
                    createTime: new Date().toISOString(),
                    updateTime: new Date().toISOString(),
                    reviewer: {
                        displayName: 'Janet Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                },
            ];

            await upsertReviewsForPlatformUseCase.execute(reviews, PlatformKey.GMB, restaurantId);

            const insertedReviews = await reviewsRepository.find({ filter: {} });
            expect(insertedReviews).toHaveLength(2);
            expect(insertedReviews.map((review) => review.text).sort()).toEqual(['Happy comment', 'Unhappy comment'].sort());
        });

        it('should update reviewPublicBusinessIdCount from organization when adding reviews', async () => {
            const restaurantId = newDbId();
            const randomPlatformKey = PlatformKey.GMB;

            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants' | 'platforms' | 'organizations'>({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().reviewPublicBusinessIdCount(0).build()];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [getDefaultRestaurant()._id(restaurantId).organizationId(dependencies.organizations()[0]._id).build()];
                        },
                    },
                    platforms: {
                        data() {
                            return [getDefaultPlatform().restaurantId(restaurantId).key(randomPlatformKey).build()];
                        },
                    },

                    reviews: {
                        data() {
                            return [];
                        },
                    },
                },
            });

            await testCase.build();
            const upsertReviewsForPlatformUseCase = container.resolve(UpsertReviewsForPlatformUseCase);
            const reviewsRepository = container.resolve(ReviewsRepository);
            const organizationsRepository = container.resolve(OrganizationsRepository);

            const reviews: GmbReview[] = [
                {
                    name: 'Review 1',
                    starRating: GmbStarRating.FIVE,
                    reviewId: '1',
                    comment: 'Happy comment',
                    createTime: new Date().toISOString(),
                    updateTime: new Date().toISOString(),
                    reviewer: {
                        displayName: 'John Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                },
                {
                    name: 'Review 2',
                    starRating: GmbStarRating.FIVE,
                    reviewId: '2',
                    comment: 'Unhappy comment',
                    createTime: new Date().toISOString(),
                    updateTime: new Date().toISOString(),
                    reviewer: {
                        displayName: 'Janet Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                },
            ];

            await upsertReviewsForPlatformUseCase.execute(reviews, PlatformKey.GMB, restaurantId);

            const insertedReviews = await reviewsRepository.find({ filter: {} });
            expect(insertedReviews).toHaveLength(2);

            const organizations = await organizationsRepository.find({ filter: {}, options: { lean: true } });

            expect(organizations).toHaveLength(1);
            expect(organizations[0].reviewPublicBusinessIdCount).toBe(2);
        });

        it('should soft delete some reviews that have disappeared on the platform', async () => {
            const restaurantId = newDbId();
            const platformId = newDbId();
            const randomPlatformKey = PlatformKey.GMB;
            const fifteenthFebruaryTwentyFourLuxon = DateTime.fromISO('2024-02-15');

            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants' | 'platforms' | 'organizations'>({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().reviewPublicBusinessIdCount(0).build()];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [getDefaultRestaurant()._id(restaurantId).organizationId(dependencies.organizations()[0]._id).build()];
                        },
                    },
                    platforms: {
                        data() {
                            return [getDefaultPlatform().restaurantId(restaurantId).key(randomPlatformKey)._id(platformId).build()];
                        },
                    },

                    reviews: {
                        data() {
                            return [
                                getDefaultReview()
                                    .restaurantId(restaurantId)
                                    .platformId(platformId)
                                    .key(randomPlatformKey)
                                    .socialId('randomId')
                                    .platformPresenceStatus(PlatformPresenceStatus.FOUND)
                                    .socialUpdatedAt(fifteenthFebruaryTwentyFourLuxon.toJSDate())
                                    .text('This will disappear')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const upsertReviewsForPlatformUseCase = container.resolve(UpsertReviewsForPlatformUseCase);
            const reviewsRepository = container.resolve(ReviewsRepository);

            const reviews: GmbReview[] = [
                {
                    name: 'Review 1',
                    starRating: GmbStarRating.FIVE,
                    reviewId: '1',
                    comment: 'Happy comment',
                    createTime: fifteenthFebruaryTwentyFourLuxon.minus({ days: 1 }).toISO(),
                    updateTime: fifteenthFebruaryTwentyFourLuxon.minus({ days: 1 }).toISO(),
                    reviewer: {
                        displayName: 'John Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                },
                {
                    name: 'Review 2',
                    starRating: GmbStarRating.FIVE,
                    reviewId: '2',
                    comment: 'Unhappy comment',
                    createTime: fifteenthFebruaryTwentyFourLuxon.minus({ days: 1 }).toISO(),
                    updateTime: fifteenthFebruaryTwentyFourLuxon.minus({ days: 1 }).toISO(),
                    reviewer: {
                        displayName: 'Janet Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                },
            ];

            await upsertReviewsForPlatformUseCase.execute(reviews, PlatformKey.GMB, restaurantId);

            const dbReviews = await reviewsRepository.find({ filter: {}, options: { lean: true } });
            const deletedReview = dbReviews.find((review) => review.text === 'This will disappear');
            expect(dbReviews).toHaveLength(3);
            expect(deletedReview?.platformPresenceStatus).toEqual(PlatformPresenceStatus.NOT_FOUND);
        });

        it('should leave the comments (replies) untouched if drafted in the db and still not on the platform', async () => {
            const restaurantId = newDbId();
            const platformId = newDbId();
            const randomPlatformKey = PlatformKey.GMB;
            const randomSocialId = 'randomId';
            const fifteenthFebruaryTwentyFourLuxon = DateTime.fromISO('2024-02-15');

            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants' | 'platforms'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).build()];
                        },
                    },

                    platforms: {
                        data() {
                            return [getDefaultPlatform().restaurantId(restaurantId).key(randomPlatformKey)._id(platformId).build()];
                        },
                    },

                    reviews: {
                        data() {
                            return [
                                getDefaultReview()
                                    .restaurantId(restaurantId)
                                    .platformId(platformId)
                                    .key(randomPlatformKey)
                                    .comments([{ text: 'Thanks bro !', posted: PostedStatus.PENDING, _id: newDbId() }])
                                    .socialId(randomSocialId)
                                    .platformPresenceStatus(PlatformPresenceStatus.FOUND)
                                    .socialUpdatedAt(fifteenthFebruaryTwentyFourLuxon.toJSDate())
                                    .text('Happy comment')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const upsertReviewsForPlatformUseCase = container.resolve(UpsertReviewsForPlatformUseCase);
            const reviewsRepository = container.resolve(ReviewsRepository);

            const reviews: GmbReview[] = [
                {
                    name: 'Review 1',
                    starRating: GmbStarRating.FIVE,
                    reviewId: randomSocialId,
                    comment: 'Happy comment',
                    createTime: fifteenthFebruaryTwentyFourLuxon.toISO(),
                    updateTime: fifteenthFebruaryTwentyFourLuxon.toISO(),
                    reviewer: {
                        displayName: 'John Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                    reviewReply: undefined, // still no reply on the platform
                },
            ];

            await upsertReviewsForPlatformUseCase.execute(reviews, PlatformKey.GMB, restaurantId);

            const dbReviews = await reviewsRepository.find({ filter: {}, options: { lean: true } });
            expect(dbReviews).toHaveLength(1);
            const [currentReview] = dbReviews;
            expect(currentReview.comments).toHaveLength(1);
        });

        it('should send a download mobile app email if first bad review for restaurant', async () => {
            const restaurantId = newDbId();
            const platformId = newDbId();
            const userId = newDbId();
            const randomPlatformKey = PlatformKey.GMB;
            const randomSocialId = 'randomId';
            const fifteenthFebruaryTwentyFourLuxon = DateTime.fromISO('2024-02-15');

            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants' | 'platforms' | 'users' | 'userRestaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).build()];
                        },
                    },

                    platforms: {
                        data() {
                            return [getDefaultPlatform().restaurantId(restaurantId).key(randomPlatformKey)._id(platformId).build()];
                        },
                    },

                    reviews: {
                        data() {
                            return [];
                        },
                    },
                    users: {
                        data() {
                            return [getDefaultUser()._id(userId).email('<EMAIL>').build()];
                        },
                    },
                    userRestaurants: {
                        data() {
                            return [getDefaultUserRestaurant().userId(userId).restaurantId(restaurantId).caslRole(CaslRole.ADMIN).build()];
                        },
                    },
                },
            });

            await testCase.build();

            class MailingUseCasesMock {
                async sendEmail(): Promise<void> {
                    console.log('sent !');
                    return;
                }
            }
            container.register(MailingUseCases, { useValue: new MailingUseCasesMock() as any });
            const mailingUseCases = container.resolve(MailingUseCases);
            jest.spyOn(mailingUseCases, 'sendEmail');
            const upsertReviewsForPlatformUseCase = container.resolve(UpsertReviewsForPlatformUseCase);

            const reviews: GmbReview[] = [
                {
                    name: 'Review 1',
                    starRating: GmbStarRating.ONE,
                    reviewId: randomSocialId,
                    comment: 'Happy comment',
                    createTime: fifteenthFebruaryTwentyFourLuxon.toISO(),
                    updateTime: fifteenthFebruaryTwentyFourLuxon.toISO(),
                    reviewer: {
                        displayName: 'John Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                    reviewReply: undefined, // still no reply on the platform
                },
            ];

            await upsertReviewsForPlatformUseCase.execute(reviews, PlatformKey.GMB, restaurantId);

            expect(mailingUseCases.sendEmail).toHaveBeenCalled();
        });

        it('should start semantic analysis for the review', async () => {
            const isFeatureAvailableForRestaurantSpy = jest.spyOn(experimentationModule, 'isFeatureAvailableForRestaurant');
            isFeatureAvailableForRestaurantSpy.mockResolvedValue(true);

            const startSemanticAnalysisServiceSpy = jest.spyOn(startSemanticAnalysisService, 'execute');

            const upsertReviewsForPlatformUseCase = container.resolve(UpsertReviewsForPlatformUseCase);

            const restaurantId = newDbId();
            const randomPlatformKey = PlatformKey.GMB;
            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants' | 'platforms' | 'organizations'>({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().reviewPublicBusinessIdCount(0).build()];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [getDefaultRestaurant()._id(restaurantId).organizationId(dependencies.organizations()[0]._id).build()];
                        },
                    },
                    platforms: {
                        data() {
                            return [getDefaultPlatform().restaurantId(restaurantId).key(randomPlatformKey).build()];
                        },
                    },
                    reviews: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult: undefined,
            });

            await testCase.build();

            const reviews: GmbReview[] = [
                {
                    name: 'Review 1',
                    starRating: GmbStarRating.FIVE,
                    reviewId: '1',
                    comment: 'Happy comment',
                    reviewReply: {
                        comment: 'Happy reply',
                        updateTime: new Date().toISOString(),
                    },
                    createTime: new Date().toISOString(),
                    updateTime: new Date().toISOString(),
                    reviewer: {
                        displayName: 'John Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                },
                {
                    name: 'Review 2',
                    starRating: GmbStarRating.FIVE,
                    reviewId: '2',
                    comment: 'Unhappy comment',
                    reviewReply: {
                        comment: 'Unhappy reply',
                        updateTime: new Date().toISOString(),
                    },
                    createTime: new Date().toISOString(),
                    updateTime: new Date().toISOString(),
                    reviewer: {
                        displayName: 'Janet Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                },
            ];
            await upsertReviewsForPlatformUseCase.execute(reviews, PlatformKey.GMB, restaurantId);

            expect(startSemanticAnalysisServiceSpy).toHaveBeenCalledTimes(2);
        });

        it('should add missing keyword analysis for all comments of the review', async () => {
            const upsertReviewsForPlatformUseCase = container.resolve(UpsertReviewsForPlatformUseCase);
            const reviewsRepository = container.resolve(ReviewsRepository);
            const restaurantId = newDbId();
            const randomPlatformKey = PlatformKey.GMB;
            const gmbReviews: GmbReview[] = [
                {
                    name: 'Review 1',
                    starRating: GmbStarRating.FIVE,
                    reviewId: '1',
                    comment: 'Happy comment',
                    reviewReply: {
                        comment: 'Happy reply',
                        updateTime: new Date().toISOString(),
                    },
                    createTime: new Date().toISOString(),
                    updateTime: new Date().toISOString(),
                    reviewer: {
                        displayName: 'John Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                },
                {
                    name: 'Review 2',
                    starRating: GmbStarRating.FIVE,
                    reviewId: '2',
                    comment: 'Unhappy comment',
                    reviewReply: {
                        comment: 'Unhappy reply',
                        updateTime: new Date().toISOString(),
                    },
                    createTime: new Date().toISOString(),
                    updateTime: new Date().toISOString(),
                    reviewer: {
                        displayName: 'Janet Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                },
            ];

            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants' | 'platforms' | 'organizations'>({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().reviewPublicBusinessIdCount(0).build()];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [getDefaultRestaurant()._id(restaurantId).organizationId(dependencies.organizations()[0]._id).build()];
                        },
                    },
                    platforms: {
                        data() {
                            return [getDefaultPlatform().restaurantId(restaurantId).key(randomPlatformKey).build()];
                        },
                    },
                    reviews: {
                        data() {
                            return [];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const platformId = seededObjects.platforms[0]._id as DbId;

            await upsertReviewsForPlatformUseCase.execute(gmbReviews, PlatformKey.GMB, restaurantId);

            const reviews = await reviewsRepository.find({ filter: { platformId } });
            expect(reviews).toHaveLength(2);

            const keywordAnalysisFirstReview = reviews[0]?.comments[0]?.keywordAnalysis;
            const keywordAnalysisSecondReview = reviews[1]?.comments[0]?.keywordAnalysis;
            expect(keywordAnalysisFirstReview).toMatchObject(defaultKeywordAnalysis);
            expect(keywordAnalysisSecondReview).toMatchObject(defaultKeywordAnalysis);
        });

        it('should not add missing keyword analysis for all comments from more than one year ago', async () => {
            const upsertReviewsForPlatformUseCase = container.resolve(UpsertReviewsForPlatformUseCase);
            const generateKeywordAnalysisForCommentServiceSpy = jest.spyOn(generateKeywordAnalysisForCommentService, 'execute');
            const restaurantId = newDbId();
            const randomPlatformKey = PlatformKey.GMB;
            const gmbReviews: GmbReview[] = [
                {
                    name: 'Review 1',
                    starRating: GmbStarRating.FIVE,
                    reviewId: '1',
                    comment: 'Happy comment',
                    reviewReply: {
                        comment: 'Happy reply',
                        updateTime: new Date().toISOString(),
                    },
                    createTime: DateTime.now().minus({ years: 1, days: 1 }).toISO(),
                    updateTime: new Date().toISOString(),
                    reviewer: {
                        displayName: 'John Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                },
                {
                    name: 'Review 2',
                    starRating: GmbStarRating.FIVE,
                    reviewId: '2',
                    comment: 'Unhappy comment',
                    reviewReply: {
                        comment: 'Unhappy reply',
                        updateTime: new Date().toISOString(),
                    },
                    createTime: DateTime.now().minus({ years: 1, days: 1 }).toISO(),
                    updateTime: new Date().toISOString(),
                    reviewer: {
                        displayName: 'Janet Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                },
            ];

            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants' | 'platforms'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).build()];
                        },
                    },
                    platforms: {
                        data() {
                            return [getDefaultPlatform().restaurantId(restaurantId).key(randomPlatformKey).build()];
                        },
                    },
                    reviews: {
                        data() {
                            return [];
                        },
                    },
                },
            });

            await testCase.build();

            await upsertReviewsForPlatformUseCase.execute(gmbReviews, PlatformKey.GMB, restaurantId);

            expect(generateKeywordAnalysisForCommentServiceSpy).not.toHaveBeenCalled();
        });

        it('should not add missing keyword analysis for all comments without text', async () => {
            const upsertReviewsForPlatformUseCase = container.resolve(UpsertReviewsForPlatformUseCase);
            const generateKeywordAnalysisForCommentServiceSpy = jest.spyOn(generateKeywordAnalysisForCommentService, 'execute');
            const restaurantId = newDbId();
            const randomPlatformKey = PlatformKey.GMB;
            const gmbReviews: GmbReview[] = [
                {
                    name: 'Review 1',
                    starRating: GmbStarRating.FIVE,
                    reviewId: '1',
                    comment: 'Happy comment',
                    reviewReply: {
                        comment: '',
                        updateTime: new Date().toISOString(),
                    },
                    createTime: DateTime.now().toISO(),
                    updateTime: new Date().toISOString(),
                    reviewer: {
                        displayName: 'John Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                },
                {
                    name: 'Review 2',
                    starRating: GmbStarRating.FIVE,
                    reviewId: '2',
                    comment: 'Unhappy comment',
                    reviewReply: {
                        comment: '',
                        updateTime: new Date().toISOString(),
                    },
                    createTime: DateTime.now().toISO(),
                    updateTime: new Date().toISOString(),
                    reviewer: {
                        displayName: 'Janet Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                },
            ];

            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants' | 'platforms'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).build()];
                        },
                    },
                    platforms: {
                        data() {
                            return [getDefaultPlatform().restaurantId(restaurantId).key(randomPlatformKey).build()];
                        },
                    },
                    reviews: {
                        data() {
                            return [];
                        },
                    },
                },
            });

            await testCase.build();

            await upsertReviewsForPlatformUseCase.execute(gmbReviews, PlatformKey.GMB, restaurantId);

            expect(generateKeywordAnalysisForCommentServiceSpy).not.toHaveBeenCalled();
        });

        it('should not add missing keyword analysis for all comments with existing saved keywords', async () => {
            const upsertReviewsForPlatformUseCase = container.resolve(UpsertReviewsForPlatformUseCase);
            const reviewsRepository = container.resolve(ReviewsRepository);
            const generateKeywordAnalysisForCommentServiceSpy = jest.spyOn(generateKeywordAnalysisForCommentService, 'execute');

            const restaurantId = newDbId();
            const randomPlatformKey = PlatformKey.GMB;
            const gmbReviews: GmbReview[] = [
                {
                    name: 'Review 1',
                    starRating: GmbStarRating.FIVE,
                    reviewId: '1',
                    comment: 'Happy comment',
                    reviewReply: {
                        comment: 'Happy reply',
                        updateTime: new Date().toISOString(),
                    },
                    createTime: DateTime.now().toISO(),
                    updateTime: new Date().toISOString(),
                    reviewer: {
                        displayName: 'John Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                },
                {
                    name: 'Review 2',
                    starRating: GmbStarRating.FIVE,
                    reviewId: '2',
                    comment: 'Unhappy comment',
                    reviewReply: {
                        comment: 'Unhappy reply',
                        updateTime: new Date().toISOString(),
                    },
                    createTime: DateTime.now().toISO(),
                    updateTime: new Date().toISOString(),
                    reviewer: {
                        displayName: 'John Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                },
            ];

            const existingKeywordAnalysis = {
                keywords: ['happy'],
                score: 3.5,
                count: 1,
            };

            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants' | 'platforms'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).build()];
                        },
                    },
                    platforms: {
                        data() {
                            return [getDefaultPlatform().restaurantId(restaurantId).key(randomPlatformKey).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId('1')
                                    .platformId(dependencies.platforms()[0]._id)
                                    .comments([
                                        {
                                            _id: newDbId(),
                                            posted: PostedStatus.POSTED,
                                            text: 'Happy reply',
                                            keywordAnalysis: existingKeywordAnalysis,
                                        },
                                    ])
                                    .build(),
                                getDefaultReview()
                                    .socialId('2')
                                    .platformId(dependencies.platforms()[0]._id)
                                    .comments([
                                        {
                                            _id: newDbId(),
                                            posted: PostedStatus.POSTED,
                                            text: 'Unhappy reply',
                                            keywordAnalysis: existingKeywordAnalysis,
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const platformId = seededObjects.platforms[0]._id as DbId;

            await upsertReviewsForPlatformUseCase.execute(gmbReviews, PlatformKey.GMB, restaurantId);

            expect(generateKeywordAnalysisForCommentServiceSpy).not.toHaveBeenCalled();

            const reviews = await reviewsRepository.find({ filter: { platformId } });
            expect(reviews).toHaveLength(2);

            const keywordAnalysisFirstReview = reviews[0]?.comments[0]?.keywordAnalysis;
            const keywordAnalysisSecondReview = reviews[1]?.comments[0]?.keywordAnalysis;
            expect(keywordAnalysisFirstReview).toMatchObject(existingKeywordAnalysis);
            expect(keywordAnalysisSecondReview).toMatchObject(existingKeywordAnalysis);
        });

        it('should not add missing keyword analysis for more than 2000 reviews', async () => {
            const upsertReviewsForPlatformUseCase = container.resolve(UpsertReviewsForPlatformUseCase);
            upsertReviewsForPlatformUseCase._MAX_KEYWORD_ANALYSIS_COUNT = 10;
            const reviewsRepository = container.resolve(ReviewsRepository);
            const generateKeywordAnalysisForCommentServiceSpy = jest.spyOn(generateKeywordAnalysisForCommentService, 'execute');

            const restaurantId = newDbId();
            const randomPlatformKey = PlatformKey.GMB;
            const gmbReviews: GmbReview[] = Array.from({ length: upsertReviewsForPlatformUseCase._MAX_KEYWORD_ANALYSIS_COUNT + 1 }).map(
                (_, i) => ({
                    name: 'Review 1',
                    starRating: GmbStarRating.FIVE,
                    reviewId: i.toString(),
                    comment: 'Happy comment',
                    reviewReply: {
                        comment: 'Happy reply',
                        updateTime: new Date().toISOString(),
                    },
                    createTime: new Date().toISOString(),
                    updateTime: new Date().toISOString(),
                    reviewer: {
                        displayName: 'John Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                })
            );

            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants' | 'platforms' | 'organizations'>({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().reviewPublicBusinessIdCount(0).build()];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [getDefaultRestaurant()._id(restaurantId).organizationId(dependencies.organizations()[0]._id).build()];
                        },
                    },
                    platforms: {
                        data() {
                            return [getDefaultPlatform().restaurantId(restaurantId).key(randomPlatformKey).build()];
                        },
                    },
                    reviews: {
                        data() {
                            return [];
                        },
                    },
                },
            });

            await testCase.build();

            await upsertReviewsForPlatformUseCase.execute(gmbReviews, PlatformKey.GMB, restaurantId);

            const reviews = await reviewsRepository.find({ filter: {} });
            expect(reviews).toHaveLength(upsertReviewsForPlatformUseCase._MAX_KEYWORD_ANALYSIS_COUNT + 1);

            expect(generateKeywordAnalysisForCommentServiceSpy).toHaveBeenCalledTimes(
                upsertReviewsForPlatformUseCase._MAX_KEYWORD_ANALYSIS_COUNT
            );
        });

        it('should keep the malou comment props (like isMalou, aiInteractionIdUsed...) unchanged if the review is not new', async () => {
            const restaurantId = newDbId();
            const platformId = newDbId();
            const aiInteractionId = newDbId();
            const randomPlatformKey = PlatformKey.GMB;
            const randomSocialId = 'randomId';
            const fifteenthFebruaryTwentyFourLuxon = DateTime.fromISO('2024-02-15');
            const commentText = 'thanks bro !';
            const commentUpdateTime = fifteenthFebruaryTwentyFourLuxon.plus({ hour: 1 });
            const reviewLang = 'whatever';

            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants' | 'platforms'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).build()];
                        },
                    },

                    platforms: {
                        data() {
                            return [getDefaultPlatform().restaurantId(restaurantId).key(randomPlatformKey)._id(platformId).build()];
                        },
                    },

                    reviews: {
                        data() {
                            return [
                                getDefaultReview()
                                    .restaurantId(restaurantId)
                                    .platformId(platformId)
                                    .key(randomPlatformKey)
                                    .lang(reviewLang)
                                    .comments([
                                        {
                                            _id: newDbId(),
                                            socialUpdatedAt: commentUpdateTime.toJSDate(),
                                            socialId: undefined, // Google does not have socialId for comments
                                            text: commentText,
                                            posted: PostedStatus.POSTED,
                                            templateIdUsed: null,
                                            isRepliedFromAggregatedView: false,
                                            aiInteractionIdUsed: aiInteractionId,
                                            isMalou: true,
                                            author: {
                                                _id: newDbId(),
                                                name: 'John Doe',
                                                picture: null,
                                            },
                                            user: undefined,
                                            socialTranslatedText: null,
                                        },
                                    ])
                                    .socialId(randomSocialId)
                                    .platformPresenceStatus(PlatformPresenceStatus.FOUND)
                                    .socialUpdatedAt(fifteenthFebruaryTwentyFourLuxon.toJSDate())
                                    .text('Happy comment')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    const seededReview = dependencies.reviews[0];
                    const seededComment = seededReview.comments[0];
                    return omit(seededComment, ['id', 'author.id', '_id']); // new _id is created every time and we don't care about it
                },
            });

            await testCase.build();
            const upsertReviewsForPlatformUseCase = container.resolve(UpsertReviewsForPlatformUseCase);
            const reviewsRepository = container.resolve(ReviewsRepository);

            const expectedResult = testCase.getExpectedResult() as IReview['comments'][0];

            const reviews: GmbReview[] = [
                {
                    name: 'Review 1',
                    starRating: GmbStarRating.FIVE,
                    reviewId: randomSocialId,
                    comment: 'Happy comment',
                    createTime: fifteenthFebruaryTwentyFourLuxon.toISO(),
                    updateTime: fifteenthFebruaryTwentyFourLuxon.toISO(),
                    reviewer: {
                        displayName: 'John Doe',
                        profilePhotoUrl: 'https://example.com',
                        isAnonymous: false,
                    },
                    reviewReply: {
                        comment: commentText,
                        updateTime: commentUpdateTime.toJSDate().toISOString(),
                    },
                },
            ];
            await upsertReviewsForPlatformUseCase.execute(reviews, PlatformKey.GMB, restaurantId);
            const dbReview = await reviewsRepository.findOne({
                filter: { socialId: randomSocialId },
                options: { lean: true },
            });
            const [dbComment] = dbReview?.comments ?? [];

            expect(omit(dbComment, ['_id'])).toEqual(expectedResult);
        });

        it('should detect intelligent subjects and upsert reviews with intelligent subjects', async () => {
            const restaurantId = newDbId();
            const platformId = newDbId();
            const randomPlatformKey = PlatformKey.GMB;
            const reviewAvailableDate = DateTime.now().minus({ days: 5 });
            const oldestActiveAutomationDate = DateTime.now().minus({ days: 10 }).toJSDate();
            const firstReview = { socialId: 'social_id_1', text: 'the bathroom is dirty' };
            const secondReview = { socialId: 'social_id_2', text: 'the bathroom is soo dirty' };

            const testCase = new TestCaseBuilderV2<'users' | 'reviews' | 'restaurants' | 'intelligentSubjectAutomations' | 'platforms'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).build()];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(restaurantId)
                                    .userId(dependencies.users()[0]._id)
                                    .subject(IntelligentSubjectName.HYGIENE)
                                    .active(true)
                                    .lastActiveUpdatedAt(oldestActiveAutomationDate)
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data() {
                            return [getDefaultPlatform().restaurantId(restaurantId).key(randomPlatformKey)._id(platformId).build()];
                        },
                    },
                    reviews: {
                        data() {
                            return [
                                getDefaultReview()
                                    .restaurantId(restaurantId)
                                    .platformId(platformId)
                                    .key(randomPlatformKey)
                                    .socialId(firstReview.socialId)
                                    .intelligentSubjects(undefined)
                                    .text(firstReview.text)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const upsertReviewsForPlatformUseCase = container.resolve(UpsertReviewsForPlatformUseCase);
            const reviewsRepository = container.resolve(ReviewsRepository);

            const reviews: GmbReview[] = [
                {
                    name: 'Review 1',
                    starRating: GmbStarRating.TWO,
                    reviewId: firstReview.socialId,
                    comment: firstReview.text,
                    createTime: reviewAvailableDate.toISO(),
                    updateTime: reviewAvailableDate.toISO(),
                    reviewer: { displayName: 'John Doe', profilePhotoUrl: 'https://example.com', isAnonymous: false },
                },
                {
                    name: 'Review 2',
                    starRating: GmbStarRating.TWO,
                    reviewId: secondReview.socialId,
                    comment: secondReview.text,
                    createTime: reviewAvailableDate.toISO(),
                    updateTime: reviewAvailableDate.toISO(),
                    reviewer: { displayName: 'John Doe', profilePhotoUrl: 'https://example.com', isAnonymous: false },
                },
            ];

            // before upserting, the reviews should not have the intelligent subjects
            const dbReviewsBeforeUpsert = await reviewsRepository.find({
                filter: { restaurantId, socialId: { $in: [firstReview.socialId, secondReview.socialId] } },
                options: { lean: true },
            });

            expect(dbReviewsBeforeUpsert[0].intelligentSubjects).toEqual([]);
            // expect(dbReviewsBeforeUpsert[1]?.intelligentSubjects).toBeNull(); // does not exist yet in the database (not seeded)

            // after upserting, the reviews should have the intelligent subjects
            await upsertReviewsForPlatformUseCase.execute(reviews, PlatformKey.GMB, restaurantId);

            const dbReviews = await reviewsRepository.find({
                filter: { restaurantId, socialId: { $in: [firstReview.socialId, secondReview.socialId] } },
                options: { lean: true },
            });

            const dbIntelligentSubjects = defaultAiResponse.map((res) => ({
                subject: res.intelligentSubject,
                isDetected: res.isDetected,
                sentiment: res.sentiment,
            }));

            expect(dbReviews[0].intelligentSubjects).toEqual(dbIntelligentSubjects);
            expect(dbReviews[1].intelligentSubjects).toEqual(dbIntelligentSubjects);
        });
    });
});
