import { Builder } from 'builder-pattern';

import { IReview, newDbId } from '@malou-io/package-models';
import { ApplicationLanguage, PlatformKey, PlatformPresenceStatus, PostedStatus } from '@malou-io/package-utils';

type ReviewPayload = Omit<IReview, 'archived'>;

const _buildReview = (review: ReviewPayload) => Builder<ReviewPayload>(review);

export const getDefaultReview = () =>
    _buildReview({
        _id: newDbId(),
        key: PlatformKey.GMB,
        text: 'Review text',
        comments: [getDefaultComment().build()],
        platformId: newDbId(),
        restaurantId: newDbId(),
        socialId: 'Random string id',
        createdAt: new Date(),
        socialCreatedAt: new Date(),
        socialSortDate: new Date(),
        updatedAt: new Date(),
        lang: ApplicationLanguage.FR,
        platformPresenceStatus: PlatformPresenceStatus.FOUND,
        publicBusinessId: newDbId().toString(),
    });

export const getDefaultComment = () =>
    Builder<IReview['comments'][number]>({
        _id: newDbId(),
        socialUpdatedAt: new Date(),
        socialId: 'Random string id',
        text: 'Comment text',
        posted: PostedStatus.POSTED,
        templateIdUsed: null,
        isRepliedFromAggregatedView: false,
        aiInteractionIdUsed: null,
        isMalou: true,
        author: {
            _id: newDbId(),
            name: 'Author name',
            picture: null,
        },
        user: undefined,
        socialTranslatedText: null,
    });
