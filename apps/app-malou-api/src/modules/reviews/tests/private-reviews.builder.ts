import { Builder } from 'builder-pattern';

import { IPrivateReview, newDbId } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

type PrivateReviewPayload = Omit<IPrivateReview, 'archived'>;

const _buildPrivateReview = (review: PrivateReviewPayload) => Builder<PrivateReviewPayload>(review);

export const getDefaultPrivateReview = () =>
    _buildPrivateReview({
        _id: newDbId(),
        key: PlatformKey.GMB,
        comments: [
            {
                _id: newDbId(),
                text: 'Private review text',
                templateIdUsed: newDbId(),
                socialUpdatedAt: new Date(),
                author: {
                    _id: newDbId(),
                    name: 'Author name',
                },
                content: {
                    _id: newDbId(),
                    object: 'Content object',
                    messageHTML: 'Content message HTML',
                    from: {
                        name: 'From name',
                        email: 'From email',
                    },
                },
                isRepliedFromAggregatedView: false,
            },
        ],
        restaurantId: newDbId(),
        createdAt: new Date(),
        socialCreatedAt: new Date(),
        socialSortDate: new Date(),
        updatedAt: new Date(),
        text: 'Private review text',
        rating: 5,
        publicBusinessId: newDbId().toString(),
    });
