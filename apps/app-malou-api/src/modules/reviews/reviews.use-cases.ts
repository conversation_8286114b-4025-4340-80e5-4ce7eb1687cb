import SQS from 'aws-sdk/clients/sqs';
import { groupBy, partition } from 'lodash';
import { DateTime } from 'luxon';
import { FilterQuery } from 'mongoose';
import assert from 'node:assert/strict';
import { autoInjectable, delay, inject } from 'tsyringe';

import {
    DbId,
    ID,
    IPlatform,
    IPrivateReview,
    IRestaurant,
    IReview,
    IReviewWithSemanticAnalysis,
    IUser,
    PopulateBuilderHelper,
    toDbId,
} from '@malou-io/package-models';
import {
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    DEFAULT_LANG_UNKNOWN,
    EmailCategory,
    EmailType,
    errorReplacer,
    FilterType,
    getPlatformDefinition,
    getPlatformKeysWithReview,
    MalouErrorCode,
    PlatformDataFetchedStatus,
    PlatformKey,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { AdvancedReviewFilters, ReviewFiltersMode } from ':helpers/filters/advanced-review-filters';
import { ReviewFilters } from ':helpers/filters/review-filters';
import { logger } from ':helpers/logger';
import { DEFAULT_REVIEWER_NAME_VALIDATION } from ':microservices/ai-previous-review-analysis.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import ClientsRepository from ':modules/clients/clients.repository';
import CredentialsRepository from ':modules/credentials/credentials.repository';
import MailingUseCases from ':modules/mailing/use-cases';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PrivateReviewsRepository } from ':modules/private-reviews/private-reviews.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { send as sqsReviewsSend } from ':modules/reviews/queues/update-reviews/update-reviews.producer';
import { RestaurantReviewsRatingStats, ReviewFiltersInput } from ':modules/reviews/reviews.interfaces';
import { ReviewMapper, ReviewMapperFactory } from ':modules/reviews/reviews.mapper';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { GenerateKeywordAnalysisForCommentService } from ':modules/reviews/services/generate-keyword-analysis-for-comment.service';
import { GetPlatformReviewsService } from ':modules/reviews/services/get-review-platform-use-cases';
import { UpsertReviewsForPlatformUseCase } from ':modules/reviews/use-cases/upsert-reviews-for-platform/upsert-reviews-for-platform.use-case';
import { StartReviewSemanticAnalysisService } from ':modules/segment-analyses/services/start-review-semantic-analysis.service';
import { isFeatureAvailableForRestaurant } from ':services/experimentations-service/experimentation.service';
import { GenerateLanguageDetectionService } from ':services/text-translator/generate-language-detection.service';

@autoInjectable()
export default class ReviewsUseCases {
    constructor(
        @inject(delay(() => ReviewsRepository))
        private readonly _reviewsRepository: ReviewsRepository,
        @inject(delay(() => PrivateReviewsRepository))
        private readonly _privateReviewsRepository: PrivateReviewsRepository,
        @inject(delay(() => RestaurantsRepository))
        private readonly _restaurantsRepository: RestaurantsRepository,
        @inject(delay(() => CredentialsRepository))
        private readonly _credentialsRepository: CredentialsRepository,
        @inject(delay(() => PlatformsRepository))
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _clientsRepository: ClientsRepository,
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        @inject(delay(() => MailingUseCases))
        private readonly _mailingUseCases: MailingUseCases,
        private readonly _getPlatformReviewsService: GetPlatformReviewsService,
        private readonly _startReviewSemanticAnalysisService: StartReviewSemanticAnalysisService,
        private readonly _generateKeywordAnalysisForCommentService: GenerateKeywordAnalysisForCommentService,
        private readonly _upsertReviewsForPlatformUseCase: UpsertReviewsForPlatformUseCase,
        private readonly _generateLanguageDetectionService: GenerateLanguageDetectionService
    ) {}

    async updateReviewArchivedValue(reviewId: ID, archived: boolean, review: IReview | IPrivateReview): Promise<IReview | IPrivateReview> {
        if (this._isPrivateReview(review)) {
            return this._privateReviewsRepository.findOneAndUpdateOrFail({
                filter: { _id: reviewId },
                update: { archived },
                options: { lean: true },
            });
        }
        return this._reviewsRepository.findOneAndUpdateOrFail({
            filter: { _id: reviewId },
            update: { archived },
            options: { lean: true },
        });
    }

    async updateComment({
        user,
        reviewId,
        comment,
        commentId,
    }: {
        user: PopulateBuilderHelper<IUser, [{ path: 'profilePicture' }]>;
        reviewId: string;
        comment: any;
        commentId: string;
    }): Promise<IReview> {
        const review = await this._reviewsRepository.getReviewById(reviewId);
        assert(review, 'Review not found');
        const platformReviewMapper = ReviewMapperFactory.getPlatformReviewMapper(review.key);
        const platformReply = this._getPlatformReviewsService.execute(review.key).updateComment({
            review,
            comment: platformReviewMapper.mapToPlatformReply(comment),
        });
        const mappedComment = ReviewMapper.mapToMalouReply(review.key, platformReply, user);
        mappedComment.isMalou = true;
        mappedComment.templateIdUsed = comment.templateIdUsed;
        mappedComment.isRepliedFromAggregatedView = comment.isRepliedFromAggregatedView;
        const aiInteractionTypesForReviewReply = [
            AiInteractionType.ANSWER_REVIEW_ADVANCED_SETTINGS,
            AiInteractionType.OPTIMIZE_REVIEW_ANSWER_ADVANCED_SETTINGS,
            AiInteractionType.REVIEW_ANSWER_TRANSLATION,
        ];
        const associatedAiInteraction = await this._aiInteractionsRepository.findOne({
            filter: { relatedEntityId: review._id, type: { $in: aiInteractionTypesForReviewReply }, options: { sort: { createdAt: -1 } } },
        });
        if (associatedAiInteraction) {
            mappedComment.aiInteractionIdUsed = associatedAiInteraction._id;
        }
        return this._reviewsRepository.updateReviewComment({
            reviewId,
            commentId,
            comment: mappedComment,
        });
    }

    async startUpdateReviewsForRestaurant(restaurantId: string): Promise<void> {
        const availablePlatforms = await this.getAvailableReviewsPlatforms(restaurantId);
        await this._restaurantsRepository.startUpdateReviews(
            restaurantId,
            availablePlatforms.map((p) => p.key)
        );
        for (const platform of availablePlatforms) {
            await sqsReviewsSend(platform._id, false);
        }
    }

    async getAvailableReviewsPlatforms(restaurantId: string): Promise<Platform[]> {
        const platforms = await this._platformsRepository.getPlatformsByRestaurantId(restaurantId);
        const reviewsPlatforms = getPlatformKeysWithReview();
        return platforms.filter((platform) => reviewsPlatforms.includes(platform.key));
    }

    async updateReviewsForPlatform(platformId: string, recentOnly: boolean) {
        const platform = await this._platformsRepository.getPlatformById(platformId);
        if (!platform) {
            logger.warn('[UPDATE_PLATFORM_REVIEWS] - Platform not found', { platformId });
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, { message: 'Platform not found' });
        }
        const restaurant = await this._restaurantsRepository.getRestaurantById(platform.restaurantId.toString());
        assert(restaurant, 'Restaurant not found');
        const fetched = restaurant?.currentState?.reviews?.fetched?.[platform.key];
        const twoHoursAgo = DateTime.now().minus({ hours: 2 }).toJSDate();
        if (fetched?.status === PlatformDataFetchedStatus.SUCCESS && new Date(fetched?.lastTried) > twoHoursAgo) {
            logger.info('[UPDATE_PLATFORM_REVIEWS] - Skipping job', { platformId });
            return;
        }
        return this._getPlatformReviews({
            platform,
            restaurant,
            recentOnly,
            shouldForceUpdate: false,
        });
    }

    getSortingFieldByPlatformKey(platformKey: string): string {
        switch (platformKey) {
            case PlatformKey.GMB:
                return 'socialUpdatedAt';
            default:
                return 'socialCreatedAt';
        }
    }

    /**
     * Used for the jobs to pull and upsert all reviews from the db
     * Get all users -> get all their restaurants -> get all platforms linked -> get all reviews & upsert
     */
    async updateAllReviews({ recentOnly = true, activeOnly = true }): Promise<(SQS.SendMessageBatchResultEntryList | undefined)[]> {
        const platforms = await this._platformsRepository.find({
            filter: {},
            projection: { _id: 1, restaurantId: 1, key: 1 },
            options: { lean: true, populate: [{ path: 'restaurant', select: '_id active' }] },
        });
        const reviewsPlatformsKeys = getPlatformKeysWithReview();
        const filteredPlatforms = this.getPlatformsToFetch(platforms, reviewsPlatformsKeys, activeOnly);
        const restaurantsReviewsPlatforms = this.getRestaurantPlatforms(filteredPlatforms);
        for (const restaurantId of Object.keys(restaurantsReviewsPlatforms)) {
            const { platformKeys } = restaurantsReviewsPlatforms[restaurantId];
            try {
                await this._restaurantsRepository.startUpdateReviews(restaurantId, platformKeys || []);
            } catch (error) {
                logger.error('[UPDATE_ALL_REVIEWS] - Error updating restaurant', { restaurantId, error });
            }
        }
        const promises: Promise<SQS.SendMessageBatchResultEntryList | undefined>[] = [];
        const progressivelyFetchedPlatformKeys = [PlatformKey.LAFOURCHETTE]; // Will be fetched through a recurring job to avoid rate limiting
        const immediatelyFetchedPlatforms = filteredPlatforms.filter(
            (platform) => !progressivelyFetchedPlatformKeys.includes(platform.key)
        );
        for (const platform of immediatelyFetchedPlatforms) {
            const { _id: platformId } = platform;
            promises.push(sqsReviewsSend(platformId, recentOnly));
        }
        return Promise.all(promises);
    }

    getPlatformsToFetch(
        platforms: PopulateBuilderHelper<IPlatform, [{ path: 'restaurant' }], { _id: 1; restaurantId: 1; key: 1 }>[],
        reviewsPlatformsKeys: string[],
        activeOnly = true
    ) {
        return platforms
            .filter((platform) => reviewsPlatformsKeys.includes(platform.key))
            .filter((platform) => (activeOnly ? !!platform.restaurant?.active : true));
    }

    getRestaurantPlatforms(
        platforms: PopulateBuilderHelper<IPlatform, [{ path: 'restaurant' }], { _id: 1; restaurantId: 1; key: 1 }>[]
    ): Record<
        string,
        {
            restaurant: IRestaurant;
            platformKeys: PlatformKey[];
        }
    > {
        const restaurantsReviewsPlatforms: Record<
            string,
            {
                restaurant: IRestaurant;
                platformKeys: PlatformKey[];
            }
        > = {};
        for (const { restaurantId, key, restaurant } of platforms) {
            const restaurantIdStr = restaurantId?.toString();
            if (!restaurantIdStr) {
                continue;
            }
            if (!restaurantsReviewsPlatforms[restaurantIdStr]) {
                restaurantsReviewsPlatforms[restaurantIdStr] = {
                    platformKeys: [],
                    restaurant,
                };
            }
            if (!restaurantsReviewsPlatforms[restaurantIdStr].platformKeys?.includes(key)) {
                restaurantsReviewsPlatforms[restaurantIdStr].platformKeys.push(key);
            }
        }
        return restaurantsReviewsPlatforms;
    }

    async getRestaurantsReviews(filters: ReviewFilters): Promise<{
        results: { restaurant: { name: string; internalName?: string } | null; total: number }[];
        startDate: string;
        endDate: string;
    }> {
        const data: {
            results: { restaurant: { name: string; internalName?: string } | null; total: number }[];
            startDate: string;
            endDate: string;
        } = {
            results: [],
            startDate: filters.startDate!.toISOString(),
            endDate: filters.endDate!.toISOString(),
        };

        if (filters.periodCantBeSet()) {
            return data;
        }

        const reviews = await this._reviewsRepository.find({
            filter: filters.buildQuery({ filterType: FilterType.REVIEWS }),
            options: { lean: true },
        });
        const groupedReviews = groupBy(reviews, 'restaurantId');

        // TODO: Make only 1 call to DB to fetch all restaurants, then map review counts
        data.results = await Promise.all(
            Object.keys(groupedReviews).map(async (restaurantId) => {
                const restaurant = await this._restaurantsRepository.findOne({
                    filter: { _id: restaurantId },
                    projection: { name: 1, internalName: 1 },
                    options: { lean: true },
                });

                return {
                    restaurant,
                    total: (groupedReviews[restaurantId] as Array<IReview>).length,
                };
            })
        );

        return data;
    }

    async getRestaurantsReviewsGroupedByRestaurantIds(filters: ReviewFilters): Promise<RestaurantReviewsRatingStats[]> {
        if (filters.periodCantBeSet()) {
            return [];
        }
        return this._reviewsRepository.getRestaurantsReviewsGroupedByRestaurantIds(filters);
    }

    /**
     * get restaurants reviews average rating
     * @param {ReviewFilters} filters
     */
    async getRestaurantsReviewsAverageRating(filters: ReviewFilters) {
        type MappedResult = {
            averageRating: number;
            averageRatingPerPlatform: { platformKey: PlatformKey; averageRating: number }[];
            restaurant: { _id: string; name: string; internalName?: string } | null;
        };
        const data: {
            results: MappedResult[];
            startDate: string;
            endDate: string;
        } = {
            results: [],
            startDate: filters.startDate!.toISOString(),
            endDate: filters.endDate!.toISOString(),
        };
        if (filters.periodCantBeSet()) {
            return data;
        }
        const results = await this._reviewsRepository.getRestaurantsReviewsAverageRating(filters);
        const mappedResult: MappedResult[] = [];
        for (const res of results) {
            const restaurant = await this._restaurantsRepository.findOne({
                filter: { _id: res.restaurantId },
                projection: { name: 1, internalName: 1, _id: 1 },
                options: { lean: true },
            });
            mappedResult.push({
                averageRating: res.averageRating,
                averageRatingPerPlatform: res.averageRatingPerPlatform,
                restaurant: restaurant ? { ...restaurant, _id: restaurant._id.toString() } : null,
            });
        }
        data.results = mappedResult;
        return data;
    }

    async getRestaurantReviewsRating(filters: ReviewFilters) {
        const data = {
            results: [],
            startDate: filters.startDate!.toISOString(),
            endDate: filters.endDate!.toISOString(),
        };
        if (filters.periodCantBeSet()) {
            return data;
        }
        data.results = await this._reviewsRepository.getRestaurantsReviewsRating(filters);
        return data;
    }

    async getRestaurantReviewsEvolution(filters: ReviewFilters): Promise<{
        results: {
            dataPerDay: { key: PlatformKey; day: number; year: number; total: number }[];
            dataPerWeek: { key: PlatformKey; week: number; year: number; total: number }[];
            dataPerMonth: { key: PlatformKey; month: number; year: number; total: number }[];
        };
        startDate: string;
        endDate: string;
    }> {
        const data = {
            results: {
                dataPerDay: [],
                dataPerWeek: [],
                dataPerMonth: [],
            },
            startDate: filters.startDate!.toISOString(),
            endDate: filters.endDate!.toISOString(),
        };
        if (filters.periodCantBeSet()) {
            return data;
        }
        const results = await this._reviewsRepository.getRestaurantReviewsEvolution(filters);
        data.results = results[0];
        return data;
    }

    async getPlatformTotalReviewCount(restaurantId: string, platformKey: PlatformKey): Promise<number> {
        return this._getPlatformReviewsService.execute(platformKey).fetchTotalReviewCount(restaurantId);
    }

    // ----------------------------------------------------------------------------------------------------------
    // Private Reviews
    // ----------------------------------------------------------------------------------------------------------

    async createPrivateReview(privateReview: Partial<IPrivateReview>): Promise<IPrivateReview> {
        const { clientId, restaurantId } = privateReview;
        assert(restaurantId, 'restaurantId is required');
        const review = await this._privateReviewsRepository.create({
            data: {
                ...privateReview,
                socialSortDate: privateReview.socialCreatedAt,
            },
        });
        if (clientId) {
            await this._clientsRepository.findOneAndUpdate({
                filter: {
                    _id: clientId,
                    restaurantId,
                },
                update: {
                    $addToSet: {
                        reviewsLeft: {
                            platformKey: 'privateNegativeReview',
                            hasLeftReview: true,
                        },
                    },
                },
            });
        }

        const isNewSemanticAnalysisFeatureEnabledForRestaurant = await isFeatureAvailableForRestaurant({
            restaurantId: restaurantId.toString(),
            featureName: 'release-new-semantic-analysis',
        });
        if (isNewSemanticAnalysisFeatureEnabledForRestaurant) {
            await this._startReviewSemanticAnalysisService.execute({ review, isPrivateReview: true });
        }
        return review;
    }

    async replyPrivate({ reviewId, comment }): Promise<IPrivateReview> {
        const review = await this._privateReviewsRepository.findOne({ filter: { _id: reviewId } });

        if (!review) {
            throw new MalouError(MalouErrorCode.REVIEW_NOT_FOUND, {
                metadata: { reviewId },
            });
        }

        if (review.clientId) {
            await this._mailingUseCases.sendEmail(EmailCategory.REVIEW_REPLY, EmailType.REVIEW_REPLY, {
                emailData: comment,
                clientId: review.clientId.toString(),
            });
        }

        const reviewReply = await this._privateReviewsRepository.findOneAndUpdateOrFail({
            filter: { _id: reviewId },
            update: {
                $push: {
                    comments: comment,
                },
            },
            options: { new: true },
        });

        return reviewReply;
    }

    async reviewsCount(restaurantId: ID, platformKey: string) {
        return this._reviewsRepository.countDocuments({
            filter: {
                restaurantId,
                key: platformKey,
            },
        });
    }

    async getAnsweredAndNotAnsweredReviews(filter: FilterQuery<IReview>) {
        const reviews = await this._reviewsRepository.find({
            filter,
            projection: { key: true, text: true, comments: true },
            options: { lean: true },
        });
        const reviewsThatCanBeAnswered = reviews.filter((review) => this._canReviewBeAnswered(review));
        const [notAnswered, answered] = partition(reviewsThatCanBeAnswered, (review) => !review.comments?.length);
        return { answered, notAnswered };
    }

    async getUnansweredReviewCount(filters: ReviewFilters): Promise<{ count: number }> {
        const reviews = await this._reviewsRepository.getUnansweredReviews(filters);
        const reviewsThatCanBeAnswered = reviews.filter((review) => this._canReviewBeAnswered(review));
        return { count: reviewsThatCanBeAnswered.length };
    }

    async handleAutoReply({
        reviewId,
        replyText,
        templateId,
        interactionId,
        replyLang,
    }: {
        reviewId: string;
        replyText: string;
        templateId?: ID;
        interactionId?: ID;
        replyLang?: string | null;
    }): Promise<IReview | undefined> {
        try {
            const review = await this._reviewsRepository.getReviewById(reviewId);
            if (!review || review?.comments?.length) {
                return;
            }
            const platformReviewsUseCases = this._getPlatformReviewsService.execute(review.key);
            const PlatformReviewMapper = ReviewMapperFactory.getPlatformReviewMapper(review.key);
            const platform = await this._platformsRepository.getPlatformById(review.platformId.toString());
            assert(platform, 'Platform not found');
            const credentialId = platform.credentials?.[0];
            const credential = credentialId
                ? ((await this._credentialsRepository.getCredentialById(credentialId)) ?? undefined)
                : undefined;
            const platformReply = await platformReviewsUseCases.reply({
                review,
                restaurantId: review.restaurantId.toString(),
                credential,
                platform,
                comment: PlatformReviewMapper.mapToPlatformReply(replyText, review, platform.socialId),
            });

            const mappedReply = ReviewMapper.mapToMalouReply(review.key, platformReply);
            mappedReply.isMalou = true;
            mappedReply.templateIdUsed = templateId ? toDbId(templateId) : null;
            mappedReply.aiInteractionIdUsed = interactionId ? toDbId(interactionId) : null;
            if (!replyLang) {
                replyLang = await this._generateLanguageDetectionService.execute({
                    relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
                    relatedEntityId: review._id.toString(),
                    restaurantId: review.restaurantId.toString(),
                    text: replyText,
                });
            }
            mappedReply.keywordAnalysis = await this._generateKeywordAnalysisForCommentService.execute({
                reviewId: review._id.toString(),
                reviewText: review.text ?? '',
                rating: review.rating ?? null,
                restaurantId: review.restaurantId,
                text: replyText,
                reviewLang: replyLang,
                reviewSocialCreatedAt: new Date(review.socialCreatedAt),
                reviewerName: review.reviewer?.displayName ?? '',
                reviewerNameValidation: review.reviewerNameValidation ?? DEFAULT_REVIEWER_NAME_VALIDATION,
            });

            await platformReviewsUseCases.pushReviewComment({
                socialId: review.socialId,
                key: review.key,
                comment: mappedReply,
            });
            logger.info('[ReviewUseCases] [handleAutoReply] Auto reply finished successfully', {
                reviewId,
                replyText,
                templateId,
                interactionId,
                mappedReply,
            });
            return this._reviewsRepository.findOneAndUpdateOrFail({
                filter: { _id: review._id },
                update: { wasAnsweredAutomatically: true },
                options: { lean: true },
            });
        } catch (error) {
            logger.error('[ReviewUseCases] [handleAutoReply] Error while auto replying', {
                reviewId,
                replyText,
                templateId,
                interactionId,
                error,
            });
            return;
        }
    }

    async getReviewsWithAnalysis(
        filters: FilterQuery<IReview>,
        isSemanticAnalysisFeatureEnabled: boolean
    ): Promise<Partial<IReviewWithSemanticAnalysis>[]> {
        filters.$and!.push({
            text: { $ne: null },
        });
        const results = await this._reviewsRepository.getReviewsWithAnalysis(filters, isSemanticAnalysisFeatureEnabled);
        return results;
    }

    detectLangAndUpdateReviews = (reviews: IReview[] | undefined, platformKey: PlatformKey): Promise<any> => {
        if (!getPlatformDefinition(platformKey)?.shouldDetectReviewsLang) {
            return Promise.resolve();
        }
        if (!reviews?.length) {
            return Promise.resolve();
        }
        const reviewsWithText = reviews.filter((review) => !!review.text?.length && (!review.lang || review.lang === 'undetermined'));
        const promises = reviewsWithText.map(async (review) => {
            const lang = await this._detectLangForReview(review);
            return this._reviewsRepository.findOneAndUpdate({
                filter: { socialId: review.socialId, platformId: review.platformId },
                update: { lang },
            });
        });

        return Promise.all(promises);
    };

    async getEstimatedReviewsCount(filters: ReviewFiltersInput): Promise<number> {
        const [reviewsCount, privateReviewsCount] = await Promise.all([
            this._reviewsRepository.getEstimatedPublicReviewCount(filters),
            filters.showPrivate
                ? this._privateReviewsRepository.countDocuments({
                      filter: new AdvancedReviewFilters(filters, ReviewFiltersMode.PRIVATE_REVIEWS).buildQuery(),
                  })
                : Promise.resolve(0),
        ]);
        return reviewsCount + privateReviewsCount;
    }

    async removeComment(reviewId: string, commentId: DbId): Promise<void> {
        await this._reviewsRepository.updateOne({
            filter: {
                _id: toDbId(reviewId),
            },
            update: {
                $pull: {
                    comments: {
                        _id: commentId,
                    },
                },
            },
        });
    }

    private _canReviewBeAnswered(review: PopulateBuilderHelper<IReview, [], { key: true; text: true; comments: true }>): boolean {
        switch (review.key) {
            case PlatformKey.LAFOURCHETTE:
                return !!review.text;
            case PlatformKey.DELIVEROO:
                return !!review.text;
            case PlatformKey.FOURSQUARE:
            case PlatformKey.RESY:
            case PlatformKey.SEVENROOMS:
                return false;
            default:
                return true;
        }
    }

    private _clarifyError(e: any): string {
        return e?.error_user_title || e?.error?.message || e?.message || e?.malouErrorCode || JSON.stringify(e, errorReplacer) || 'unknown';
    }

    private _isPrivateReview(review: IReview | IPrivateReview): review is IPrivateReview {
        return !!(review as IPrivateReview).campaignId || !!(review as IPrivateReview).scanId;
    }

    private _detectLangForReview = async (review: IReview): Promise<string> => {
        try {
            if (review.lang) {
                return review.lang;
            }
            const detectedLang = await this._generateLanguageDetectionService.execute({
                relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
                relatedEntityId: review._id.toString(),
                restaurantId: review.restaurantId.toString(),
                text: review.text ?? '',
            });
            return detectedLang;
        } catch (err) {
            return DEFAULT_LANG_UNKNOWN;
        }
    };

    private async _getPlatformReviews({
        platform,
        restaurant,
        recentOnly,
        shouldForceUpdate,
    }: {
        platform: Platform;
        restaurant: { _id: DbId };
        recentOnly: boolean;
        shouldForceUpdate: boolean;
    }) {
        try {
            const credential =
                (await this._credentialsRepository.findOne({
                    filter: { _id: platform.credentials?.[0] },
                    options: { lean: true },
                })) ?? undefined;
            const platformUseCases = this._getPlatformReviewsService.execute(platform.key);
            const reviews = await platformUseCases.getReviewsData(
                {
                    socialId: platform.socialId,
                    restaurantId: restaurant?._id?.toString(),
                    platformId: platform._id.toString(),
                    restaurantSocialId: platform.socialId,
                    credential,
                },
                recentOnly,
                shouldForceUpdate
            );
            logger.info('[UPDATE_PLATFORM_REVIEWS] - Found reviews', {
                platformId: platform._id,
                restaurantId: restaurant._id,
                platformKey: platform.key,
                reviewsCount: reviews?.length ?? 0,
            });

            return this._upsertReviewsForPlatformUseCase.execute(reviews, platform.key, restaurant._id);
        } catch (error) {
            logger.warn('[REVIEWS_UPDATE] - Error trying to fetch reviews', {
                platformId: platform._id,
                platformKey: platform.key,
                restaurantId: restaurant._id,
                error,
            });
            return this._restaurantsRepository.findOneAndUpdate({
                filter: { _id: restaurant._id },
                update: {
                    [`currentState.reviews.fetched.${platform.key}`]: {
                        status: PlatformDataFetchedStatus.ERROR,
                        lastTried: new Date(),
                        error: this._clarifyError(error),
                    },
                },
            });
        }
    }
}
