import assert from 'node:assert/strict';
import { container } from 'tsyringe';

import { IPlatform, IReview } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import { DeepPartial } from ':helpers/types/deep-partial';
import ZenchefCredentialsUseCases from ':modules/credentials/platforms/zenchef/zenchef.use-cases';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { ZenchefReplyPayload } from ':modules/reviews/platforms/zenchef/zenchef-review-mapper';
import { ReviewMapper } from ':modules/reviews/reviews.mapper';
import { reviewsFetchCounter, reviewsReplyCounter } from ':modules/reviews/reviews.metrics';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';

const reviewsRepository = container.resolve(ReviewsRepository);
const zenchefCredentialsUseCases = container.resolve(ZenchefCredentialsUseCases);
const platformsRepository = container.resolve(PlatformsRepository);
/**
 *
 * @param {Object} data
 * @param {string} data.restaurantId
 */
export const getReviewsData = async function ({ restaurantId }: { restaurantId: string }, recentOnly) {
    const platform = await platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.ZENCHEF);
    assert(platform, 'Missing platform');
    const { credentials, socialId } = platform;
    const credentialId = credentials?.[0];
    assert(credentialId, 'Missing credentialId');
    assert(socialId, 'Missing socialId');
    let res;
    try {
        res = await zenchefCredentialsUseCases.fetchAllReviews(credentialId, socialId, recentOnly);
    } catch (err) {
        reviewsFetchCounter.add(1, {
            source: PlatformKey.ZENCHEF,
            status: 'failure',
        });
        throw err;
    }
    reviewsFetchCounter.add(1, {
        source: PlatformKey.ZENCHEF,
        status: 'success',
    });
    return res.data;
};

export const mapReviewsDataToMalou = function (platform: IPlatform, reviewsData): DeepPartial<IReview>[] {
    const filteredReviews = reviewsData.map((review) => ReviewMapper.mapToMalouReview(platform, review)).filter((r) => r.socialId);
    return filteredReviews;
};

/**
 * Update Reviews in malou database with new review reply
 */

export const reply = async function ({
    review,
    comment,
    restaurantId,
}: {
    review: IReview;
    comment: ZenchefReplyPayload;
    restaurantId: string;
}): Promise<any> {
    try {
        const platform = await platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.ZENCHEF);
        assert(platform, 'Missing platform');
        const { credentials, socialId } = platform;
        const credentialId = credentials?.[0];
        assert(credentialId, 'Missing credentialId');
        assert(socialId, 'Missing socialId');
        const reviewReply = await zenchefCredentialsUseCases.replyToReview(credentials[0], socialId, review, comment);
        reviewsReplyCounter.add(1, {
            source: PlatformKey.ZENCHEF,
            status: 'success',
        });
        return reviewReply;
    } catch (error) {
        reviewsReplyCounter.add(1, {
            source: PlatformKey.ZENCHEF,
            status: 'failure',
        });
        throw error;
    }
};

export const pushReviewComment = ({ socialId, key, comment }) => reviewsRepository.pushReviewComment({ socialId, key, comment });
