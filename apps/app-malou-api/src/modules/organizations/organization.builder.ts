import { Builder } from 'builder-pattern';

import { IOrganization, newDbId } from '@malou-io/package-models';

type OrganizationPayload = IOrganization;

const _buildOrganization = (organization: OrganizationPayload) => Builder<OrganizationPayload>(organization);

export const getDefaultOrganization = () =>
    _buildOrganization({
        _id: newDbId(),
        name: 'Test Organization',
        createdAt: new Date(),
        updatedAt: new Date(),
        reviewPublicBusinessIdCount: 0,
    });
