import { singleton } from 'tsyringe';

import {
    CommentReviewDto,
    CreatePrivateReviewDto,
    ReviewResponseDto,
    ReviewWithTranslationsResponseDto,
    SearchPrivateReviewBodyDto,
    SemanticAnalysisInsightsReviewDto,
} from '@malou-io/package-dto';
import { IPrivateReview, toDbId } from '@malou-io/package-models';
import { PlatformKey, ReviewType } from '@malou-io/package-utils';

import {
    PrivateReviewsSearchFilters,
    PrivateReviewWithScanWithNfc,
    PrivateReviewWithSemanticAnalysisAndSentiment,
    PrivateReviewWithTranslations,
} from ':modules/private-reviews/private-reviews.interface';
import { ReviewAnalysesDtoMapper } from ':modules/review-analyses/mappers/review-analyses.mapper.dto';
import { ScansDtoMapper } from ':modules/scans/scans.dto-mapper';
import { Translations } from ':modules/translations/entities/translations.entity';

@singleton()
export class PrivateReviewsDtoMapper {
    constructor(
        private readonly _scanDtoMapper: ScansDtoMapper,
        private readonly _reviewAnalysesDtoMapper: ReviewAnalysesDtoMapper
    ) {}

    toPrivateReviewsSearchFilters(dto: SearchPrivateReviewBodyDto): PrivateReviewsSearchFilters {
        return {
            scanIds: dto.scanIds?.map((id) => toDbId(id)),
        };
    }

    toPrivateReviewResponseDto(model: IPrivateReview): ReviewResponseDto {
        const { text, lang, rating, archived } = model;

        return {
            ...model,
            id: model._id.toString(),
            _id: model._id.toString(),
            platformId: undefined,
            restaurantId: model.restaurantId.toString(),
            key: model.key as PlatformKey,
            socialId: model._id.toString(),
            socialLink: undefined,
            businessSocialLink: undefined,
            type: ReviewType.RATING,
            text,
            title: undefined,
            socialTranslatedText: undefined,
            lang,
            rating,
            socialRating: undefined,
            reviewer: undefined,
            archived,
            wasAnsweredAutomatically: false,
            platformPresenceStatus: undefined,
            socialCreatedAt: model.socialCreatedAt?.toISOString(),
            socialUpdatedAt: model.socialCreatedAt?.toISOString(),
            comments: model.comments.map((comment) => this._toCommentPrivateReviewDto(comment)),
            socialAttachments: [],
            scanId: model.scanId?.toString(),
            campaignId: model.campaignId?.toString(),
            clientId: model.clientId?.toString(),
            semanticAnalysisFetchStatus: model.semanticAnalysisFetchStatus ?? null,
            publicBusinessId: model.publicBusinessId,
            responseStyle: model.responseStyle ?? undefined,
            reviewerNameValidation: model.reviewerNameValidation ?? undefined,
        };
    }

    toPrivateReviewWithTranslationsResponseDto(model: PrivateReviewWithTranslations): ReviewWithTranslationsResponseDto {
        const privateReviewResponseDto = this.toPrivateReviewResponseDto(model);
        const translations = model.translations
            ? new Translations({ ...model.translations, id: model.translations._id.toString() })
            : undefined;

        return {
            ...privateReviewResponseDto,
            translations: translations?.toDto(),
        };
    }

    toPrivateReviewWithScanResponseDto(model: PrivateReviewWithScanWithNfc): ReviewResponseDto {
        return {
            ...this.toPrivateReviewResponseDto(model),
            scan: model.scan ? this._scanDtoMapper.toScanWithNfcDto(model.scan) : undefined,
            semanticAnalysis: model.semanticAnalysis
                ? (this._reviewAnalysesDtoMapper.fromSemanticAnalysisToReviewAnalysisDto(model.semanticAnalysis) ?? undefined)
                : undefined,
            semanticAnalysisSegments: model.semanticAnalysisSegments
                ? this._reviewAnalysesDtoMapper.fromSemanticAnalysisToReviewAnalysisDtoV2(
                      model.semanticAnalysisSegments,
                      model.restaurantId.toString()
                  )
                : undefined,
        };
    }

    fromPrivateReviewWithScanWithNfcToReviewWithTranslationsResponseDto(
        model: PrivateReviewWithScanWithNfc
    ): ReviewWithTranslationsResponseDto {
        return {
            ...this.toPrivateReviewWithTranslationsResponseDto(model),
            scan: model.scan ? this._scanDtoMapper.toScanWithNfcDto(model.scan) : undefined,
            semanticAnalysis: model.semanticAnalysis
                ? (this._reviewAnalysesDtoMapper.fromSemanticAnalysisToReviewAnalysisDto(model.semanticAnalysis) ?? undefined)
                : undefined,
            semanticAnalysisSegments: model.semanticAnalysisSegments
                ? this._reviewAnalysesDtoMapper.fromSemanticAnalysisToReviewAnalysisDtoV2(
                      model.semanticAnalysisSegments,
                      model.restaurantId.toString()
                  )
                : undefined,
        };
    }

    toCreatePrivateReview(dto: CreatePrivateReviewDto['privateReview']): Partial<IPrivateReview> {
        const { text, lang, rating, archived, campaignId, clientId, scanId } = dto;

        return {
            restaurantId: toDbId(dto.restaurantId),
            text,
            lang: lang ?? undefined,
            rating,
            archived,
            socialCreatedAt: dto.socialCreatedAt ? new Date(dto.socialCreatedAt) : undefined,
            campaignId: campaignId ? toDbId(campaignId) : undefined,
            clientId: clientId ? toDbId(clientId) : undefined,
            scanId: scanId ? toDbId(scanId) : undefined,
        };
    }

    toSemanticAnalysisInsightsPrivateReviewDto(model: PrivateReviewWithSemanticAnalysisAndSentiment): SemanticAnalysisInsightsReviewDto {
        return {
            ...this.toPrivateReviewResponseDto(model),
            reviewer: {
                displayName: [model.client?.firstName, model.client?.lastName].filter(Boolean).join(' '),
                profilePhotoUrl: undefined,
            },
            sentiment: model.sentiment,
            semanticAnalysisSegments: model.semanticAnalysisSegments
                ? this._reviewAnalysesDtoMapper.fromSemanticAnalysisToReviewAnalysisDtoV2(
                      model.semanticAnalysisSegments,
                      model.restaurantId.toString()
                  )
                : [],
            ratingTags: [],
            menuItemReviews: [],
            socialId: '',
        };
    }

    private _toCommentPrivateReviewDto(comment: IPrivateReview['comments'][0]): CommentReviewDto {
        const { text, content } = comment;

        return {
            ...comment,
            _id: comment._id.toString(),
            id: comment._id.toString(),
            socialId: comment._id.toString(),
            text,
            socialTranslatedText: undefined,
            keywordAnalysis: undefined,
            socialUpdatedAt: comment.socialUpdatedAt?.toISOString(),
            user: undefined,
            posted: undefined,
            isMalou: true,
            author: {
                _id: comment.author._id.toString(),
                name: comment.author.name,
            },
            templateIdUsed: comment.templateIdUsed?.toString(),
            isRepliedFromAggregatedView: comment.isRepliedFromAggregatedView,
            aiInteractionIdUsed: undefined,
            retries: undefined,
            content,
        };
    }
}
