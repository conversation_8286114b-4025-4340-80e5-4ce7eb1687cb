import _ from 'lodash';
import { singleton } from 'tsyringe';

import { CtaProps } from '@malou-io/package-dto';
import { IPost, toDbId } from '@malou-io/package-models';
import { APP_DEFAULT_LOCALE, filterByRequiredKeys, Locale, PlatformKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import PostsUseCases from ':modules/posts/posts.use-cases';
import { Report } from ':modules/reports/report.entity';
import { IPerformancePeriods } from ':modules/reports/use-cases/performance-reports/performance-reports.interface';
import { Translation } from ':services/translation.service';

@singleton()
export class GetGmbPostsMonthlyPerformanceRecommendationsUseCase {
    private readonly MIN_POSTS_COUNT = 4;
    private readonly MIN_KEYWORD_SCORE = 4;
    private readonly MIN_POSTS_PERCENTAGE_WITH_CTA = 40;

    constructor(
        private readonly _translator: Translation,
        private readonly _postsUseCases: PostsUseCases
    ) {}

    async execute({
        report,
        config,
        periods,
    }: {
        report: Report;
        config: Report['configurations'][0];
        periods: IPerformancePeriods;
    }): Promise<CtaProps | undefined> {
        const metaData = report.getMetaDataFromConfig(config);

        try {
            const { startDate, endDate } = periods.current;
            const lang = metaData.user?.defaultLanguage ?? APP_DEFAULT_LOCALE;

            const posts = await this._postsUseCases.getPublishedPostsForPerformanceReport({
                restaurantIds: config.restaurants.map((restaurant) => toDbId(restaurant._id)),
                startDate,
                endDate,
                platformKey: PlatformKey.GMB,
            });

            const noticeText = this._getPostsRecommendation(posts, lang);

            return {
                noticeText,
                link: `${report.getBaseUrl(config)}/statistics/seo?from_email=${report.type}&clicked_on=gmb_posts_recommendation_cta`,
            };
        } catch (err) {
            logger.error(`${report.getLogGroup()} GMB insights section, get posts recommendation`, {
                err,
                ...metaData,
            });

            return undefined;
        }
    }

    private _getPostsRecommendation(posts: IPost[], lang: Locale): string {
        if (posts.length === 0) {
            return this._translator.fromLang({ lang }).recommendations.posts.monthly_performance_report.gmb.postAtLeastTimesAWeek();
        }

        const groupedPosts = _.chain(posts)
            .groupBy('restaurantId')
            .map((value, key) => {
                return {
                    key,
                    nbPosts: value.length,
                };
            })
            .value();

        const goodRestaurantPostingCount = groupedPosts.filter((value) => value.nbPosts >= this.MIN_POSTS_COUNT);

        if (
            (groupedPosts.length === 1 && groupedPosts[0].nbPosts < this.MIN_POSTS_COUNT) ||
            goodRestaurantPostingCount.length < groupedPosts.length / 2
        ) {
            return this._translator.fromLang({ lang }).recommendations.posts.monthly_performance_report.gmb.postAtLeastTimesAWeek();
        }

        const postsWithKeywordAnalysis = filterByRequiredKeys(posts, ['keywordAnalysis']);
        const keywordAnalysisScore = filterByRequiredKeys(
            postsWithKeywordAnalysis.map(({ keywordAnalysis }) => keywordAnalysis),
            ['score']
        );
        const postsAverageKeywordsScore =
            keywordAnalysisScore.reduce((acc, keywordAnalysis) => acc + keywordAnalysis.score, 0) / keywordAnalysisScore.length;

        if (postsAverageKeywordsScore < this.MIN_KEYWORD_SCORE) {
            return this._translator.fromLang({ lang }).recommendations.posts.monthly_performance_report.gmb.shouldIncreaseKeywordsScore();
        }

        // Compute % of posts with CTA
        const hasCta = (post: IPost) => !!post?.callToAction?.actionType && !!post?.callToAction?.url;
        const postsWithCta = posts.filter(hasCta);

        if ((postsWithCta.length * 100) / posts.length < this.MIN_POSTS_PERCENTAGE_WITH_CTA) {
            return this._translator.fromLang({ lang }).recommendations.posts.monthly_performance_report.gmb.congratsForPosting();
        }

        return this._translator.fromLang({ lang }).recommendations.posts.monthly_performance_report.gmb.congratsForPostingWithALotOfCta();
    }
}
