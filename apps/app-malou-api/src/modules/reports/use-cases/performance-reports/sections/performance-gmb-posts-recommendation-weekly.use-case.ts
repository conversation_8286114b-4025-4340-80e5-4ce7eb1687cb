import { singleton } from 'tsyringe';

import { CtaSectionProps } from '@malou-io/package-dto';
import { IPost, toDbId } from '@malou-io/package-models';
import { APP_DEFAULT_LOCALE, filterByRequiredKeys, Locale, PlatformKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import PostsUseCases from ':modules/posts/posts.use-cases';
import { Report } from ':modules/reports/report.entity';
import { Translation } from ':services/translation.service';

@singleton()
export class GetGmbPostsWeeklyPerformanceRecommendationsUseCase {
    private readonly MIN_KEYWORD_SCORE = 4;
    private readonly MIN_POSTS_PERCENTAGE_WITH_CTA = 40;

    constructor(
        private readonly _translator: Translation,
        private readonly _postsUseCases: PostsUseCases
    ) {}

    async execute({
        report,
        config,
        startDate,
        endDate,
    }: {
        report: Report;
        config: Report['configurations'][0];
        startDate: Date;
        endDate: Date;
    }): Promise<CtaSectionProps | undefined> {
        const metaData = report.getMetaDataFromConfig(config);

        try {
            const lang = metaData.user?.defaultLanguage ?? APP_DEFAULT_LOCALE;
            const posts = await this._postsUseCases.getPlannedPostsForPerformanceReport({
                restaurantIds: config.restaurants.map((restaurant) => toDbId(restaurant._id)),
                startDate,
                endDate,
                platformKey: PlatformKey.GMB,
            });

            const { title, subtitle } = this._getPostsRecommendation(posts, lang);

            return {
                title,
                subtitle,
                link: `${report.getBaseUrl(config)}/statistics/seo?from_email=${report.type}&clicked_on=gmb_posts_recommendation_cta`,
            };
        } catch (err) {
            logger.error(`${report.getLogGroup()} GMB insights section, get posts recommendation`, {
                err,
                ...metaData,
            });

            return undefined;
        }
    }

    private _getPostsRecommendation(
        posts: IPost[],
        lang: Locale
    ): {
        title: string;
        subtitle: string;
    } {
        if (posts.length === 0) {
            return {
                title: this._translator.fromLang({ lang }).recommendations.posts.weekly_performance_report.gmb.no_post.title(),
                subtitle: this._translator.fromLang({ lang }).recommendations.posts.weekly_performance_report.gmb.no_post.subtitle(),
            };
        }

        const postsWithKeywordAnalysisScore = posts.filter(
            (post) => post.keywordAnalysis?.score !== null && post.keywordAnalysis?.score !== undefined
        );

        const postsWithKeywordAnalysis = filterByRequiredKeys(postsWithKeywordAnalysisScore, ['keywordAnalysis']);
        const keywordAnalysisScore = filterByRequiredKeys(
            postsWithKeywordAnalysis.map(({ keywordAnalysis }) => keywordAnalysis),
            ['score']
        );
        const postsAverageKeywordsScore =
            keywordAnalysisScore.reduce((acc, keywordAnalysis) => acc + keywordAnalysis.score, 0) / postsWithKeywordAnalysisScore.length;

        if (postsAverageKeywordsScore < this.MIN_KEYWORD_SCORE) {
            return {
                title: this._translator
                    .fromLang({ lang })
                    .recommendations.posts.weekly_performance_report.gmb.low_average_keywords_score.title(),
                subtitle: this._translator
                    .fromLang({ lang })
                    .recommendations.posts.weekly_performance_report.gmb.low_average_keywords_score.subtitle(),
            };
        }

        // Compute % of posts with CTA
        const hasCta = (post: IPost) => !!post?.callToAction?.actionType && !!post?.callToAction?.url;
        const postsWithCta = posts.filter(hasCta);

        if ((postsWithCta.length * 100) / posts.length < this.MIN_POSTS_PERCENTAGE_WITH_CTA) {
            return {
                title: this._translator.fromLang({ lang }).recommendations.posts.weekly_performance_report.gmb.low_cta_rate.title(),
                subtitle: this._translator.fromLang({ lang }).recommendations.posts.weekly_performance_report.gmb.low_cta_rate.subtitle(),
            };
        }

        return {
            title: this._translator.fromLang({ lang }).recommendations.posts.weekly_performance_report.gmb.good_posting_habits.title(),
            subtitle: this._translator
                .fromLang({ lang })
                .recommendations.posts.weekly_performance_report.gmb.good_posting_habits.subtitle(),
        };
    }
}
