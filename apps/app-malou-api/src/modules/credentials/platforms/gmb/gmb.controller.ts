import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import {
    GetAssociatedLocationsParamsDto,
    getAssociatedLocationsParamsValidator,
    GetAssociatedLocationsQueryDto,
    getAssociatedLocationsQueryValidator,
} from '@malou-io/package-dto';
import { EntityRepository, ICredential } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { Params, Query } from ':helpers/decorators/validators';
import { logger } from ':helpers/logger';
import { _getNewOrganizationIdsBuilder } from ':modules/credentials/credentials.use-cases';
import { GmbCredentialsRepository } from ':modules/credentials/platforms/gmb/gmb.repository';
import { GmbApiProviderUseCases } from ':modules/credentials/platforms/gmb/gmb.use-cases';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { UsersRepository } from ':modules/users/users.repository';
import { CachePrefixKey, deleteCachePrefix } from ':plugins/cache-middleware';

@singleton()
export default class GmbCredentialsController {
    constructor(
        private _gmbCredentialsRepository: GmbCredentialsRepository,
        private _usersRepository: UsersRepository,
        private _gmbCredentialsUseCases: GmbApiProviderUseCases,
        private _platformsRepository: PlatformsRepository,
        private _restaurantsRepository: RestaurantsRepository
    ) {}

    handleOauth2Callback = async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { code, state } = req.query;
            // State added by GetOauthUrlService when generating login URL
            const { user: userId, location, restaurantId } = JSON.parse(state as string);

            const user = await this._usersRepository.findOneOrFail({
                filter: { _id: userId },
                options: { populate: [{ path: 'organizations' }], lean: true },
            });
            if (!user.organizations?.length) {
                return res.redirect(`${Config.baseAppUrl}/credentials/missing_organization`);
            }
            if (!code) {
                // user cancelled process
                return res.redirect(`${Config.baseAppUrl}`);
            }
            const autoOrganization = user.organizations?.length === 1 ? user.organizations[0] : null;
            const getNewOrganizationIds = _getNewOrganizationIdsBuilder(
                this._gmbCredentialsRepository as unknown as EntityRepository<ICredential>,
                this._platformsRepository,
                this._restaurantsRepository,
                PlatformKey.GMB
            );
            const handleOauthCallback = this._gmbCredentialsUseCases.handleOauthCallBackUseCase(
                this._gmbCredentialsUseCases,
                this._gmbCredentialsRepository as unknown as EntityRepository<ICredential>,
                getNewOrganizationIds
            );
            const callback = await handleOauthCallback({ user, restaurantId, code: code as string });
            if (!callback) {
                throw new MalouError(MalouErrorCode.CREDENTIALS_GMB_API_ERROR, {
                    message: 'Error while handling oauth callback',
                    metadata: { user, restaurantId, code },
                });
            }
            const { _id, authId } = callback;
            const credentials = await this._gmbCredentialsRepository.find({ filter: { authId } });
            const credentialIds = credentials.map((e) => e._id);
            deleteCachePrefix(
                CachePrefixKey.GET_PLATFORM_RESTAURANT_ACCOUNTS,
                credentialIds.map((e) => [e.toString()])
            ).catch(logger.error);
            res.redirect(
                `${
                    Config.baseAppUrl
                }/credentials/validate?_id=${_id}&key=gmb&should_choose_organization=${!autoOrganization}&redirect=${encodeURIComponent(
                    location
                )}&auth_id=${authId}`
            );
        } catch (error) {
            next(error);
        }
    };

    handleGetAssociatedAccounts = async (req: Request, res: Response, next: NextFunction) => {
        const { credential_id: credentialId } = req.params;

        return this._gmbCredentialsUseCases
            .fetchAccounts(credentialId)
            .then((accounts) => res.json({ data: accounts }))
            .catch(next);
    };

    @Query(getAssociatedLocationsQueryValidator)
    @Params(getAssociatedLocationsParamsValidator)
    async handleGetAssociatedLocations(
        req: Request<GetAssociatedLocationsParamsDto, any, any, GetAssociatedLocationsQueryDto>,
        res: Response,
        next: NextFunction
    ) {
        const { accountId } = req.query;
        const { credentialId } = req.params;
        return this._gmbCredentialsUseCases
            .fetchAccountLocations(accountId, credentialId)
            .then((locations) => res.json({ data: locations }))
            .catch(next);
    }

    handleGetLocationAttributes = async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { location_id: locationId } = req.params;
            const { credentialId } = req.query;
            const apiEndpointV2 = `locations/${locationId}`;
            const locationAttributes = await this._gmbCredentialsUseCases.getLocationAttributes(credentialId as string, apiEndpointV2);
            res.json({ data: locationAttributes?.data });
        } catch (err) {
            next(err);
        }
    };

    handleGetLocationMedias = async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { location_id: locationId } = req.params;
            const { account_id: accountId, all } = req.query;
            // all = false only get profile and cover
            const shouldGetAll = all === 'true';
            const endpoint = `accounts/${accountId}/locations/${locationId}`;
            const { credentials } = await this._platformsRepository.findOneOrFail({
                filter: { apiEndpoint: endpoint },
                options: { lean: true },
            });
            const credentialId = credentials?.[0];
            const medias = await this._gmbCredentialsUseCases.listLocationMedia(credentialId, endpoint, shouldGetAll);
            res.json({
                data: medias,
            });
        } catch (error) {
            next(error);
        }
    };
}
