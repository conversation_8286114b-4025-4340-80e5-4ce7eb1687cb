import { singleton } from 'tsyringe';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { SevenroomsCredentialRepository } from ':modules/credentials/platforms/sevenrooms/sevenrooms.repository';
import { SevenroomsProvider } from ':providers/sevenrooms/sevenrooms.provider';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class ExtendSevenroomsCredentialExpirationJob extends GenericJobDefinition {
    constructor(
        private readonly _sevenroomsProvider: SevenroomsProvider,
        private readonly _sevenroomsCredentialRepository: SevenroomsCredentialRepository,
        private readonly _slackService: SlackService
    ) {
        super({
            agendaJobName: AgendaJobName.EXTEND_SEVENROOMS_CREDENTIAL_EXPIRATION,
        });
    }

    async executeJob(): Promise<void> {
        try {
            const { sessionId, csrfToken } = await this._sevenroomsCredentialRepository.getSuperCredential();
            await this._sevenroomsProvider.getReservationUrl({
                sessionId,
                csrfToken,
            });
            await this._sevenroomsCredentialRepository.updateSuperCredentialExpiration();
        } catch (error) {
            this._slackService.sendAlert({
                channel: SlackChannel.APP_ALERTS,
                data: {
                    err: error,
                    message: 'Error while extending Sevenrooms credential expiration',
                    jobName: this.agendaJobName,
                },
            });
        }
    }
}
