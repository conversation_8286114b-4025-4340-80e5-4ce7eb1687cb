import { groupBy } from 'lodash';
import { DateTime } from 'luxon';
import { autoInjectable } from 'tsyringe';

import { DiagnosticDto } from '@malou-io/package-dto';
import { IDiagnostic } from '@malou-io/package-models';
import {
    AiInteractionRelatedEntityCollection,
    DEFAULT_REVIEW_RATING,
    filterByRequiredKeys,
    isNotNil,
    MalouErrorCode,
    MaloupeLocale,
    PlatformKey,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { ReviewPagination } from ':helpers/pagination';
import { GenerateReviewSemanticAnalysisWithoutTopicsService } from ':modules/ai/services/generate-review-semantic-analysis-without-topics.service';
import DiagnosticsRepository from ':modules/diagnostics/diagnostic.repository';
import { Diagnostic } from ':modules/diagnostics/entities/diagnostic.entity';
import { ReviewFiltersInput, ReviewWithSemanticAnalysis } from ':modules/reviews/reviews.interfaces';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { SegmentAnalysesRepository } from ':modules/segment-analyses/segment-analyses.repository';
import { ReviewsSemanticAnalysisService } from ':services/semantic-analysis/reviews/reviews.semantic-analysis.service';
import { RestaurantReviewSemanticAnalysisOverviewPayload } from ':services/semantic-analysis/reviews/reviews.semantic-analysis.types';
import { SlackChannel, SlackService } from ':services/slack.service';

@autoInjectable()
export class UpdateDiagnosticWithReviewsAnalysesUseCase {
    constructor(
        private readonly _diagnosticsRepository: DiagnosticsRepository,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _segmentAnalysesRepository: SegmentAnalysesRepository,
        private readonly _reviewsSemanticAnalysisService: ReviewsSemanticAnalysisService,
        private readonly _generateReviewSemanticAnalysisWithoutTopicsService: GenerateReviewSemanticAnalysisWithoutTopicsService,
        private readonly _slackService: SlackService
    ) {}

    async execute(malouDiagnosticId: string): Promise<DiagnosticDto> {
        const partialDiagnostic = await this._diagnosticsRepository.getDiagnosticById(malouDiagnosticId);

        if (!partialDiagnostic) {
            throw new MalouError(MalouErrorCode.DIAGNOSTIC_NOT_FOUND, {
                message: 'Diagnostic not found',
                metadata: { malouDiagnosticId },
            });
        }

        const reviews = partialDiagnostic.reviews;
        if (
            reviews &&
            reviews.length > 0 &&
            reviews.some((review) => review.semanticAnalysisSegments && review.semanticAnalysisSegments.length > 0)
        ) {
            return partialDiagnostic.toDto();
        }

        try {
            const updatedReviews = await this._getRestaurantReviews(partialDiagnostic);
            const semanticAnalysisOverviewByLang = await this._getRestaurantReviewSemanticAnalysisOverview(
                partialDiagnostic,
                updatedReviews
            );
            const diagnostic = await this._diagnosticsRepository.updateDiagnosticWithSemanticallyAnalyzedReviews(
                partialDiagnostic.id,
                updatedReviews,
                semanticAnalysisOverviewByLang
            );

            if (!diagnostic) {
                throw new MalouError(MalouErrorCode.DIAGNOSTIC_CANNOT_UPDATE_DIAGNOSTIC, {
                    message: 'Cannot update diagnostic',
                    metadata: { malouDiagnosticId },
                });
            }

            return diagnostic.toDto();
        } catch (error) {
            this._slackService.sendAlert({
                channel: SlackChannel.MALOUPE_ALERTS,
                data: {
                    err: error,
                    message: 'Error while updating diagnostic with reviews analyses',
                    metadata: { malouDiagnosticId },
                },
            });
            throw error;
        }
    }

    private async _getRestaurantReviews(partialDiagnostic: Diagnostic): Promise<IDiagnostic['reviews']> {
        if (partialDiagnostic.restaurantId) {
            return await this._getMalouRestaurantReviews(partialDiagnostic.restaurantId);
        } else {
            return await this._getNonMalouRestaurantReviews(partialDiagnostic);
        }
    }

    private async _getMalouRestaurantReviews(
        restaurantId: string,
        isSemanticAnalysisFeatureEnabled: boolean = false // TODO: Semantic Analysis Reports - 'release-new-semantic-analysis' - remove default value
    ): Promise<IDiagnostic['reviews']> {
        const pagination = new ReviewPagination({ pageSize: 1000, total: 0 });

        const reviews: ReviewWithSemanticAnalysis[] = (await this._reviewsRepository.getRestaurantReviewsPaginatedV2({
            pagination,
            filters: this._getReviewsFilters(restaurantId),
            isSemanticAnalysisFeatureEnabled,
        })) as ReviewWithSemanticAnalysis[]; // Because we have showPrivate: false

        const reviewsSocialIds = reviews.map((review) => review.socialId);
        const reviewsSegmentAnalysis =
            await this._segmentAnalysesRepository.getSegmentAnalysesReviewCountByCategoryAndSentimentByReviewsSocialIds(reviewsSocialIds);

        return reviewsSegmentAnalysis
            .map((reviewSegmentAnalysis) => {
                const matchedReview = reviews.find((review) => review.socialId === reviewSegmentAnalysis.reviewSocialId);
                if (!matchedReview) {
                    return null;
                }
                return {
                    author: matchedReview.reviewer?.displayName,
                    text: matchedReview.text ?? undefined,
                    rating: matchedReview.rating ?? DEFAULT_REVIEW_RATING,
                    socialId: matchedReview.socialId,
                    language: matchedReview.lang ?? undefined,
                    segmentAnalyses: reviewSegmentAnalysis.analysis.map((segmentAnalysis) => ({
                        category: segmentAnalysis.category,
                        sentiment: segmentAnalysis.sentiment,
                        segment: segmentAnalysis.segment,
                    })),
                };
            })
            .filter(isNotNil);
    }

    private _getReviewsFilters(restaurantId: string): ReviewFiltersInput {
        const now = DateTime.now();
        return {
            restaurantIds: [restaurantId],
            searchText: '',
            platforms: [PlatformKey.GMB],
            showPrivate: false,
            ratings: [1, 2, 3, 4, 5],
            startDate: now.minus({ months: 1 }).toJSDate(),
            endDate: now.toJSDate(),
            answered: true,
            notAnswered: true,
            pending: true,
            archived: true,
            unarchived: true,
            withText: true,
            withoutText: false,
            answerable: false,
            privatePlatforms: undefined,
        };
    }

    private async _getNonMalouRestaurantReviews(partialDiagnostic: Diagnostic): Promise<IDiagnostic['reviews']> {
        if (!partialDiagnostic.reviews) {
            return [];
        }
        const reviews = filterByRequiredKeys(partialDiagnostic.reviews, ['text']);
        const reviewsAnalysis = await Promise.all(
            reviews.map((review) =>
                this._generateReviewSemanticAnalysisWithoutTopicsService.execute({
                    placeId: partialDiagnostic.placeId,
                    review: {
                        text: review.text,
                        rating: review.rating,
                        socialId: review.socialId,
                    },
                })
            )
        );

        return reviews?.map((review, index) => {
            return {
                ...review,
                segmentAnalyses: reviewsAnalysis[index]?.map((segmentAnalysis) => ({
                    category: segmentAnalysis.category,
                    sentiment: segmentAnalysis.sentiment,
                    segment: segmentAnalysis.segment,
                })),
            } as NonNullable<Diagnostic['reviews']>[number];
        });
    }

    private async _getRestaurantReviewSemanticAnalysisOverview(
        diagnostic: Diagnostic,
        reviews: IDiagnostic['reviews']
    ): Promise<NonNullable<IDiagnostic['semanticAnalysisOverviewByLang']> | null> {
        const restaurantName = diagnostic.restaurant.name;
        const langs = ['French', 'English'];
        const reviewsSemanticAnalysis = this._selectSegmentsFromReviews(reviews);
        const restaurantsPayloadData: RestaurantReviewSemanticAnalysisOverviewPayload = {
            restaurantName,
            reviewsSemanticAnalysis,
        };
        const semanticAnalysesOverview = await Promise.all(
            langs.map((lang) =>
                this._reviewsSemanticAnalysisService.getSemanticAnalysisOverview({
                    collection: AiInteractionRelatedEntityCollection.DIAGNOSTICS,
                    collectionId: diagnostic.id,
                    language: lang,
                    restaurantsData: [restaurantsPayloadData],
                })
            )
        );
        if (!semanticAnalysesOverview[0]?.semanticAnalysisResult || !semanticAnalysesOverview[1]?.semanticAnalysisResult) {
            return null;
        }
        return {
            [MaloupeLocale.FR]: semanticAnalysesOverview[0].semanticAnalysisResult,
            [MaloupeLocale.EN]: semanticAnalysesOverview[1].semanticAnalysisResult,
        };
    }

    private _selectSegmentsFromReviews(
        reviews: IDiagnostic['reviews']
    ): RestaurantReviewSemanticAnalysisOverviewPayload['reviewsSemanticAnalysis'] {
        const allSegmentAnalyses = reviews
            ?.flatMap((review) =>
                review.semanticAnalysisSegments?.map(({ category, sentiment, segment }) => ({
                    category,
                    sentiment,
                    segment,
                }))
            )
            .filter((segment) => segment !== undefined);

        // Select 5 segments per couple tag/sentiment
        const MAXIMUM_SELECTED_TAGS = 5;

        const groupedSegmentAnalyses = groupBy(allSegmentAnalyses, (segment) => `${segment.category}-${segment.sentiment}`);
        const selectedSegments = Object.values(groupedSegmentAnalyses).map((segments) => segments.slice(0, MAXIMUM_SELECTED_TAGS));
        return selectedSegments.flat();
    }
}
