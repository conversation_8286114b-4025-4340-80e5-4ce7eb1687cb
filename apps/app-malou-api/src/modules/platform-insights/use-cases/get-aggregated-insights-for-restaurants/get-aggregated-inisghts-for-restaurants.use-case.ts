import { inject, singleton } from 'tsyringe';

import { GetInsightsAggregatedRequestBodyDto } from '@malou-io/package-dto';
import { filterByRequiredKeys, MalouErrorCode, PartialRecord, PlatformKey, TimeInSeconds } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { InjectionToken } from ':helpers/injection';
import { TimeScaleToMetricToDataValues } from ':modules/platform-insights/platform-insights.types';
import { GetAggregatedInsightsForRestaurantAndPlatformUseCase } from ':modules/platform-insights/use-cases/get-aggregated-insights-for-restaurant-and-platform/get-aggregated-insights-for-restaurant-and-platform.use-case';
import { Cache } from ':plugins/cache';
import { CachePrefixKey, computeCacheKey } from ':plugins/cache-middleware';

interface InsightsWithDetails {
    restaurantId: string;
    platformKey: PlatformKey;
    insights?: TimeScaleToMetricToDataValues;
}

type InsightsByPlatformByRestaurant = PartialRecord<string, PartialRecord<PlatformKey, TimeScaleToMetricToDataValues>>;

@singleton()
export class GetAggregatedInsightsForRestaurantsUseCase {
    constructor(
        private readonly _getAggregatedInsightsForRestaurantAndPlatformUseCase: GetAggregatedInsightsForRestaurantAndPlatformUseCase,
        @inject(InjectionToken.Cache) private readonly _cache: Cache
    ) {}

    async execute(
        { aggregators, endDate, metrics, platformKeys, previousPeriod, restaurantIds, startDate }: GetInsightsAggregatedRequestBodyDto,
        // TODO: removed this parameter later
        isAggregatedSocialMediaPerformanceReleaseEnabled: boolean = false
    ): Promise<InsightsByPlatformByRestaurant> {
        const insightsConditions = { metrics, aggregators };

        if (!startDate || !endDate) {
            throw new MalouError(MalouErrorCode.PLATFORM_INSIGHTS_ERROR, {
                message: 'Start date and end date are required',
                metadata: { startDate, endDate },
            });
        }
        const periodConditions = {
            startDate: new Date(startDate),
            endDate: new Date(endDate),
            previousPeriod: previousPeriod || false,
        };

        const insightsWithDetailsPromises = restaurantIds.flatMap((restaurantId): Promise<InsightsWithDetails>[] =>
            platformKeys.map(async (platformKey): Promise<InsightsWithDetails> => {
                const suffixKey = `${restaurantId}-${platformKey}-${JSON.stringify(insightsConditions)}-${JSON.stringify(
                    periodConditions
                )}`;
                const key: string = computeCacheKey(CachePrefixKey.GET_INSIGHTS_AGGREGATED_BY_RESTAURANT, suffixKey);
                const cachedData: TimeScaleToMetricToDataValues = await this._cache.get(key);
                if (cachedData && !cachedData.error) {
                    return { restaurantId, platformKey, insights: cachedData };
                }
                const insights = await this._getAggregatedInsightsForRestaurantAndPlatformUseCase.execute(
                    {
                        restaurantId,
                        platformKey,
                        insightsConditions,
                        periodConditions,
                    },
                    isAggregatedSocialMediaPerformanceReleaseEnabled
                );
                if (insights && !insights.error) {
                    await this._cache.set(key, insights, 10 * TimeInSeconds.MINUTE);
                }
                return { restaurantId, platformKey, insights };
            })
        );

        const insightsWithDetails = await Promise.all(insightsWithDetailsPromises);

        if (insightsWithDetails.length > 0 && insightsWithDetails.every(({ insights }) => insights?.error)) {
            const insightWithDetails = filterByRequiredKeys(insightsWithDetails, ['insights', 'platformKey'])[0];
            throw new MalouError(MalouErrorCode.PLATFORM_INSIGHTS_ERROR, {
                metadata: {
                    index: 0,
                    detail: insightWithDetails.insights.message,
                    key: insightWithDetails.platformKey,
                },
            });
        }
        const insightsByPlatformByRestaurant: InsightsByPlatformByRestaurant = insightsWithDetails.reduce(
            (acc: InsightsByPlatformByRestaurant, { restaurantId, platformKey, insights }) => {
                if (!acc[restaurantId]) {
                    acc[restaurantId] = {};
                }
                acc[restaurantId][platformKey] = insights;
                return acc;
            },
            {}
        );

        return insightsByPlatformByRestaurant;
    }
}
