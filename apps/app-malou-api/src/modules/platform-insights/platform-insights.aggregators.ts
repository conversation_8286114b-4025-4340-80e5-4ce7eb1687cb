import { isNumber } from 'lodash';

import {
    AggregationTimeScale,
    filterByRequiredKeys,
    getMonthsFromPeriod,
    getWeeksFromPeriod,
    isInDayList,
    isSameDay,
    MalouMetric,
    Month,
} from '@malou-io/package-utils';

import {
    DailyValue,
    MetricToDataValues,
    TimeScaleToMetricToDataValues,
    TotalComputationRule,
} from ':modules/platform-insights/platform-insights.types';

export class InsightsAggregator {
    aggregateInsights = (
        insightsByDay: MetricToDataValues<DailyValue>,
        aggregators: AggregationTimeScale[],
        startDate: Date,
        endDate: Date
    ): TimeScaleToMetricToDataValues => {
        const aggregatedData: TimeScaleToMetricToDataValues = {};
        for (const aggregator of aggregators) {
            switch (aggregator) {
                case AggregationTimeScale.TOTAL:
                    aggregatedData[AggregationTimeScale.TOTAL] = this._aggregateInsightsTotal(insightsByDay);
                    break;
                case AggregationTimeScale.BY_WEEK:
                    aggregatedData[AggregationTimeScale.BY_WEEK] = this._aggregateInsightsByWeek(insightsByDay, startDate, endDate);
                    break;
                case AggregationTimeScale.BY_MONTH:
                    aggregatedData[AggregationTimeScale.BY_MONTH] = this._aggregateInsightsByMonth(insightsByDay, startDate, endDate);
                    break;
                case AggregationTimeScale.BY_DAY:
                default:
                    aggregatedData[AggregationTimeScale.BY_DAY] = insightsByDay;
                    break;
            }
        }
        return aggregatedData;
    };

    private _aggregateInsightsTotal = (
        insightsByDay: MetricToDataValues<DailyValue>
    ): TimeScaleToMetricToDataValues[AggregationTimeScale.TOTAL] => {
        const totalInsights: TimeScaleToMetricToDataValues[AggregationTimeScale.TOTAL] = {};
        Object.keys(insightsByDay).forEach((malouMetric) => {
            const totalComputationRule = this._getTotalComputationRule(malouMetric as MalouMetric);
            totalInsights[malouMetric as MalouMetric] = {
                value:
                    totalComputationRule === TotalComputationRule.TAKE_LAST_ITEM
                        ? (insightsByDay[malouMetric as MalouMetric]?.[0]?.value ?? 0)
                        : (filterByRequiredKeys(insightsByDay[malouMetric as MalouMetric] ?? [], ['value'])
                              .map((v) => v.value)
                              ?.reduce((acc, curr) => acc + curr, 0) ?? 0),
            };
        });
        return totalInsights;
    };

    private _getTotalComputationRule = (metric: MalouMetric): TotalComputationRule => {
        switch (metric) {
            case MalouMetric.FOLLOWERS:
            case MalouMetric.PLATFORM_RATING:
                return TotalComputationRule.TAKE_LAST_ITEM;
            default:
                return TotalComputationRule.SUM;
        }
    };

    private _aggregateInsightsByWeek = (
        insightsByDay: MetricToDataValues<DailyValue>,
        startDate: Date,
        endDate: Date
    ): TimeScaleToMetricToDataValues[AggregationTimeScale.BY_WEEK] => {
        const weeks = getWeeksFromPeriod(startDate, endDate);
        const metrics = Object.keys(insightsByDay);

        const insightsByWeek: TimeScaleToMetricToDataValues[AggregationTimeScale.BY_WEEK] = {};
        metrics?.forEach((metric) => {
            insightsByWeek[metric as MalouMetric] = new Array(weeks.length).fill(null);
        });

        weeks.forEach((week, index) => {
            for (const metric of metrics) {
                if (metric === MalouMetric.FOLLOWERS) {
                    const followersEndOfWeek =
                        insightsByDay[MalouMetric.FOLLOWERS]?.find((f) => isSameDay(week.endDayInsideRange, new Date(f.date)))?.value ?? 0;
                    if (insightsByWeek[MalouMetric.FOLLOWERS]) {
                        insightsByWeek[MalouMetric.FOLLOWERS][index] = {
                            weekStart: week.start,
                            value: followersEndOfWeek,
                        };
                    }
                    continue;
                }
                const allInsightsForWeek = insightsByDay[metric as MalouMetric]?.filter((i) => isInDayList(i?.date, week.days));
                const insightsAggregated = filterByRequiredKeys(allInsightsForWeek ?? [], ['value'])
                    ?.map((i) => i?.value)
                    .reduce((acc, curr) => (isNumber(curr) || isNumber(acc) ? (acc ?? 0) + (curr ?? 0) : 0), 0);
                insightsByWeek[metric][index] = {
                    weekStart: week.start,
                    value: insightsAggregated,
                };
            }
        });
        return insightsByWeek;
    };

    private _aggregateInsightsByMonth = (
        insightsByDay: MetricToDataValues<DailyValue>,
        startDate: Date,
        endDate: Date
    ): TimeScaleToMetricToDataValues[AggregationTimeScale.BY_MONTH] => {
        const months: Month[] = getMonthsFromPeriod(startDate, endDate);
        const metrics = Object.keys(insightsByDay);

        const insightsByMonth: TimeScaleToMetricToDataValues[AggregationTimeScale.BY_MONTH] = {};
        metrics?.forEach((metric) => {
            insightsByMonth[metric as MalouMetric] = new Array(months.length).fill(null);
        });

        months.forEach((month, index) => {
            for (const metric of metrics) {
                if (metric === MalouMetric.FOLLOWERS) {
                    const endOfMonthInsideRange: Date = month.end > endDate ? endDate : month.end;
                    const followersEndOfMonth =
                        insightsByDay[MalouMetric.FOLLOWERS]?.find((f) => isSameDay(endOfMonthInsideRange, new Date(f.date)))?.value ?? 0;
                    if (insightsByMonth[MalouMetric.FOLLOWERS]) {
                        insightsByMonth[MalouMetric.FOLLOWERS][index] = {
                            monthStart: month.start,
                            value: followersEndOfMonth,
                        };
                    }
                    continue;
                }
                const allInsightsForMonth = insightsByDay[metric as MalouMetric]?.filter((i) => isInDayList(i?.date, month.days));
                const insightsAggregated = filterByRequiredKeys(allInsightsForMonth ?? [], ['value'])
                    ?.map((i) => i?.value)
                    .reduce((acc, curr) => (isNumber(curr) || isNumber(acc) ? (acc ?? 0) + (curr ?? 0) : 0), 0);
                insightsByMonth[metric][index] = {
                    monthStart: month.start,
                    value: insightsAggregated,
                };
            }
        });
        return insightsByMonth;
    };
}
