import { singleton } from 'tsyringe';

import { StoreLocatorOrganizationRestaurantDto } from '@malou-io/package-dto';
import { IRestaurant, PopulateBuilderHelper, toDbId } from '@malou-io/package-models';
import { BusinessCategory, PlatformKey, RELEVANT_ATTRIBUTES, RestaurantAttributeValue } from '@malou-io/package-utils';

import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

type StoreLocatorOrganizationRestaurant = PopulateBuilderHelper<
    IRestaurant,
    [{ path: 'attributeList'; populate: [{ path: 'attribute' }] }]
>;
@singleton()
export class GetStoreLocatorOrganizationRestaurantsUseCase {
    private readonly _DEFAULT_PRIORITY = 99;

    constructor(private readonly _restaurantsRepository: RestaurantsRepository) {}

    async execute(organizationId: string): Promise<StoreLocatorOrganizationRestaurantDto[]> {
        const restaurants = (await this._restaurantsRepository.find({
            filter: {
                organizationId: toDbId(organizationId),
                active: true,
                type: BusinessCategory.LOCAL_BUSINESS,
            },
            projection: {
                name: 1,
                internalName: 1,
                address: 1,
                type: 1,
            },
            options: {
                populate: [
                    {
                        path: 'attributeList',
                        populate: [{ path: 'attribute', select: '_id attributeId attributeName platformKey' }],
                    },
                ],
                lean: true,
            },
        })) as StoreLocatorOrganizationRestaurant[];

        return restaurants.map((restaurant) => this._toDto(restaurant));
    }

    private _toDto(restaurant: StoreLocatorOrganizationRestaurant): StoreLocatorOrganizationRestaurantDto {
        return {
            id: restaurant._id.toString(),
            name: restaurant.name,
            internalName: restaurant.internalName,
            address: restaurant.address || undefined,
            type: restaurant.type,
            attributeList: restaurant.attributeList
                ?.filter(
                    (restaurantAttribute) =>
                        restaurantAttribute.attributeValue === RestaurantAttributeValue.YES &&
                        restaurantAttribute.attribute.platformKey === PlatformKey.GMB &&
                        this._isInRelevantAttributeList(restaurantAttribute.attribute.attributeId)
                )
                .map((restaurantAttribute) => ({
                    id: restaurantAttribute.attribute._id.toString(),
                    priority: this._getAttributePriority(restaurantAttribute.attribute.attributeId),
                    attributeId: restaurantAttribute.attribute.attributeId,
                    platformKey: restaurantAttribute.attribute.platformKey,
                    attributeName: {
                        fr: restaurantAttribute.attribute.attributeName.fr,
                        en: restaurantAttribute.attribute.attributeName.en,
                        es: restaurantAttribute.attribute.attributeName.es,
                        it: restaurantAttribute.attribute.attributeName.it,
                    },
                })),
        };
    }

    private _isInRelevantAttributeList(attributeId: string): boolean {
        return RELEVANT_ATTRIBUTES.some((attribute) => attribute.attributeId === attributeId);
    }

    private _getAttributePriority(attributeId: string): number {
        const attribute = RELEVANT_ATTRIBUTES.find((attr) => attr.attributeId === attributeId);
        return attribute?.priority ?? this._DEFAULT_PRIORITY;
    }
}
