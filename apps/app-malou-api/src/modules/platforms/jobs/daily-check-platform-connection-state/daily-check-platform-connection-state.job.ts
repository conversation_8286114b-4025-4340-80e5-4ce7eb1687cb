import { singleton } from 'tsyringe';

import { IPlatform } from '@malou-io/package-models';
import { PlatformKey, waitFor } from '@malou-io/package-utils';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import { GetAccountPermissionsStatusUseCase } from ':modules/credentials/use-cases/get-account-permissions-status.use-case';
import { CreatePlatformDisconnectedNotificationUseCase } from ':modules/notifications/use-cases/create-platform-disconnected-notification/create-platform-disconnected-notification.use-case';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { MetricsService } from ':services/metrics.service';

const platformConnectionDisconnectedNotificationCounter = new MetricsService().getMeter().createCounter<{
    platformIds: string[];
    status: 'success' | 'failure';
}>(`platform.disconnected.notification.count`, {
    description: 'Number of issues related to platform connection notification creation',
});

@singleton()
export class DailyCheckPlatformConnectionStateJob extends GenericJobDefinition {
    constructor(
        private readonly _createPlatformDisconnectedNotificationUseCase: CreatePlatformDisconnectedNotificationUseCase,
        private readonly _getAccountPermissionsStatusUseCase: GetAccountPermissionsStatusUseCase,
        private readonly _platformsRepository: PlatformsRepository
    ) {
        super({
            agendaJobName: AgendaJobName.DAILY_CHECK_PLATFORM_CONNECTION_STATE,
            shouldAwaitExecution: false,
        });
    }

    async executeJob(): Promise<void> {
        type PlatformWithRestaurant = IPlatform & {
            restaurant: {
                _id: string;
                name: string;
                internalName: string;
                active: boolean;
            };
        };

        const platforms = await this._platformsRepository
            .aggregate<{
                deliveroo: PlatformWithRestaurant[];
                other: PlatformWithRestaurant[];
            }>(
                [
                    {
                        $match: {
                            // Facebook and insta are already covered via webhooks and all the api calls they make, check fb provider
                            key: { $nin: [PlatformKey.INSTAGRAM, PlatformKey.FACEBOOK] },
                            'credentials.0': { $exists: true },
                        },
                    },
                    {
                        $lookup: {
                            from: 'restaurants',
                            localField: 'restaurantId',
                            foreignField: '_id',
                            as: 'restaurant',
                            pipeline: [{ $project: { _id: 1, active: 1, internalName: 1, name: 1 } }],
                        },
                    },
                    { $unwind: '$restaurant' },
                    { $match: { 'restaurant.active': true } },
                    {
                        // extract platforms with key deliveroo separately
                        $facet: {
                            deliveroo: [{ $match: { key: PlatformKey.DELIVEROO } }],
                            other: [{ $match: { key: { $ne: PlatformKey.DELIVEROO } } }],
                        },
                    },
                ],
                {
                    readPreference: 'secondary',
                }
            )
            .then((res) => res[0]);

        // takes approximately 20-30mins to complete, 0.5 sec per platform x 3000 platforms more or less..
        const disconnectedPlatforms: { platform: { id: string; key: PlatformKey }; restaurant: { id: string; name: string } }[] = [];
        const disconnectedDeliverooPlatforms: { platform: { id: string; key: PlatformKey }; restaurant: { id: string; name: string } }[] =
            [];
        try {
            for (const platform of platforms?.other ?? []) {
                try {
                    const res = await this._getAccountPermissionsStatusUseCase.execute(new Platform(platform));
                    if (!res.isValid) {
                        disconnectedPlatforms.push({
                            platform: { id: platform._id.toString(), key: platform.key },
                            restaurant: {
                                id: platform.restaurantId.toString(),
                                name: platform.restaurant.internalName || platform.restaurant.name,
                            },
                        });
                    }
                } catch (error) {
                    logger.error(`Error checking platform connection state for platform ${platform._id}`, error);
                }
                // this is to avoid rate limiting
                await waitFor(200);
            }
            await this._createPlatformDisconnectedNotificationUseCase.execute({ disconnectedPlatforms });

            /* We separate deliveroo platforms from the rest to avoid rate limiting by having a higher delay between requests */

            for (const platform of platforms?.deliveroo ?? []) {
                try {
                    const res = await this._getAccountPermissionsStatusUseCase.execute(new Platform(platform));
                    if (!res.isValid) {
                        disconnectedDeliverooPlatforms.push({
                            platform: { id: platform._id.toString(), key: platform.key },
                            restaurant: {
                                id: platform.restaurantId.toString(),
                                name: platform.restaurant.internalName ?? platform.restaurant.name,
                            },
                        });
                    }
                } catch (error) {
                    logger.error(`Error checking platform connection state for platform ${platform._id}`, error);
                }
                // this is to avoid rate limiting
                await waitFor(2000);
            }
            await this._createPlatformDisconnectedNotificationUseCase.execute({ disconnectedPlatforms: disconnectedDeliverooPlatforms });

            logger.info('DailyCheckPlatformConnectionStateJob finished');
            platformConnectionDisconnectedNotificationCounter.add(1, {
                platformIds: disconnectedPlatforms.concat(disconnectedDeliverooPlatforms).map((p) => p.platform.id),
                status: 'success',
            });
        } catch (error) {
            logger.info('DailyCheckPlatformConnectionStateJob finished');
            platformConnectionDisconnectedNotificationCounter.add(1, {
                platformIds: disconnectedPlatforms.concat(disconnectedDeliverooPlatforms).map((p) => p.platform.id),
                status: 'failure',
            });
        }
    }
}
