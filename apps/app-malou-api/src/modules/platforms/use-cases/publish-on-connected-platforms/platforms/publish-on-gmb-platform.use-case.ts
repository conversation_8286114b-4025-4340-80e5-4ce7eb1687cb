import { mybusinessbusinessinformation_v1 } from '@googleapis/mybusinessbusinessinformation';
import { GaxiosError } from 'gaxios';
import { omit, pick } from 'lodash';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { IMedia, toDbId } from '@malou-io/package-models';
import {
    exponentialBackoffDelay,
    GmbAttributesEnum,
    GmbErrorCode,
    GmbOpeningStatus,
    InvalidPlatformReason,
    isNotNil,
    MalouErrorCode,
    MediaCategory,
    PlatformKey,
    waitFor,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { GetGmbAccountPermissionsStatusService } from ':modules/credentials/platforms/gmb/services/get-gmb-account-permissions-status.service';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { GmbMapper } from ':modules/platforms/platforms/gmb/gmb-mapper';
import { GmbAttribute, GmbLocation, GmbMediaCategory, GmbMediaFormat, GmbMediaItemInput } from ':modules/platforms/platforms/gmb/gmb.types';
import { GmbHandlePlaceActionLinksService } from ':modules/platforms/platforms/gmb/services/gmb-handle-place-action-links.service';
import {
    PublishOnPlatformResponse,
    RestaurantPopulatedToPublish,
} from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { GmbBusinessInformationProvider } from ':providers/google/gmb.business-information.provider';
import { GmbApiProvider } from ':providers/google/gmb.provider';
import { GmbRefreshTokenService } from ':services/credentials/gmb/gmb-refresh-token.service';

type GmbMappedData = Partial<
    GmbLocation & {
        attributes: GmbAttribute[];
    }
>;

@singleton()
export class PublishOnGmbPlatformUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _gmbRefreshTokenService: GmbRefreshTokenService,
        private readonly _gmbHandlePlaceActionLinksService: GmbHandlePlaceActionLinksService,
        private readonly _gmbBusinessInformationProvider: GmbBusinessInformationProvider,
        private readonly _gmbApiProvider: GmbApiProvider,
        private readonly _getGmbAccountPermissionsStatusService: GetGmbAccountPermissionsStatusService,
        private readonly _gmbMapper: GmbMapper
    ) {}

    async execute({
        restaurant,
        keysToUpdate,
        options,
    }: {
        restaurant: RestaurantPopulatedToPublish;
        keysToUpdate: (keyof RestaurantPopulatedToPublish)[];
        options?: { validateOnly?: boolean };
    }): Promise<PublishOnPlatformResponse> {
        const restaurantId = restaurant._id.toString();
        const restaurantToUpdate = pick(restaurant, [...keysToUpdate, '_id']);
        const validateOnly = !!options?.validateOnly;

        try {
            // Check for platform availability
            const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.GMB);

            if (!platform) {
                logger.warn('[GMB PUBLISH] Platform not found', { restaurantId });
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND);
            }

            const { credentials, apiEndpointV2, apiEndpoint } = platform;
            const credentialId = credentials?.[0];
            if (!credentialId || !apiEndpointV2 || !apiEndpoint) {
                logger.warn('[GMB PUBLISH] No platform credential', { restaurantId, platformId: platform._id });
                throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
            }
            const locationId = apiEndpointV2.replace('locations/', '');

            // Check that gmb location is in a right state
            const { isValid, missing, miscellaneous } = await this._getGmbAccountPermissionsStatusService.execute(platform);
            if (!isValid) {
                logger.error('[GMB PUBLISH] Platform not active', { restaurantId, locationId, missing, miscellaneous });

                return {
                    success: false,
                    statusCode: 500,
                    errors: [
                        {
                            field: 'platform',
                            reason: miscellaneous || InvalidPlatformReason.GMB_DISCONNECTED,
                        },
                    ],
                };
            }

            const locationData = this._cleanLocationData(restaurantToUpdate);
            const mappedData = await this._gmbMapper.toPlatformMapper(locationData, restaurantId.toString());

            logger.info('[GMB PUBLISH] Publishing on GMB', {
                restaurantId,
                locationId,
                updatedData: mappedData,
            });

            const errors: {
                field: string;
                reason: string;
            }[] = [];

            // validateOnly option doesn't exist for action links so we simply skip the update in validateOnly mode
            if (!validateOnly) {
                const { failedProperties: failedPropertiesForActionLinks } = await this._gmbHandlePlaceActionLinksService.execute({
                    credentialId,
                    locationId,
                    data: locationData,
                });
                errors.push(...failedPropertiesForActionLinks);
            }

            // validateOnly option doesn't exist for attributes patch method so we simply skip the update in validateOnly mode
            if ((mappedData.attributes?.length ?? 0) > 0 && !validateOnly) {
                errors.push(
                    ...(await this._publishAttributes({
                        restaurantId,
                        credentialId,
                        locationId,
                        mappedData,
                    }))
                );

                delete mappedData.attributes;
            }

            if (mappedData.openInfo) {
                const {
                    openInfo: { openingDate, status },
                } = mappedData;

                // For some reason, GMB doesn't allow to update openInfo.status and openInfo.openingDate at the same time, so we handle them separately
                mappedData['openInfo.status'] = {
                    status: status || (platform.isClosedTemporarily ? GmbOpeningStatus.CLOSED_TEMPORARILY : GmbOpeningStatus.OPEN),
                };
                if (openingDate) {
                    mappedData['openInfo.openingDate'] = { openingDate };
                }
                delete mappedData.openInfo;
            }

            // Update remaining infos
            const { primaryMappedData, secondaryMappedData } = this._setPrimaryAndSecondaryDataToUpdate({
                mappedData,
            });

            // Publish primary fields first
            errors.push(
                ...(
                    await Promise.all(
                        Object.keys(primaryMappedData).map(async (field) =>
                            this._publishField({
                                restaurantId,
                                credentialId,
                                locationId,
                                mappedData,
                                field,
                                validateOnly,
                            })
                        )
                    )
                ).filter(isNotNil)
            );

            // Publish secondary fields
            errors.push(
                ...(
                    await Promise.all(
                        Object.keys(secondaryMappedData).map(async (field) =>
                            this._publishField({
                                restaurantId,
                                credentialId,
                                locationId,
                                mappedData,
                                field,
                                validateOnly,
                            })
                        )
                    )
                ).filter(isNotNil)
            );

            const promises: Promise<{ field: string; reason: string } | undefined>[] = [];
            if (!validateOnly && restaurant.logoChanged && restaurant.logo) {
                promises.push(
                    this._updateRestaurantMedia({
                        restaurantId,
                        credentialId,
                        locationId,
                        endpoint: apiEndpoint,
                        media: restaurant.logo,
                        field: 'logo',
                    })
                );
            }

            if (!validateOnly && restaurant.coverChanged && restaurant.cover) {
                promises.push(
                    this._updateRestaurantMedia({
                        restaurantId,
                        credentialId,
                        locationId,
                        endpoint: apiEndpoint,
                        media: restaurant.cover,
                        field: 'cover',
                    })
                );
            }
            if (promises.length > 0) {
                errors.push(...(await Promise.all(promises)).filter(isNotNil));
            }

            const cleanedErrors = errors.filter(isNotNil);
            logger.info('[GMB PUBLISH] Update done', {
                restaurantId,
                locationId,
                data: mappedData,
                errors: cleanedErrors,
            });

            return {
                success: cleanedErrors.length === 0,
                statusCode: cleanedErrors.length === 0 ? 200 : 500,
                errors: cleanedErrors,
            };
        } catch (e: any) {
            const realError = e?.response?.data?.error ?? e;
            logger.error('[GMB PUBLISH] Error publishing infos on GMB', {
                error: realError,
                restaurantId,
                data: restaurantToUpdate,
                ...(e.malouErrorCode && { malouErrorCode: e.malouErrorCode }),
            });

            return {
                success: false,
                statusCode: 500,
                message: realError.message,
                errors: keysToUpdate.map((field) => ({
                    field,
                    reason: '',
                })),
                ...(e.malouErrorCode && { malouErrorCode: e.malouErrorCode }),
            };
        }
    }

    private async _publishAttributes({
        restaurantId,
        credentialId,
        locationId,
        mappedData,
    }: {
        restaurantId: string;
        credentialId: string;
        locationId: string;
        mappedData: GmbMappedData;
    }): Promise<
        {
            field: string;
            reason: string;
        }[]
    > {
        const errors: { field: string; reason: string }[] = [];

        try {
            const { attributes, mask: attributeMask } = await this._getCleanedAttributes({
                credentialId,
                locationId,
                attributes: mappedData.attributes ?? [],
            });

            if (attributes?.length === 0) {
                logger.info('[GMB PUBLISH] [ATTRIBUTES] No attributes to update', {
                    restaurantId,
                    locationId,
                    originalAttributes: mappedData.attributes,
                });

                return errors;
            }

            logger.info('[GMB PUBLISH] [ATTRIBUTES] About to update attributes', {
                restaurantId,
                locationId,
                attributeMask,
                attributes,
                originalAttributes: mappedData.attributes,
            });

            const credential = await this._gmbRefreshTokenService.getFreshTokenIfNecessary(credentialId);
            await this._patchLocationAttributesAndRetryIfNeeded({
                accessToken: credential.accessToken,
                locationId,
                attributeMask,
                attributes,
            });

            logger.info('[GMB PUBLISH] [ATTRIBUTES] Updated attributes', {
                restaurantId,
                locationId,
                attributeMask,
                attributes,
                originalAttributes: mappedData.attributes,
            });
        } catch (err: any) {
            logger.error('[GMB PUBLISH] [ATTRIBUTES] Error updating attributes', {
                error: err?.response?.data?.error ?? err,
                restaurantId,
                originalAttributes: mappedData.attributes,
            });

            if (err?.response?.data?.error?.details?.length > 0) {
                errors.push(
                    ...err.response.data.error.details.map((detail) => ({
                        // field_mask is empty when checking attributes
                        field: detail.metadata?.field_mask || 'attributes',
                        reason: detail.reason,
                    }))
                );
            } else {
                errors.push({
                    field: 'attributes',
                    reason: err?.response?.data?.error?.message || err.message,
                });
            }
        }

        return errors;
    }

    private async _getCleanedAttributes({
        credentialId,
        locationId,
        attributes,
    }: {
        credentialId: string;
        locationId: string;
        attributes: GmbAttribute[];
    }): Promise<{ attributes: GmbAttribute[]; mask: string }> {
        const { accessToken } = await this._gmbRefreshTokenService.getFreshTokenIfNecessary(credentialId);
        const { data: authorizedAttributesForCategory } = await this._gmbBusinessInformationProvider.listAvailableAttributes({
            accessToken,
            locationId,
        });
        const authorizedAttributesNames = authorizedAttributesForCategory.attributeMetadata?.map((att) => att.parent);

        const authorizedAttributes = attributes.filter((attribute) => authorizedAttributesNames?.includes(attribute.name));
        const attributeMask = authorizedAttributes.map((attribute) => attribute.name).join(',');

        // Remove undefined social networks or empty urls (orderUrl, reservationUrl, ...)
        // mask is defined before because we want to keep attribute names in mask with empty values to delete them
        const cleanedAttributes = authorizedAttributes.filter((attribute) => !attribute.uriValues || attribute.uriValues.length > 0);

        return {
            attributes: cleanedAttributes,
            mask: attributeMask,
        };
    }

    private async _publishField({
        restaurantId,
        credentialId,
        locationId,
        mappedData,
        field,
        validateOnly,
    }: {
        restaurantId: string;
        credentialId: string;
        locationId: string;
        mappedData: GmbMappedData;
        field: string;
        validateOnly: boolean;
    }): Promise<
        | {
              field: string;
              reason: string;
          }
        | undefined
    > {
        const malouKey = Object.values(this._gmbMapper.mappingConfiguration).find((item) => item.gmbKey === field)?.malouKey || field;

        try {
            logger.info('[GMB PUBLISH] About to update info', {
                restaurantId,
                locationId,
                updateMask: field,
                malouKey,
                data: mappedData[field],
            });

            const gmbField = this._getOriginalGmbField(field) || field;

            const { accessToken } = await this._gmbRefreshTokenService.getFreshTokenIfNecessary(credentialId);
            await this._patchLocationAndRetryIfNeeded({
                accessToken,
                locationId,
                updateMask: field,
                bodyData: {
                    [gmbField]: mappedData[field],
                },
                validateOnly,
            });

            logger.info('[GMB PUBLISH] Updated info', {
                restaurantId,
                locationId,
                updateMask: field,
                malouKey,
                data: mappedData[field],
            });

            return undefined;
        } catch (err: any) {
            logger.error('[GMB PUBLISH] Error updating info', {
                error: err?.response?.data?.error ?? err,
                restaurantId,
                field,
                data: mappedData[field],
            });

            return this._handleGmbError(err, malouKey);
        }
    }

    private _setPrimaryAndSecondaryDataToUpdate({ mappedData }: { mappedData: GmbMappedData }): {
        primaryMappedData: GmbMappedData;
        secondaryMappedData: GmbMappedData;
    } {
        const fieldsToDelay: string[] = [];

        // If both regularHours and specialHours are updated, we need to split the update, and update regular hours first
        if (mappedData.regularHours && mappedData.specialHours) {
            fieldsToDelay.push('specialHours');
        }

        return {
            primaryMappedData: omit(mappedData, fieldsToDelay),
            secondaryMappedData: pick(mappedData, fieldsToDelay),
        };
    }

    private _handleGmbError(
        err,
        field
    ):
        | {
              field: string;
              reason: string;
          }
        | undefined {
        // If gmb returns a 409 error, it means that the location is still being updated with this value. User has attempted too many updates with a different value
        // For some reason, the field seems to be updated on GMB despite the error. So we don't store it but we log it to monitor it
        if (err?.status === 409) {
            return undefined;
        }

        // Validation errors
        const gmbErrorCode: GmbErrorCode = err?.response?.data?.error?.details?.[0]?.reason;
        if (gmbErrorCode) {
            return {
                field,
                reason: gmbErrorCode,
            };
        }

        return {
            field,
            reason: err?.response?.data?.error?.message || err.message,
        };
    }

    private _getOriginalGmbField(field: string): string {
        if (['openInfo.status', 'openInfo.openingDate'].includes(field)) {
            return 'openInfo';
        }

        return field;
    }

    private _cleanLocationData(restaurant: Partial<RestaurantPopulatedToPublish>): Partial<RestaurantPopulatedToPublish> {
        const { specialHours, ...locationData } = restaurant;
        const futureSpecialHours = specialHours?.filter(
            (specialHour) =>
                DateTime.now().startOf('day') <=
                DateTime.fromObject({ ...specialHour.endDate, month: specialHour.endDate.month + 1 }).startOf('day')
        ); // We add 1 to the endDate month because Luxon first month is 1 and our first month is 0

        return { ...locationData, specialHours: futureSpecialHours };
    }

    private async _patchLocationAttributesAndRetryIfNeeded(
        {
            accessToken,
            locationId,
            attributeMask,
            attributes,
        }: {
            accessToken: string;
            locationId: string;
            attributeMask: string;
            attributes: GmbAttribute[];
        },
        retryCount = 0
    ): Promise<void> {
        const MAX_RETRIES = 3;

        try {
            await this._gmbBusinessInformationProvider.patchLocationAttributes({
                accessToken,
                locationId,
                attributeMask,
                body: {
                    attributes,
                },
            });
        } catch (error: any) {
            const gmbErrorCode: GmbErrorCode = error?.response?.data?.error?.details?.[0]?.reason;
            if (gmbErrorCode === GmbErrorCode.ATTRIBUTE_PROVIDER_URL_NOT_ALLOWED) {
                logger.warn('[GMB PUBLISH] Error publishing attributes on GMB due to provider URL not allowed', {
                    error: error?.response?.data?.error ?? error,
                    locationId,
                    attributeMask,
                    attributes,
                });

                const failingAttributeName = error?.response?.data?.error?.details?.[0]?.metadata?.attribute_name;
                if (
                    [`attributes/${GmbAttributesEnum.URL_RESERVATIONS}`, `attributes/${GmbAttributesEnum.URL_ORDER_AHEAD}`].includes(
                        failingAttributeName
                    )
                ) {
                    const filteredAttributes = attributes.filter((attribute) => attribute.name !== failingAttributeName);
                    return await this._patchLocationAttributesAndRetryIfNeeded({
                        accessToken,
                        locationId,
                        // We keep the same attributeMask to delete the failing attribute
                        attributeMask,
                        attributes: filteredAttributes,
                    });
                }
            }

            if (retryCount < MAX_RETRIES && this._shouldRetryOnError(error)) {
                const realError = error?.response?.data?.error ?? error;
                logger.error('[GMB PUBLISH] Error publishing attributes on GMB but will retry', {
                    error: realError,
                    locationId,
                    attributeMask,
                    attributes,
                    retryCount,
                });
                const delay = exponentialBackoffDelay(retryCount); // arbitrary and exponential delay to not overload the API but still retry quickly
                await waitFor(delay);
                await this._patchLocationAttributesAndRetryIfNeeded({ accessToken, locationId, attributeMask, attributes }, retryCount + 1);
            } else {
                throw error;
            }
        }
    }

    private async _patchLocationAndRetryIfNeeded(
        {
            accessToken,
            locationId,
            updateMask,
            bodyData,
            validateOnly,
        }: {
            accessToken: string;
            locationId: string;
            updateMask: string;
            bodyData: Omit<mybusinessbusinessinformation_v1.Schema$Location, 'name'>;
            validateOnly: boolean;
        },
        retryCount = 0
    ): Promise<void> {
        const MAX_RETRIES = 3;

        try {
            await this._gmbBusinessInformationProvider.patchLocation({
                accessToken,
                locationId,
                updateMask,
                body: bodyData,
                validateOnly,
            });
        } catch (error: any) {
            if (retryCount < MAX_RETRIES && this._shouldRetryOnError(error) && !validateOnly) {
                const realError = error?.response?.data?.error ?? error;
                logger.error('[GMB PUBLISH] Error publishing data on GMB but will retry', {
                    error: realError,
                    locationId,
                    updateMask,
                    bodyData,
                    retryCount,
                    validateOnly,
                });
                const delay = exponentialBackoffDelay(retryCount); // arbitrary and exponential delay to not overload the API but still retry quickly
                await waitFor(delay);
                await this._patchLocationAndRetryIfNeeded(
                    {
                        accessToken,
                        locationId,
                        updateMask,
                        bodyData,
                        validateOnly,
                    },
                    retryCount + 1
                );
            } else {
                throw error;
            }
        }
    }

    private async _updateRestaurantMedia({
        restaurantId,
        credentialId,
        locationId,
        endpoint,
        media,
        field,
    }: {
        restaurantId: string;
        credentialId: string;
        locationId: string;
        endpoint: string;
        media: IMedia;
        field: 'logo' | 'cover';
    }): Promise<
        | {
              field: string;
              reason: string;
          }
        | undefined
    > {
        try {
            logger.info(`[GMB PUBLISH] [MEDIA] About to update ${field}`, {
                restaurantId,
                locationId,
                media,
            });

            const { accessToken } = await this._gmbRefreshTokenService.getFreshTokenIfNecessary(credentialId);
            const gmbMedia = this._getGmbMediaProperties(media);
            await this._updateRestaurantMediaAndRetryIfNeeded({
                accessToken,
                locationId,
                media: gmbMedia,
                field,
                endpoint,
            });
            await this._restaurantsRepository.findOneAndUpdate({
                filter: { _id: toDbId(restaurantId) },
                update: { [`${field}Changed`]: false },
            });

            logger.info(`[GMB PUBLISH] [MEDIA] Updated ${field}`, {
                restaurantId,
                locationId,
                media,
            });

            return undefined;
        } catch (err) {
            logger.error(`[GMB PUBLISH] [MEDIA] Error updating ${field}`, {
                restaurantId,
                locationId,
                media,
                err,
            });

            return this._handleGmbError(err, field);
        }
    }

    private async _updateRestaurantMediaAndRetryIfNeeded(
        {
            accessToken,
            locationId,
            media,
            field,
            endpoint,
        }: {
            accessToken: string;
            locationId: string;
            field: string;
            media: Required<GmbMediaItemInput>;
            endpoint: string;
        },
        retryCount = 0
    ): Promise<void> {
        const MAX_RETRIES = 3;

        try {
            await this._gmbApiProvider.createLocationMedia({ accessToken, endpoint, body: media });
        } catch (error: any) {
            if (retryCount < MAX_RETRIES && this._shouldRetryOnError(error)) {
                const realError = error?.response?.data?.error ?? error;
                logger.error(`[GMB PUBLISH] [MEDIA] Error updating ${field} but will retry`, {
                    error: realError,
                    locationId,
                    field,
                    media,
                    retryCount,
                });

                const delay = exponentialBackoffDelay(retryCount); // arbitrary and exponential delay to not overload the API but still retry quickly
                await waitFor(delay);
                await this._updateRestaurantMediaAndRetryIfNeeded(
                    {
                        accessToken,
                        locationId,
                        field,
                        media,
                        endpoint,
                    },
                    retryCount + 1
                );
            } else {
                throw error;
            }
        }
    }

    private _getGmbMediaProperties(media: IMedia): Required<GmbMediaItemInput> {
        return {
            description: media.title || media.aiDescription || media.name || '',
            mediaFormat: GmbMediaFormat[media.type?.toUpperCase()] || GmbMediaFormat.MEDIA_FORMAT_UNSPECIFIED,
            locationAssociation: {
                category: GmbMediaCategory[media.category.toUpperCase()] || GmbMediaCategory.ADDITIONAL,
            },
            sourceUrl:
                media.category === MediaCategory.COVER
                    ? media.urls.cover || ''
                    : media.urls.original || media.urls.igFit || media.urls.small || '',
        };
    }

    private _shouldRetryOnError(error: GaxiosError | undefined): boolean {
        // Code 409: conflict, or request aborted
        // Code 429: Resource has been exhausted (e.g. check quota)
        // Code 503: service unavailable
        const errorCode = error?.code ? parseInt(error.code, 10) : 0;
        return [409, 429, 500, 503].includes(errorCode);
    }
}
