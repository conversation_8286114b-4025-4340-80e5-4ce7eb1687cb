import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { errorReplacer, MalouErrorCode, PlatformKey, StoredInDBInsightsMetric } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { GmbApiProviderUseCases } from ':modules/credentials/platforms/gmb/gmb.use-cases';
import PlatformInsightsRepo from ':modules/platform-insights/platform-insights.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { GmbMapper } from ':modules/platforms/platforms/gmb/gmb-mapper';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

const restaurantsRepository = container.resolve(RestaurantsRepository);
const platformInsightsRepository = container.resolve(PlatformInsightsRepo);
const gmbCredentialsUseCases = container.resolve(GmbApiProviderUseCases);
const platformsRepository = container.resolve(PlatformsRepository);
const gmbMapper = container.resolve(GmbMapper);

export const getOverviewData = async function ({ restaurantId }: { restaurantId: string }) {
    const platform = await platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.GMB);

    const credentialId = platform?.credentials?.[0];
    const apiEndpointV2 = platform?.apiEndpointV2;
    const apiEndpoint = platform?.apiEndpoint;
    if (!credentialId || !apiEndpointV2 || !apiEndpoint) {
        throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
    }
    const gmbData = await gmbCredentialsUseCases.getLocation(credentialId, apiEndpointV2);

    const { attributes } = (await gmbCredentialsUseCases.getLocationAttributes(credentialId, apiEndpointV2))?.data;
    if (!gmbData || !gmbData.data) {
        return { error: true, message: MalouErrorCode.PLATFORM_NO_DATA_IN_RESPONSE };
    }
    const { averageRating } = await gmbCredentialsUseCases.fetchLocationReviews(credentialId, apiEndpoint);
    return { ...gmbData.data, attributes, rating: averageRating };
};

/**
 * @param {Object} data
 * @param {string} data.credentialId
 * @param {string} data.apiEndpointV2
 */
export const getLocationData = async ({ credentialId, apiEndpointV2 }) => {
    const [location, locationAttributes] = await Promise.all([
        gmbCredentialsUseCases.getLocation(credentialId, apiEndpointV2),
        gmbCredentialsUseCases.getLocationAttributes(credentialId, apiEndpointV2),
    ]);

    return {
        ...location?.data,
        attributes: locationAttributes?.data?.attributes ?? [],
    };
};

export const getSocialId = async function (restaurantId: string) {
    const restaurant = await restaurantsRepository.findOne({ filter: { _id: toDbId(restaurantId) }, options: { lean: true } });
    if (!restaurant) {
        return { error: true, message: MalouErrorCode.RESTAURANT_NOT_FOUND };
    }

    if (!restaurant.placeId) {
        return { error: true, message: MalouErrorCode.PLATFORM_MISSING_LOCATION_ID };
    }

    return restaurant.placeId;
};

/**
 *
 * @param {Object} platformData the platformData
 * @returns {Promise<Object>} malou Platform object
 */
export const mapOverviewDataToMalou = async function (platformData) {
    try {
        const malouData = await gmbMapper.toMalouMapper(platformData);
        return malouData;
    } catch (e) {
        return { error: true, message: e, errorData: JSON.stringify(e, errorReplacer) };
    }
};

export const upsertLinkedComponents = async ({ platform }) => {
    const today = DateTime.local();
    const { year, month, day } = today;
    const { rating } = platform;
    if (rating !== undefined && rating !== null) {
        const filter = {
            socialId: platform.socialId,
            platformKey: platform.key,
            metric: StoredInDBInsightsMetric.PLATFORM_RATING,
            year,
            month: month - 1,
            day,
        };
        const insight = {
            ...filter,
            value: rating,
            date: today.toJSDate(),
        };
        try {
            await platformInsightsRepository.upsert({ filter, update: insight });
        } catch (e: any) {
            if (e.code !== 11000) {
                throw e;
            }
        }
    }
};
