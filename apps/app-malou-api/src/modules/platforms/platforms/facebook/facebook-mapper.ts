import phoneUtil from 'google-libphonenumber';
import { isNil, omit, omitBy, uniq } from 'lodash';
import { container } from 'tsyringe';

import { DbId, IPlatform, IRestaurant } from '@malou-io/package-models';
import {
    BusinessCategory,
    COUNTRIES,
    Day,
    descriptionSize,
    FacebookPageCategory,
    filterByRequiredKeys,
    isNotNil,
    Langs,
    MalouErrorCode,
    PlatformKey,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import CategoriesRepository from ':modules/categories/categories.repository';
import { AddOrUpdateCategoriesService } from ':modules/categories/services/add-or-update-category.service';
import { FacebookCategoryIdEnum, GmbCategoryIdEnum } from ':modules/categories/types';
import { FacebookCityTypes, FacebookPageInfo, FacebookTemporaryStatus } from ':modules/credentials/platforms/facebook/facebook.types';
import { citySearch } from ':modules/credentials/platforms/facebook/facebook.use-cases';
import { invalidFacebookCategoryForActionLinks } from ':modules/information-updates/constants';
import { gmbToFacebookCategoriesMapping } from ':modules/platforms/platforms/facebook/gmb-to-fb-categories.mapping';
import { RestaurantPopulatedToPublish } from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import { MalouRestaurantSearchResult } from ':modules/platforms/use-cases/search-social-ids/search-social-ids.interface';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

/**
 * Mapper implementation for Facebook
 * https://developers.facebook.com/docs/graph-api/reference/page/#Updating
 */

const restaurantsRepository = container.resolve(RestaurantsRepository);
const categoriesRepository = container.resolve(CategoriesRepository);

export class FacebookMapper {
    supportedFields: string[] = [];
    private static readonly _MAX_CATEGORIES_COUNT: number = 3;

    constructor(private _isBrandPage = false) {
        this.setSupportedFields();
    }

    setSupportedFields() {
        this.supportedFields = Object.keys(FacebookMapper.mappingConfiguration);

        if (this._isBrandPage) {
            this.supportedFields = this.supportedFields.filter((f) => f !== 'address');
        }
    }

    getPlatformFields() {
        return this.supportedFields;
    }

    getPlatformFieldName(malouField) {
        if (!this.isSupportedField(malouField)) {
            return;
        }

        return FacebookMapper.mappingConfiguration[malouField].facebookKey;
    }

    /**
     * Returns true if field is supported by our platform implementation
     * @param {*} field
     */
    isSupportedField(field: string): boolean {
        return this.supportedFields.includes(field);
    }

    async toPlatformMapper(malouData: Partial<RestaurantPopulatedToPublish> & { _id: DbId }): Promise<Partial<FacebookPageInfo>> {
        const res: Partial<FacebookPageInfo> = {};

        await Promise.all(
            Object.keys(malouData)
                .filter(
                    (key) =>
                        this.isSupportedField(key) &&
                        FacebookMapper.mappingConfiguration[key].shouldBePublished !== false &&
                        isNotNil(this.getFacebookFieldName(key))
                )
                .map(async (key) => {
                    const mappedData = isNotNil(FacebookMapper.mappingConfiguration[key]?.toFacebookFormat)
                        ? await FacebookMapper.mappingConfiguration[key].toFacebookFormat(malouData)
                        : malouData[key];

                    if (isNotNil(mappedData)) {
                        const index = this.getFacebookFieldName(key);
                        if (index) {
                            res[index] = mappedData;
                        }
                    }
                })
        );

        return res;
    }

    getDataForUpdate(facebookData: Partial<FacebookPageInfo>): Partial<FacebookPageInfo> {
        const cleanedData = omitBy(facebookData, isNil) as Partial<FacebookPageInfo>;

        // If no hours, remove temporary_status
        // To prevent error: (#100) temporary_status is not applicable for this Page because it has no hours or opening information
        if (!cleanedData.hours && cleanedData.temporary_status) {
            delete cleanedData.temporary_status;
        }

        // remove delivery_and_pickup_option_info if category is invalid because of "(#100) can only update DeliveryAndPickupOptionInfo for Restarurant pages"
        if (invalidFacebookCategoryForActionLinks.includes(cleanedData.category_list?.[0] as FacebookCategoryIdEnum)) {
            delete cleanedData.delivery_and_pickup_option_info;
        }

        return cleanedData;
    }

    toPlatformLogo(malouCover) {
        const {
            urls: { original: url },
        } = malouCover;

        return {
            picture: url,
        };
    }

    async toMalouMapper(facebookData, restaurantId: string | undefined): Promise<IPlatform> {
        const platformData = {};

        for (const key of this.supportedFields ?? []) {
            const mappingConfiguration: any = Object.values(FacebookMapper.mappingConfiguration).find((el) => el.malouKey === key);

            platformData[mappingConfiguration?.malouKey] = isNotNil(mappingConfiguration?.toMalouFormat)
                ? await mappingConfiguration?.toMalouFormat(facebookData, restaurantId)
                : facebookData[key];
        }

        return platformData as IPlatform;
    }

    toMalouMapperSearch(searchResults): MalouRestaurantSearchResult[] {
        if (!searchResults || !Array.isArray(searchResults)) {
            throw new MalouError(MalouErrorCode.PLATFORM_SEARCH_RESULT_NOT_ARRAY, { message: 'Facebook' });
        }
        return searchResults
            .map((r) => {
                if (!r.id) {
                    return null;
                }
                let fullName = r.name || null;
                if (fullName && r.store_location_descriptor) {
                    fullName += ` (${r.store_location_descriptor})`;
                }

                return {
                    socialId: r.id,
                    name: fullName || '',
                    formattedAddress: [r.location?.street, r.location?.zip, r.location?.city].filter((l) => !!l)?.join(', ') || undefined,
                    socialUrl: r.link || undefined,
                    picture: r.picture?.data?.url || undefined,
                    rating: r.overall_star_rating || undefined,
                    pageCategory: FacebookMapper.getFacebookPageCategory(r),
                    parentSocialId: r.parent_page?.id,
                    hasTransitionedToNewPageExperience: r.has_transitioned_to_new_page_experience,
                };
            })
            .filter(isNotNil);
    }

    getFacebookFieldName(malouField) {
        if (!this.isSupportedField(malouField)) {
            return;
        }

        return FacebookMapper.mappingConfiguration[malouField]?.facebookKey;
    }

    // ---- reference ----- https://developers.facebook.com/docs/graph-api/reference/page/#Updating
    static get mappingConfiguration(): Record<
        string,
        {
            malouKey: string;
            facebookKey?: string;
            shouldBePublished?: boolean;
            toMalouFormat?: (data: any, restaurantId?: string) => any;
            toFacebookFormat?: (data: Partial<RestaurantPopulatedToPublish> & { _id: DbId }) => any;
        }
    > {
        const addOrUpdateCategoriesService = container.resolve(AddOrUpdateCategoriesService);
        return {
            name: {
                malouKey: 'name',
                facebookKey: 'name',
                shouldBePublished: false,
            },
            category: {
                malouKey: 'category',
                toMalouFormat: async (data, restaurantId: string | undefined) => {
                    if (!data.category || !data.category_list) {
                        throw new MalouError(MalouErrorCode.PLATFORM_CATEGORY_NOT_FOUND, { message: 'Facebook' });
                    }

                    try {
                        const mainCategory = data.category_list.find((cat) => cat.name === data.category);

                        // Activate unused categories only for brand businesses that will use them
                        const restaurant = restaurantId ? await restaurantsRepository.getRestaurantById(restaurantId) : null;

                        // If new restaurant or brand, we add the main category
                        if (!restaurant || restaurant?.type === BusinessCategory.BRAND) {
                            await addOrUpdateCategoriesService.execute({
                                categories: [{ categoryId: mainCategory.id, categoryName: mainCategory.name }],
                                platformKey: PlatformKey.FACEBOOK,
                            });
                        }

                        // We find it after in case it was just created
                        const fbCat = await categoriesRepository.findOne({
                            filter: { platformKey: PlatformKey.FACEBOOK, categoryId: mainCategory.id },
                            projection: { _id: 1 },
                            options: { lean: true },
                        });

                        return fbCat?._id;
                    } catch (err) {
                        logger.warn('[FACEBOOK_MAPPER_CATEGORY_LIST_ERROR]', err);
                    }
                },
                facebookKey: 'category',
                shouldBePublished: false,
            },
            categoryList: {
                malouKey: 'categoryList',
                toMalouFormat: async (data, restaurantId: string | undefined) => {
                    try {
                        if (!data.category_list || !data.category) {
                            return [];
                        }

                        const restaurant = restaurantId ? await restaurantsRepository.getRestaurantById(restaurantId) : null;

                        // Activate unused categories only for brand businesses or new brand businesses that will use them
                        if (!restaurant || restaurant?.type === BusinessCategory.BRAND) {
                            await addOrUpdateCategoriesService.execute({
                                categories: data.category_list.map((category) => ({
                                    categoryId: category.id,
                                    categoryName: category.name,
                                })),
                                platformKey: PlatformKey.FACEBOOK,
                            });
                        }

                        const mainCategory = data.category_list.find((cat) => cat.name === data.category);
                        const secondaryCategoryIds = data.category_list.filter((cat) => cat.id !== mainCategory.id).map((cat) => cat.id);

                        const fbCats = await categoriesRepository.find({
                            filter: {
                                categoryId: { $in: secondaryCategoryIds },
                                platformKey: PlatformKey.FACEBOOK,
                            },
                            projection: { _id: 1 },
                            options: { lean: true },
                        });

                        return fbCats.map((cat) => cat?._id);
                    } catch (err) {
                        logger.warn('[FACEBOOK_MAPPER_CATEGORY_LIST_ERROR]', err);
                    }
                },
                facebookKey: 'category_list',
                toFacebookFormat: async (
                    el: Partial<RestaurantPopulatedToPublish> & { _id: DbId }
                ): Promise<FacebookPageInfo['category_list'] | null> => {
                    const restaurant = await restaurantsRepository.findOne({
                        filter: { _id: el._id },
                        options: { lean: true, populate: [{ path: 'category' }, { path: 'categoryList' }] },
                    });

                    // FACEBOOK API: first in category_list is considered to be the main category
                    if (restaurant?.type === BusinessCategory.BRAND) {
                        const newCategories = [restaurant?.category?.categoryId, ...restaurant.categoryList?.map((cat) => cat.categoryId)];

                        if (newCategories.length === 0) {
                            return null;
                        }

                        return newCategories.slice(0, this._MAX_CATEGORIES_COUNT);
                    }

                    const facebookCategories: FacebookCategoryIdEnum[] = [];

                    const gmbMainCategoryId = restaurant?.category?.categoryId as GmbCategoryIdEnum;
                    const fbMainCategories = gmbToFacebookCategoriesMapping[gmbMainCategoryId];

                    // If no match for main category, we don't update any category because we don't want to replace the main category with arbitrary secondary categories
                    if (isNil(fbMainCategories)) {
                        return null;
                    }

                    if (fbMainCategories?.length > 0) {
                        const fbMainCategory = fbMainCategories.shift();
                        if (fbMainCategory) {
                            facebookCategories.push(fbMainCategory);
                        }
                    }

                    const remainingFbCategories =
                        restaurant?.categoryList
                            .map((cat) => gmbToFacebookCategoriesMapping[cat.categoryId as GmbCategoryIdEnum])
                            .flat()
                            .filter(isNotNil) ?? [];

                    facebookCategories.push(
                        ...uniq([
                            // Get remaining fb category from main category mapping
                            ...(fbMainCategories ? fbMainCategories : []),
                            // And all secondary categories
                            ...remainingFbCategories,
                        ])
                            // Filter out main category
                            .filter((cat) => !facebookCategories.includes(cat))
                    );

                    if (facebookCategories.length === 0) {
                        return null;
                    }

                    return facebookCategories.slice(0, this._MAX_CATEGORIES_COUNT);
                },
            },
            isClaimed: {
                malouKey: 'isClaimed',
                toMalouFormat: () => true,
                facebookKey: 'is_owned',
                shouldBePublished: false,
            },
            phone: {
                malouKey: 'phone',
                toMalouFormat: (data) => {
                    if (data.phone) {
                        try {
                            const number = phoneUtil.PhoneNumberUtil.getInstance().parseAndKeepRawInput(data.phone);
                            return {
                                prefix: number.getCountryCode(),
                                digits: number.getNationalNumber(),
                            };
                        } catch (e) {
                            return null;
                        }
                    }
                    return null;
                },
                facebookKey: 'phone',
                toFacebookFormat: (el: Partial<RestaurantPopulatedToPublish> & { _id: DbId }): FacebookPageInfo['phone'] | null => {
                    if (!(el.phone && el.phone.prefix && el.phone.digits)) {
                        return null;
                    }

                    return `+${el.phone.prefix}${el.phone.digits}`;
                },
            },
            rating: {
                malouKey: 'rating',
                toMalouFormat: (data) => (data.overall_star_rating ? data.overall_star_rating : null),
                facebookKey: 'overall_star_rating',
                shouldBePublished: false,
            },
            email: {
                malouKey: 'email',
                toMalouFormat: (data) => (data.emails ? data.emails[0] : null),
                facebookKey: 'emails',
                toFacebookFormat: (el: Partial<RestaurantPopulatedToPublish> & { _id: DbId }): FacebookPageInfo['emails'] | null =>
                    !el.email ? null : [el.email],
            },
            website: { malouKey: 'website', facebookKey: 'website' },
            descriptions: {
                malouKey: 'descriptions',
                toMalouFormat: (data) =>
                    data.about
                        ? [
                              {
                                  platformKey: PlatformKey.FACEBOOK,
                                  text: data.about,
                                  active: true,
                                  size: 'short',
                                  language: 'fr',
                              },
                          ]
                        : [],
                facebookKey: 'about',
                toFacebookFormat: (el: Partial<RestaurantPopulatedToPublish> & { _id: DbId }): FacebookPageInfo['about'] | null => {
                    if (!el.descriptions) {
                        return null;
                    }
                    const activeDesc = el.descriptions.find(
                        (elt) => elt.size === descriptionSize.SHORT.key && elt.language === Langs.FR.short
                    );
                    return !activeDesc?.text ? null : activeDesc.text.slice(0, 160);
                },
            },
            address: {
                malouKey: 'address',
                toMalouFormat: (data) =>
                    data.location
                        ? {
                              locality: data.location.city,
                              country: data.location.country,
                              route: data.location.street,
                              postalCode: data.location.zip,
                              formattedAddress: data.location.street,
                              // eslint-disable-next-line no-nested-ternary
                              regionCode: data.location.country
                                  ? COUNTRIES.find((c) => c.name.toLowerCase() === data.location.country.toLowerCase())
                                      ? COUNTRIES.find((c) => c.name.toLowerCase() === data.location.country.toLowerCase())?.code
                                      : null
                                  : null,
                          }
                        : null,
                facebookKey: 'location',
                toFacebookFormat: async (el: Partial<RestaurantPopulatedToPublish> & { _id: DbId }) => {
                    if (!el.address) {
                        return null;
                    }
                    let countryName: string | undefined;
                    if (el.address?.regionCode) {
                        const country = COUNTRIES.find((c) => c.code === el.address?.regionCode);
                        countryName = country?.name;
                    }
                    const defaultMappedLocation = {
                        city: el.address.locality,
                        country: countryName || el.address.country, // need to give the name in English
                        state: countryName || el.address.country,
                        zip: el.address.postalCode,
                        street: el.address.formattedAddress,
                        latitude: el.latlng?.lat,
                        longitude: el.latlng?.lng,
                    };
                    try {
                        const { data: cityList } = el.address.locality ? await citySearch(el.address.locality) : { data: [] };
                        const bestCity = cityList[0];
                        if (bestCity.type === FacebookCityTypes.CITY && bestCity.country_code === el.address.regionCode) {
                            const cityId = bestCity.key;
                            return {
                                city_id: cityId,
                                zip: el.address.postalCode,
                                street: el.address.formattedAddress,
                                latitude: el.latlng?.lat,
                                longitude: el.latlng?.lng,
                            };
                        }
                        return defaultMappedLocation;
                    } catch (error) {
                        return defaultMappedLocation;
                    }
                },
            },
            openingDate: {
                malouKey: 'openingDate',
                toMalouFormat: (data) => {
                    if (!data.start_info || !data.start_info.date || data.start_info.type === 'Unspecified') {
                        return null;
                    }
                    return new Date(data.start_info.date.year, data.start_info.date.month - 1, data.start_info.date.day);
                },
            },
            socialLink: {
                malouKey: 'socialLink',
                toMalouFormat: (data) => data.link || null,
                facebookKey: 'link',
                shouldBePublished: false,
            },
            isClosedTemporarily: {
                malouKey: 'isClosedTemporarily',
                toMalouFormat: (data) => data.temporary_status?.toLowerCase() === 'temporarily_closed',
                facebookKey: 'temporary_status',
                toFacebookFormat: (
                    el: Partial<RestaurantPopulatedToPublish> & { _id: DbId }
                ): FacebookPageInfo['temporary_status'] | null =>
                    isNil(el.isClosedTemporarily)
                        ? null
                        : el.isClosedTemporarily
                          ? FacebookTemporaryStatus.TEMPORARILY_CLOSED
                          : FacebookTemporaryStatus.OPERATING_AS_USUAL,
            },
            orderUrl: {
                malouKey: 'orderUrl',
                toMalouFormat: (data) => data.delivery_and_pickup_option_info?.[0] || null,
                facebookKey: 'delivery_and_pickup_option_info',
                toFacebookFormat: (
                    restaurant: Partial<RestaurantPopulatedToPublish> & { _id: DbId }
                ): FacebookPageInfo['delivery_and_pickup_option_info'] | null => (restaurant.orderUrl ? [restaurant.orderUrl] : null),
            },
            regularHours: {
                malouKey: 'regularHours',
                toMalouFormat: (data) => {
                    if (!data.hours) {
                        return [];
                    }
                    const res: { openDay: any; openTime?: any; closeDay: any; closeTime?: any; isClosed: boolean }[] = [];
                    const fbDayDict = {
                        mon: 'MONDAY',
                        tue: 'TUESDAY',
                        wed: 'WEDNESDAY',
                        thu: 'THURSDAY',
                        fri: 'FRIDAY',
                        sat: 'SATURDAY',
                        sun: 'SUNDAY',
                    };
                    // eslint-disable-next-line guard-for-in
                    for (const d in fbDayDict) {
                        // set open days
                        [1, 2].forEach((r) => {
                            // fb day range
                            if (`${d}_${r}_open` in data.hours) {
                                res.push({
                                    openDay: fbDayDict[d],
                                    openTime: data.hours[`${d}_${r}_open`],
                                    closeDay: fbDayDict[d],
                                    closeTime: data.hours[`${d}_${r}_close`],
                                    isClosed: false,
                                });
                            }
                        });
                    }
                    // eslint-disable-next-line guard-for-in
                    for (const key in fbDayDict) {
                        // ex: mon. Set closed days
                        const val = fbDayDict[key]; // ex: MONDAY
                        const openDay = res.filter((day) => day.openDay === val);
                        if (openDay.length === 0) {
                            res.push({
                                openDay: val,
                                closeDay: val,
                                isClosed: true,
                            });
                        }
                    }
                    return res;
                },
                facebookKey: 'hours',
                toFacebookFormat: (el: Partial<RestaurantPopulatedToPublish> & { _id: DbId }): FacebookPageInfo['hours'] | null => {
                    const hours = el.regularHours ?? [];

                    if (hours.length === 0) {
                        return null;
                    }

                    // Facebook API only allows 2 opening hours per day, if there are more than 2, we don't send them at all
                    const hasMoreThanTwoHoursInOneDay = hours
                        ?.reduce(
                            (acc, h) => {
                                const sameDayIndex = acc.findIndex((a) => a.openDay === h.openDay);
                                if (sameDayIndex !== -1) {
                                    acc[sameDayIndex].count += 1;
                                } else {
                                    acc.push({ openDay: h.openDay, count: 1 });
                                }
                                return acc;
                            },
                            [] as { openDay: Day; count: number }[]
                        )
                        .some((h) => h.count > 2);

                    if (!hours || hasMoreThanTwoHoursInOneDay) {
                        return {};
                    }

                    const res = {};
                    const groupedByDay: Partial<Record<Day, IRestaurant['regularHours']>> = {};

                    // Group by day

                    hours
                        .filter((h) => !h.isClosed)
                        .forEach((h) => {
                            if (!groupedByDay[h.openDay]) {
                                groupedByDay[h.openDay] = [];
                            }
                            groupedByDay[h.openDay]!.push(h);
                        });

                    // Sort each group by openTime
                    Object.entries(groupedByDay).forEach(([day, entries]) => {
                        if (!entries || entries.length === 0) {
                            return;
                        }

                        filterByRequiredKeys(entries, ['openTime'])
                            .sort((a, b) => a.openTime.localeCompare(b.openTime)) // sort by openTime
                            .forEach((h, index) => {
                                const keyPrefix = `${day.slice(0, 3).toLowerCase()}_${index + 1}`;
                                res[`${keyPrefix}_open`] = h.openTime;
                                res[`${keyPrefix}_close`] = h.closeTime;
                            });
                    });

                    if (Object.keys(res).length === 0) {
                        return null;
                    }

                    return res;
                },
            },
        };
    }

    static getFacebookPageCategory(locationData): FacebookPageCategory {
        if (locationData.locations?.data?.length) {
            return FacebookPageCategory.BRAND;
        }
        if (locationData.parent_page) {
            return FacebookPageCategory.STORE;
        }
        if (locationData.location && Object.keys(omit(locationData.location, ['latitude', 'longitude']))?.length) {
            return FacebookPageCategory.INDEPENDANT_LOCATION;
        }
        return FacebookPageCategory.DARK_KITCHEN;
    }
}
