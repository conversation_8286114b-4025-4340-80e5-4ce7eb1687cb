import { DateTime } from 'luxon';

export const projectStoryItemStage = {
    $project: {
        _id: 1,
        platformKeys: 1,
        published: 1,
        isPublishing: 1,
        feedbackMessageCount: 1,
        plannedPublicationDate: 1,
        firstAttachment: 1,
        socialAttachments: 1,
        error: 1,
        socialLink: 1,
        socialCreatedAt: 1,
        sortDate: 1,
        author: 1,
        mostRecentPublicationErrorCode: 1,
        bindingId: 1,
    },
} as const;

export const countStoriesStages = () => [
    {
        $facet: {
            total: [
                {
                    $count: 'total',
                },
            ],
            error: [
                {
                    $match: {
                        published: 'error',
                        createdAt: { $gt: DateTime.now().minus({ months: 6 }).toJSDate() },
                    },
                },
                { $count: 'error' },
            ],
            draft: [
                {
                    $match: {
                        published: 'draft',
                    },
                },
                {
                    $count: 'draft',
                },
            ],
            feedbacks: [
                {
                    $match: {
                        feedbackMessageCount: {
                            $gt: 0,
                        },
                    },
                },
                {
                    $count: 'feedbacks',
                },
            ],
        },
    },
    {
        $project: {
            total: {
                $arrayElemAt: ['$total.total', 0],
            },
            error: {
                $arrayElemAt: ['$error.error', 0],
            },
            draft: {
                $arrayElemAt: ['$draft.draft', 0],
            },
            feedbacks: {
                $arrayElemAt: ['$feedbacks.feedbacks', 0],
            },
        },
    },
];
