import { singleton } from 'tsyringe';

import { StoryItemDto } from '@malou-io/package-dto';
import { StoriesListFilter } from '@malou-io/package-utils';

import { StoriesRepository } from ':modules/stories/repository/stories.repository';

@singleton()
export class GetStoriesUseCase {
    private readonly _DEFAULT_LIMIT = 10;

    constructor(private readonly _storiesRepository: StoriesRepository) {}

    async execute(
        restaurantId: string,
        cursor: null | Date,
        limit: null | number,
        filter: StoriesListFilter | null
    ): Promise<{ storiesItems: StoryItemDto[]; nextCursor: null | Date }> {
        const actualLimit = limit ?? this._DEFAULT_LIMIT;
        const stories = await this._storiesRepository.getStories(restaurantId, cursor, actualLimit, filter);

        const storiesItems = stories.map((story) => story.toDto());
        const nextCursor = stories.length === actualLimit ? (stories[stories.length - 1].sortDate ?? null) : null;

        return { storiesItems, nextCursor };
    }
}
