import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { StoryItemDto } from '@malou-io/package-dto';
import { IFeedbackMessage, newDbId } from '@malou-io/package-models';
import {
    FeedbackMessageType,
    FeedbackMessageVisibility,
    PlatformKey,
    PostPublicationStatus,
    PostSource,
    PublicationErrorCode,
    Role,
    StoriesListFilter,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { getDefaultFeedback } from ':modules/posts/v2/tests/feedbacks.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { GetStoriesUseCase } from ':modules/stories/use-cases/get-stories/get-stories.use-case';

describe('GetStoriesUseCase', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'PostsRepository', 'FeedbacksRepository']);
    });

    describe('Empty result cases', () => {
        it('should return an empty array and null nextCursor if there is no posts', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult: (): { storiesItems: StoryItemDto[]; nextCursor: null | Date } => {
                    return { storiesItems: [], nextCursor: null };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, null, null, null);

            expect(storiesItems).toEqual(expectedResult);
        });

        it('should return an empty array and null nextCursor if the restaurant has no posts', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('123').build(), getDefaultRestaurant().uniqueKey('456').build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): { storiesItems: StoryItemDto[]; nextCursor: null | Date } => {
                    return { storiesItems: [], nextCursor: null };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, null, null, null);

            expect(storiesItems).toEqual(expectedResult);
        });

        it('should return an empty array and null nextCursor if the restaurant has no stories', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SEO)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): { storiesItems: StoryItemDto[]; nextCursor: null | Date } => {
                    return { storiesItems: [], nextCursor: null };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, null, null, null);

            expect(storiesItems).toEqual(expectedResult);
        });
    });

    describe('Filters', () => {
        it('should return only the story of the restaurant', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Amazing story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SEO)
                                    .text('Perfect SEO post')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): { storiesItems: StoryItemDto[]; nextCursor: null | Date } => {
                    const socialPost = dependencies.posts[0];
                    return {
                        storiesItems: [
                            {
                                id: socialPost._id.toString(),
                                platformKeys: [socialPost.key!],
                                published: socialPost.published,
                                isPublishing: socialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: socialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,
                                socialLink: socialPost.socialLink,
                                socialCreatedAt: socialPost.socialCreatedAt?.toISOString(),
                                sortDate: socialPost.sortDate!.toISOString(),
                                author: socialPost.author
                                    ? {
                                          id: socialPost.author._id.toString(),
                                          name: socialPost.author.name,
                                          lastname: socialPost.author.lastname,
                                          picture: socialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: undefined,
                                bindingId: socialPost.bindingId,
                            },
                        ],
                        nextCursor: null,
                    };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, null, null, null);

            expect(storiesItems).toEqual(expectedResult);
        });

        it('should return only the draft stories of the restaurant', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .published(PostPublicationStatus.DRAFT)
                                    .text('Amazing story')
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .text('Perfect published post')
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .published(PostPublicationStatus.ERROR)
                                    .text('Incredible error post')
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .published(PostPublicationStatus.PENDING)
                                    .text('Pretty pending post')
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .published(PostPublicationStatus.REJECTED)
                                    .text('Surprising rejected post')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): { storiesItems: StoryItemDto[]; nextCursor: null | Date } => {
                    const socialPost = dependencies.posts[0];
                    return {
                        storiesItems: [
                            {
                                id: socialPost._id.toString(),
                                platformKeys: [socialPost.key!],
                                published: socialPost.published,
                                isPublishing: socialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: socialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,
                                socialLink: socialPost.socialLink,
                                socialCreatedAt: socialPost.socialCreatedAt?.toISOString(),
                                sortDate: socialPost.sortDate!.toISOString(),
                                author: socialPost.author
                                    ? {
                                          id: socialPost.author._id.toString(),
                                          name: socialPost.author.name,
                                          lastname: socialPost.author.lastname,
                                          picture: socialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: undefined,
                                bindingId: socialPost.bindingId,
                            },
                        ],
                        nextCursor: null,
                    };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, null, null, StoriesListFilter.DRAFT);

            expect(storiesItems).toEqual(expectedResult);
        });

        it('should return only the stories in error of the restaurant', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .published(PostPublicationStatus.DRAFT)
                                    .text('Amazing story')
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .text('Perfect published post')
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .published(PostPublicationStatus.ERROR)
                                    .text('Incredible error post')
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .published(PostPublicationStatus.PENDING)
                                    .text('Pretty pending post')
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .published(PostPublicationStatus.REJECTED)
                                    .text('Surprising rejected post')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): { storiesItems: StoryItemDto[]; nextCursor: null | Date } => {
                    const socialPost = dependencies.posts[2];
                    return {
                        storiesItems: [
                            {
                                id: socialPost._id.toString(),
                                platformKeys: [socialPost.key!],
                                published: socialPost.published,
                                isPublishing: socialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: socialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,
                                socialLink: socialPost.socialLink,
                                socialCreatedAt: socialPost.socialCreatedAt?.toISOString(),
                                sortDate: socialPost.sortDate!.toISOString(),
                                author: socialPost.author
                                    ? {
                                          id: socialPost.author._id.toString(),
                                          name: socialPost.author.name,
                                          lastname: socialPost.author.lastname,
                                          picture: socialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: undefined,
                                bindingId: socialPost.bindingId,
                            },
                        ],
                        nextCursor: null,
                    };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, null, null, StoriesListFilter.ERROR);

            expect(storiesItems).toEqual(expectedResult);
        });

        it('should return all the stories of the restaurant', async () => {
            const today = DateTime.now().toJSDate();
            const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
            const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
            const threeDaysAgo = DateTime.now().minus({ days: 3 }).toJSDate();
            const fourDaysAgo = DateTime.now().minus({ days: 4 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .published(PostPublicationStatus.DRAFT)
                                    .text('Amazing story')
                                    .sortDate(today)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .text('Perfect published post')
                                    .sortDate(yesterday)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .published(PostPublicationStatus.ERROR)
                                    .text('Incredible error post')
                                    .sortDate(twoDaysAgo)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .published(PostPublicationStatus.PENDING)
                                    .text('Pretty pending post')
                                    .sortDate(threeDaysAgo)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .published(PostPublicationStatus.REJECTED)
                                    .text('Surprising rejected post')
                                    .sortDate(fourDaysAgo)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): { storiesItems: StoryItemDto[]; nextCursor: null | Date } => {
                    const draftSocialPost = dependencies.posts[0];
                    const publishedSocialPost = dependencies.posts[1];
                    const errorSocialPost = dependencies.posts[2];
                    const pendingSocialPost = dependencies.posts[3];
                    const rejectedSocialPost = dependencies.posts[4];
                    return {
                        storiesItems: [
                            {
                                id: draftSocialPost._id.toString(),
                                platformKeys: [draftSocialPost.key!],
                                published: draftSocialPost.published,
                                isPublishing: draftSocialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: draftSocialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,
                                socialLink: draftSocialPost.socialLink,
                                socialCreatedAt: draftSocialPost.socialCreatedAt?.toISOString(),
                                sortDate: draftSocialPost.sortDate!.toISOString(),
                                author: draftSocialPost.author
                                    ? {
                                          id: draftSocialPost.author._id.toString(),
                                          name: draftSocialPost.author.name,
                                          lastname: draftSocialPost.author.lastname,
                                          picture: draftSocialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: undefined,
                                bindingId: draftSocialPost.bindingId,
                            },
                            {
                                id: publishedSocialPost._id.toString(),
                                platformKeys: [publishedSocialPost.key!],
                                published: publishedSocialPost.published,
                                isPublishing: publishedSocialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: publishedSocialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,
                                socialLink: publishedSocialPost.socialLink,
                                socialCreatedAt: publishedSocialPost.socialCreatedAt?.toISOString(),
                                sortDate: publishedSocialPost.sortDate!.toISOString(),
                                author: publishedSocialPost.author
                                    ? {
                                          id: publishedSocialPost.author._id.toString(),
                                          name: publishedSocialPost.author.name,
                                          lastname: publishedSocialPost.author.lastname,
                                          picture: publishedSocialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: undefined,
                                bindingId: publishedSocialPost.bindingId,
                            },
                            {
                                id: errorSocialPost._id.toString(),
                                platformKeys: [errorSocialPost.key!],
                                published: errorSocialPost.published,
                                isPublishing: errorSocialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: errorSocialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,
                                socialLink: errorSocialPost.socialLink,
                                socialCreatedAt: errorSocialPost.socialCreatedAt?.toISOString(),
                                sortDate: errorSocialPost.sortDate!.toISOString(),
                                author: errorSocialPost.author
                                    ? {
                                          id: errorSocialPost.author._id.toString(),
                                          name: errorSocialPost.author.name,
                                          lastname: errorSocialPost.author.lastname,
                                          picture: errorSocialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: undefined,
                                bindingId: errorSocialPost.bindingId,
                            },
                            {
                                id: pendingSocialPost._id.toString(),
                                platformKeys: [pendingSocialPost.key!],
                                published: pendingSocialPost.published,
                                isPublishing: pendingSocialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: pendingSocialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,
                                socialLink: pendingSocialPost.socialLink,
                                socialCreatedAt: pendingSocialPost.socialCreatedAt?.toISOString(),
                                sortDate: pendingSocialPost.sortDate!.toISOString(),
                                author: pendingSocialPost.author
                                    ? {
                                          id: pendingSocialPost.author._id.toString(),
                                          name: pendingSocialPost.author.name,
                                          lastname: pendingSocialPost.author.lastname,
                                          picture: pendingSocialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: undefined,
                                bindingId: pendingSocialPost.bindingId,
                            },
                            {
                                id: rejectedSocialPost._id.toString(),
                                platformKeys: [rejectedSocialPost.key!],
                                published: rejectedSocialPost.published,
                                isPublishing: rejectedSocialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: rejectedSocialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,

                                socialLink: rejectedSocialPost.socialLink,
                                socialCreatedAt: rejectedSocialPost.socialCreatedAt?.toISOString(),
                                sortDate: rejectedSocialPost.sortDate!.toISOString(),
                                author: rejectedSocialPost.author
                                    ? {
                                          id: rejectedSocialPost.author._id.toString(),
                                          name: rejectedSocialPost.author.name,
                                          lastname: rejectedSocialPost.author.lastname,
                                          picture: rejectedSocialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: undefined,
                                bindingId: rejectedSocialPost.bindingId,
                            },
                        ],
                        nextCursor: null,
                    };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, null, null, StoriesListFilter.ALL);

            expect(storiesItems).toEqual(expectedResult);
        });

        it('should return the stories that have feedbacks of the restaurant', async () => {
            const feedbackMessage: IFeedbackMessage = {
                _id: newDbId(),
                type: FeedbackMessageType.TEXT,
                visibility: FeedbackMessageVisibility.BASIC,
                text: 'Feedback message',
                author: {
                    _id: newDbId(),
                    name: 'Customer',
                    lastname: 'Customer',
                    email: '<EMAIL>',
                    role: Role.ADMIN,
                },
                createdAt: DateTime.now().toJSDate(),
                updatedAt: DateTime.now().toJSDate(),
            };

            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts' | 'feedbacks'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    feedbacks: {
                        data() {
                            return [
                                getDefaultFeedback().isOpen(false).feedbackMessages([feedbackMessage]).build(),
                                getDefaultFeedback().isOpen(true).feedbackMessages([]).build(),
                                getDefaultFeedback().isOpen(true).feedbackMessages([feedbackMessage]).build(),
                            ];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Social post without feedbacks')
                                    .feedbackId(null)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Social post with closed feedbacks')
                                    .feedbackId(dependencies.feedbacks()[0]._id)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Social post with open feedbacks but not messages')
                                    .feedbackId(dependencies.feedbacks()[1]._id)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Social post with open feedbacks and messages')
                                    .feedbackId(dependencies.feedbacks()[2]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): { storiesItems: StoryItemDto[]; nextCursor: null | Date } => {
                    const socialPostWithOpenFeedbacksAndMessages = dependencies.posts[3];
                    return {
                        storiesItems: [
                            {
                                id: socialPostWithOpenFeedbacksAndMessages._id.toString(),
                                platformKeys: [socialPostWithOpenFeedbacksAndMessages.key!],
                                published: socialPostWithOpenFeedbacksAndMessages.published,
                                isPublishing: socialPostWithOpenFeedbacksAndMessages.isPublishing ?? false,
                                feedbackMessageCount: dependencies.feedbacks[2].feedbackMessages.length,
                                plannedPublicationDate:
                                    socialPostWithOpenFeedbacksAndMessages.plannedPublicationDate?.toISOString() ?? null,
                                media: null,

                                socialLink: socialPostWithOpenFeedbacksAndMessages.socialLink,
                                socialCreatedAt: socialPostWithOpenFeedbacksAndMessages.socialCreatedAt?.toISOString(),
                                sortDate: socialPostWithOpenFeedbacksAndMessages.sortDate!.toISOString(),
                                author: socialPostWithOpenFeedbacksAndMessages.author
                                    ? {
                                          id: socialPostWithOpenFeedbacksAndMessages.author._id.toString(),
                                          name: socialPostWithOpenFeedbacksAndMessages.author.name,
                                          lastname: socialPostWithOpenFeedbacksAndMessages.author.lastname,
                                          picture: socialPostWithOpenFeedbacksAndMessages.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: undefined,
                                bindingId: socialPostWithOpenFeedbacksAndMessages.bindingId,
                            },
                        ],
                        nextCursor: null,
                    };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, null, null, StoriesListFilter.FEEDBACK);

            expect(storiesItems).toEqual(expectedResult);
        });
    });

    describe('Pagination', () => {
        it('should return the stories of the restaurant sorted by sort date desc', async () => {
            const today = DateTime.now().toJSDate();
            const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
            const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();

            const limit = 3;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Amazing story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(today)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.FACEBOOK)
                                    .text('Awesome story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(twoDaysAgo)
                                    .publicationErrors([{ happenedAt: new Date(), code: PublicationErrorCode.UNKNOWN_ERROR }])
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Incredible story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(yesterday)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): { storiesItems: StoryItemDto[]; nextCursor: null | Date } => {
                    const amazingSocialPost = dependencies.posts[0];
                    const awesomeSocialPost = dependencies.posts[1];
                    const incredibleSocialPost = dependencies.posts[2];
                    return {
                        storiesItems: [
                            {
                                id: amazingSocialPost._id.toString(),
                                platformKeys: [amazingSocialPost.key!],
                                published: amazingSocialPost.published,
                                isPublishing: amazingSocialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: amazingSocialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,

                                socialLink: amazingSocialPost.socialLink,
                                socialCreatedAt: amazingSocialPost.socialCreatedAt?.toISOString(),
                                sortDate: amazingSocialPost.sortDate!.toISOString(),
                                author: amazingSocialPost.author
                                    ? {
                                          id: amazingSocialPost.author._id.toString(),
                                          name: amazingSocialPost.author.name,
                                          lastname: amazingSocialPost.author.lastname,
                                          picture: amazingSocialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: undefined,
                                bindingId: amazingSocialPost.bindingId,
                            },
                            {
                                id: incredibleSocialPost._id.toString(),
                                platformKeys: [incredibleSocialPost.key!],
                                published: incredibleSocialPost.published,
                                isPublishing: incredibleSocialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: incredibleSocialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,

                                socialLink: incredibleSocialPost.socialLink,
                                socialCreatedAt: incredibleSocialPost.socialCreatedAt?.toISOString(),
                                sortDate: incredibleSocialPost.sortDate!.toISOString(),
                                author: incredibleSocialPost.author
                                    ? {
                                          id: incredibleSocialPost.author._id.toString(),
                                          name: incredibleSocialPost.author.name,
                                          lastname: incredibleSocialPost.author.lastname,
                                          picture: incredibleSocialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: undefined,
                                bindingId: incredibleSocialPost.bindingId,
                            },
                            {
                                id: awesomeSocialPost._id.toString(),
                                platformKeys: [awesomeSocialPost.key!],
                                published: awesomeSocialPost.published,
                                isPublishing: awesomeSocialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: awesomeSocialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,

                                socialLink: awesomeSocialPost.socialLink,
                                socialCreatedAt: awesomeSocialPost.socialCreatedAt?.toISOString(),
                                sortDate: awesomeSocialPost.sortDate!.toISOString(),
                                author: awesomeSocialPost.author
                                    ? {
                                          id: awesomeSocialPost.author._id.toString(),
                                          name: awesomeSocialPost.author.name,
                                          lastname: awesomeSocialPost.author.lastname,
                                          picture: awesomeSocialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: PublicationErrorCode.UNKNOWN_ERROR,
                                bindingId: awesomeSocialPost.bindingId,
                            },
                        ],
                        nextCursor: awesomeSocialPost.sortDate ?? null,
                    };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, null, limit, null);

            expect(storiesItems).toEqual(expectedResult);
        });

        it('should return maximum [limit] stories of the restaurant sorted by sort date desc', async () => {
            const today = DateTime.now().toJSDate();
            const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
            const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
            const threeDaysAgo = DateTime.now().minus({ days: 3 }).toJSDate();

            const limit = 3;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Amazing story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(today)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.FACEBOOK)
                                    .text('Awesome story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(twoDaysAgo)
                                    .publicationErrors([
                                        { happenedAt: new Date(), code: PublicationErrorCode.USER_MISSING_APPROPRIATE_ROLE },
                                    ])
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Incredible story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(yesterday)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.FACEBOOK)
                                    .text('Formidable story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(threeDaysAgo)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): { storiesItems: StoryItemDto[]; nextCursor: null | Date } => {
                    const amazingSocialPost = dependencies.posts[0];
                    const awesomeSocialPost = dependencies.posts[1];
                    const incredibleSocialPost = dependencies.posts[2];
                    return {
                        storiesItems: [
                            {
                                id: amazingSocialPost._id.toString(),
                                platformKeys: [amazingSocialPost.key!],
                                published: amazingSocialPost.published,
                                isPublishing: amazingSocialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: amazingSocialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,

                                socialLink: amazingSocialPost.socialLink,
                                socialCreatedAt: amazingSocialPost.socialCreatedAt?.toISOString(),
                                sortDate: amazingSocialPost.sortDate!.toISOString(),
                                author: amazingSocialPost.author
                                    ? {
                                          id: amazingSocialPost.author._id.toString(),
                                          name: amazingSocialPost.author.name,
                                          lastname: amazingSocialPost.author.lastname,
                                          picture: amazingSocialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: undefined,
                                bindingId: amazingSocialPost.bindingId,
                            },
                            {
                                id: incredibleSocialPost._id.toString(),
                                platformKeys: [incredibleSocialPost.key!],
                                published: incredibleSocialPost.published,
                                isPublishing: incredibleSocialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: incredibleSocialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,

                                socialLink: incredibleSocialPost.socialLink,
                                socialCreatedAt: incredibleSocialPost.socialCreatedAt?.toISOString(),
                                sortDate: incredibleSocialPost.sortDate!.toISOString(),
                                author: incredibleSocialPost.author
                                    ? {
                                          id: incredibleSocialPost.author._id.toString(),
                                          name: incredibleSocialPost.author.name,
                                          lastname: incredibleSocialPost.author.lastname,
                                          picture: incredibleSocialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: undefined,
                                bindingId: incredibleSocialPost.bindingId,
                            },
                            {
                                id: awesomeSocialPost._id.toString(),
                                platformKeys: [awesomeSocialPost.key!],
                                published: awesomeSocialPost.published,
                                isPublishing: awesomeSocialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: awesomeSocialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,

                                socialLink: awesomeSocialPost.socialLink,
                                socialCreatedAt: awesomeSocialPost.socialCreatedAt?.toISOString(),
                                sortDate: awesomeSocialPost.sortDate!.toISOString(),
                                author: awesomeSocialPost.author
                                    ? {
                                          id: awesomeSocialPost.author._id.toString(),
                                          name: awesomeSocialPost.author.name,
                                          lastname: awesomeSocialPost.author.lastname,
                                          picture: awesomeSocialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: PublicationErrorCode.USER_MISSING_APPROPRIATE_ROLE,
                                bindingId: awesomeSocialPost.bindingId,
                            },
                        ],
                        nextCursor: awesomeSocialPost.sortDate ?? null,
                    };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, null, limit, null);

            expect(storiesItems).toEqual(expectedResult);
        });

        it('should return all the stories of the restaurant, and nextCursor null, if the limit exceeds their count', async () => {
            const today = DateTime.now().toJSDate();
            const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
            const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
            const threeDaysAgo = DateTime.now().minus({ days: 3 }).toJSDate();

            const limit = 10;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Amazing story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(today)
                                    .publicationErrors([
                                        { happenedAt: new Date(), code: PublicationErrorCode.UNKNOWN_ERROR },
                                        { happenedAt: new Date(), code: PublicationErrorCode.CONNECTION_EXPIRED },
                                    ])
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.FACEBOOK)
                                    .text('Awesome story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(twoDaysAgo)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Incredible story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(yesterday)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.FACEBOOK)
                                    .text('Formidable story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(threeDaysAgo)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): { storiesItems: StoryItemDto[]; nextCursor: null | Date } => {
                    const amazingSocialPost = dependencies.posts[0];
                    const awesomeSocialPost = dependencies.posts[1];
                    const incredibleSocialPost = dependencies.posts[2];
                    const formidableSocialPost = dependencies.posts[3];
                    return {
                        storiesItems: [
                            {
                                id: amazingSocialPost._id.toString(),
                                platformKeys: [amazingSocialPost.key!],
                                published: amazingSocialPost.published,
                                isPublishing: amazingSocialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: amazingSocialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,

                                socialLink: amazingSocialPost.socialLink,
                                socialCreatedAt: amazingSocialPost.socialCreatedAt?.toISOString(),
                                sortDate: amazingSocialPost.sortDate!.toISOString(),
                                author: amazingSocialPost.author
                                    ? {
                                          id: amazingSocialPost.author._id.toString(),
                                          name: amazingSocialPost.author.name,
                                          lastname: amazingSocialPost.author.lastname,
                                          picture: amazingSocialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: PublicationErrorCode.CONNECTION_EXPIRED,
                                bindingId: amazingSocialPost.bindingId,
                            },
                            {
                                id: incredibleSocialPost._id.toString(),
                                platformKeys: [incredibleSocialPost.key!],
                                published: incredibleSocialPost.published,
                                isPublishing: incredibleSocialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: incredibleSocialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,

                                socialLink: incredibleSocialPost.socialLink,
                                socialCreatedAt: incredibleSocialPost.socialCreatedAt?.toISOString(),
                                sortDate: incredibleSocialPost.sortDate!.toISOString(),
                                author: incredibleSocialPost.author
                                    ? {
                                          id: incredibleSocialPost.author._id.toString(),
                                          name: incredibleSocialPost.author.name,
                                          lastname: incredibleSocialPost.author.lastname,
                                          picture: incredibleSocialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: undefined,
                                bindingId: incredibleSocialPost.bindingId,
                            },
                            {
                                id: awesomeSocialPost._id.toString(),
                                platformKeys: [awesomeSocialPost.key!],
                                published: awesomeSocialPost.published,
                                isPublishing: awesomeSocialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: awesomeSocialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,

                                socialLink: awesomeSocialPost.socialLink,
                                socialCreatedAt: awesomeSocialPost.socialCreatedAt?.toISOString(),
                                sortDate: awesomeSocialPost.sortDate!.toISOString(),
                                author: awesomeSocialPost.author
                                    ? {
                                          id: awesomeSocialPost.author._id.toString(),
                                          name: awesomeSocialPost.author.name,
                                          lastname: awesomeSocialPost.author.lastname,
                                          picture: awesomeSocialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: undefined,
                                bindingId: awesomeSocialPost.bindingId,
                            },
                            {
                                id: formidableSocialPost._id.toString(),
                                platformKeys: [formidableSocialPost.key!],
                                published: formidableSocialPost.published,
                                isPublishing: formidableSocialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: formidableSocialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,

                                socialLink: formidableSocialPost.socialLink,
                                socialCreatedAt: formidableSocialPost.socialCreatedAt?.toISOString(),
                                sortDate: formidableSocialPost.sortDate!.toISOString(),
                                author: formidableSocialPost.author
                                    ? {
                                          id: formidableSocialPost.author._id.toString(),
                                          name: formidableSocialPost.author.name,
                                          lastname: formidableSocialPost.author.lastname,
                                          picture: formidableSocialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                mostRecentPublicationErrorCode: undefined,
                                bindingId: formidableSocialPost.bindingId,
                            },
                        ],
                        nextCursor: null,
                    };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, null, limit, null);

            expect(storiesItems).toEqual(expectedResult);
        });

        it('should return the stories after the given next cursor', async () => {
            const today = DateTime.now().toJSDate();
            const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
            const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
            const threeDaysAgo = DateTime.now().minus({ days: 3 }).toJSDate();

            const limit = 3;
            const nextCursor = twoDaysAgo;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Amazing story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(today)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.FACEBOOK)
                                    .text('Awesome story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(twoDaysAgo)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Incredible story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(yesterday)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.FACEBOOK)
                                    .text('Formidable story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(threeDaysAgo)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): { storiesItems: StoryItemDto[]; nextCursor: null | Date } => {
                    const formidableSocialPost = dependencies.posts[3];
                    return {
                        storiesItems: [
                            {
                                id: formidableSocialPost._id.toString(),
                                platformKeys: [formidableSocialPost.key!],
                                published: formidableSocialPost.published,
                                isPublishing: formidableSocialPost.isPublishing ?? false,
                                feedbackMessageCount: 0,
                                plannedPublicationDate: formidableSocialPost.plannedPublicationDate?.toISOString() ?? null,
                                media: null,

                                socialLink: formidableSocialPost.socialLink,
                                socialCreatedAt: formidableSocialPost.socialCreatedAt?.toISOString(),
                                sortDate: formidableSocialPost.sortDate!.toISOString(),
                                author: formidableSocialPost.author
                                    ? {
                                          id: formidableSocialPost.author._id.toString(),
                                          name: formidableSocialPost.author.name,
                                          lastname: formidableSocialPost.author.lastname,
                                          picture: formidableSocialPost.author.picture ?? undefined,
                                      }
                                    : undefined,
                                bindingId: formidableSocialPost.bindingId,
                            },
                        ],
                        nextCursor: null,
                    };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, nextCursor, limit, null);

            expect(storiesItems).toEqual(expectedResult);
        });

        it('should return and empty array if the restaurant has no story after the given next cursor', async () => {
            const today = DateTime.now().toJSDate();
            const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
            const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
            const threeDaysAgo = DateTime.now().minus({ days: 3 }).toJSDate();

            const limit = 3;
            const nextCursor = threeDaysAgo;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Amazing story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(today)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.FACEBOOK)
                                    .text('Awesome story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(twoDaysAgo)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .text('Incredible story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(yesterday)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.FACEBOOK)
                                    .text('Formidable story')
                                    .feedbackId(null)
                                    .thumbnail(null)
                                    .sortDate(threeDaysAgo)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): { storiesItems: StoryItemDto[]; nextCursor: null | Date } => {
                    return {
                        storiesItems: [],
                        nextCursor: null,
                    };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, nextCursor, limit, null);

            expect(storiesItems).toEqual(expectedResult);
        });
    });

    describe('Fields population', () => {
        it('should return the key of the story (if it has one) in platformKeys', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): StoryItemDto['platformKeys'] => {
                    const socialPost = dependencies.posts[0];
                    return [socialPost.key!];
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, null, null, null);

            expect(storiesItems.storiesItems[0]?.platformKeys).toEqual(expectedResult);
        });

        it('should return the keys of the story, if it has no key field, in platformKeys', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .key(undefined)
                                    .keys([PlatformKey.INSTAGRAM, PlatformKey.FACEBOOK])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): StoryItemDto['platformKeys'] => {
                    const socialPost = dependencies.posts[0];
                    return socialPost.keys ?? [];
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, null, null, null);

            expect(storiesItems.storiesItems[0]?.platformKeys).toIncludeSameMembers(expectedResult);
        });

        it('should return feedbackMessageCount 0 if the story has no feedbackId', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .feedbackId(null)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): StoryItemDto['feedbackMessageCount'] => {
                    return 0;
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, null, null, null);

            expect(storiesItems.storiesItems[0]?.feedbackMessageCount).toEqual(expectedResult);
        });

        it("should return feedbackMessageCount according to the story's feedback", async () => {
            const feedbackMessages: IFeedbackMessage[] = [
                {
                    _id: newDbId(),
                    text: 'Hello :) \n',
                    author: {
                        _id: newDbId(),
                        name: 'Arthur',
                        lastname: 'Saint Martin',
                        email: '<EMAIL>',
                        role: Role.ADMIN,
                    },
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    type: FeedbackMessageType.TEXT,
                    visibility: FeedbackMessageVisibility.ADMIN,
                },
                {
                    _id: newDbId(),
                    text: 'Another one',
                    author: {
                        _id: newDbId(),
                        name: 'Arthur',
                        lastname: 'Saint Martin',
                        email: '<EMAIL>',
                        role: Role.ADMIN,
                    },
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    type: FeedbackMessageType.TEXT,
                    visibility: FeedbackMessageVisibility.ADMIN,
                },
            ];

            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts' | 'feedbacks'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .feedbackId(dependencies.feedbacks()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    feedbacks: {
                        data() {
                            return [getDefaultFeedback().feedbackMessages(feedbackMessages).build()];
                        },
                    },
                },
                expectedResult: (): StoryItemDto['feedbackMessageCount'] => {
                    return feedbackMessages.length;
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, null, null, null);

            expect(storiesItems.storiesItems[0]?.feedbackMessageCount).toEqual(expectedResult);
        });

        it('should return the author of the story', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .feedbackId(null)
                                    .author({
                                        _id: newDbId(),
                                        name: 'Arthur',
                                        lastname: 'Saint Martin',
                                        picture: 'https://www.google.com',
                                    })
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies): StoryItemDto['author'] => {
                    return {
                        id: dependencies.posts[0].author!._id.toString(),
                        name: dependencies.posts[0].author!.name,
                        lastname: dependencies.posts[0].author!.lastname,
                        picture: dependencies.posts[0].author!.picture ?? undefined,
                    };
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const expectedResult = testCase.getExpectedResult();

            const getStoriesUseCase = container.resolve(GetStoriesUseCase);
            const storiesItems = await getStoriesUseCase.execute(restaurantId, null, null, null);

            expect(storiesItems.storiesItems[0]?.author).toEqual(expectedResult);
        });
    });
});
