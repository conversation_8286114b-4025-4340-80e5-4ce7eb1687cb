import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { GetStoriesCountsDto } from '@malou-io/package-dto';
import { IFeedbackMessage, newDbId } from '@malou-io/package-models';
import { FeedbackMessageType, FeedbackMessageVisibility, PostPublicationStatus, PostSource, Role } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { getDefaultFeedback } from ':modules/posts/v2/tests/feedbacks.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { GetStoriesCountsUseCase } from ':modules/stories/use-cases/get-stories-counts/get-stories-counts.use-case';

describe('GetStoriesCountsUseCase', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'PostsRepository', 'FeedbacksRepository']);
    });

    describe('0 result cases', () => {
        it('should return 0 for all counts if there is no post', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult: (): GetStoriesCountsDto => {
                    return { total: 0, error: 0, draft: 0, feedbacks: 0 };
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getStoriesCountsUseCase = container.resolve(GetStoriesCountsUseCase);
            const result = await getStoriesCountsUseCase.execute(restaurantId);

            expect(result).toEqual(expectedResult);
        });

        it('should return 0 for all counts if restaurant has no post', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('123').build(), getDefaultRestaurant().uniqueKey('456').build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): GetStoriesCountsDto => {
                    return { total: 0, error: 0, draft: 0, feedbacks: 0 };
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getStoriesCountsUseCase = container.resolve(GetStoriesCountsUseCase);
            const result = await getStoriesCountsUseCase.execute(restaurantId);

            expect(result).toEqual(expectedResult);
        });

        it('should return 0 for all counts if restaurant has no story', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SEO)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): GetStoriesCountsDto => {
                    return { total: 0, error: 0, draft: 0, feedbacks: 0 };
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getStoriesCountsUseCase = container.resolve(GetStoriesCountsUseCase);
            const result = await getStoriesCountsUseCase.execute(restaurantId);

            expect(result).toEqual(expectedResult);
        });
    });

    describe('1 result cases', () => {
        it('should return total 1 if restaurant has one published story', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): GetStoriesCountsDto => {
                    return { total: 1, error: 0, draft: 0, feedbacks: 0 };
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getStoriesCountsUseCase = container.resolve(GetStoriesCountsUseCase);
            const result = await getStoriesCountsUseCase.execute(restaurantId);

            expect(result).toEqual(expectedResult);
        });

        it('should return error 1 if restaurant has one story in error', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.ERROR)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): GetStoriesCountsDto => {
                    return { total: 1, error: 1, draft: 0, feedbacks: 0 };
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getStoriesCountsUseCase = container.resolve(GetStoriesCountsUseCase);
            const result = await getStoriesCountsUseCase.execute(restaurantId);

            expect(result).toEqual(expectedResult);
        });

        it('should return draft 1 if restaurant has one draft story', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.DRAFT)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): GetStoriesCountsDto => {
                    return { total: 1, error: 0, draft: 1, feedbacks: 0 };
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getStoriesCountsUseCase = container.resolve(GetStoriesCountsUseCase);
            const result = await getStoriesCountsUseCase.execute(restaurantId);

            expect(result).toEqual(expectedResult);
        });

        it('should return feedbacks 1 if restaurant has one story with open feedbacks', async () => {
            const feedbackMessage: IFeedbackMessage = {
                _id: newDbId(),
                type: FeedbackMessageType.TEXT,
                visibility: FeedbackMessageVisibility.BASIC,
                text: 'Feedback message',
                author: {
                    _id: newDbId(),
                    name: 'Customer',
                    lastname: 'Customer',
                    email: '<EMAIL>',
                    role: Role.ADMIN,
                },
                createdAt: DateTime.now().toJSDate(),
                updatedAt: DateTime.now().toJSDate(),
            };

            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts' | 'feedbacks'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    feedbacks: {
                        data() {
                            return [getDefaultFeedback().isOpen(true).feedbackMessages([feedbackMessage]).build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .feedbackId(dependencies.feedbacks()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): GetStoriesCountsDto => {
                    return { total: 1, error: 0, draft: 0, feedbacks: 1 };
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getStoriesCountsUseCase = container.resolve(GetStoriesCountsUseCase);
            const result = await getStoriesCountsUseCase.execute(restaurantId);

            expect(result).toEqual(expectedResult);
        });
    });

    describe('Multiple results cases', () => {
        it('should return correct counts if restaurant has multiple stories', async () => {
            const feedbackMessage: IFeedbackMessage = {
                _id: newDbId(),
                type: FeedbackMessageType.TEXT,
                visibility: FeedbackMessageVisibility.BASIC,
                text: 'Feedback message',
                author: {
                    _id: newDbId(),
                    name: 'Customer',
                    lastname: 'Customer',
                    email: '<EMAIL>',
                    role: Role.ADMIN,
                },
                createdAt: DateTime.now().toJSDate(),
                updatedAt: DateTime.now().toJSDate(),
            };

            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts' | 'feedbacks'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    feedbacks: {
                        data() {
                            return [
                                getDefaultFeedback().isOpen(true).feedbackMessages([feedbackMessage]).build(),
                                getDefaultFeedback().isOpen(false).feedbackMessages([feedbackMessage]).build(),
                                getDefaultFeedback().isOpen(true).feedbackMessages([feedbackMessage]).build(),
                            ];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.ERROR)
                                    .feedbackId(dependencies.feedbacks()[0]._id)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .feedbackId(dependencies.feedbacks()[1]._id)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.DRAFT)
                                    .feedbackId(dependencies.feedbacks()[2]._id)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.DRAFT)
                                    .feedbackId(null)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PENDING)
                                    .feedbackId(null)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): GetStoriesCountsDto => {
                    return { total: 5, error: 1, draft: 2, feedbacks: 2 };
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getStoriesCountsUseCase = container.resolve(GetStoriesCountsUseCase);
            const result = await getStoriesCountsUseCase.execute(restaurantId);

            expect(result).toEqual(expectedResult);
        });
    });
});
