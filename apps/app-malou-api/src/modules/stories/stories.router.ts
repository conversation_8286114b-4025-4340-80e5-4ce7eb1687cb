import { Router } from 'express';
import { singleton } from 'tsyringe';

import StoriesController from ':modules/stories/stories.controller';
import { authorize } from ':plugins/passport';

@singleton()
export default class StoriesRouter {
    private readonly _prefix = '/stories';

    constructor(private readonly _storiesController: StoriesController) {}

    init(router: Router): void {
        router.get(`${this._prefix}/restaurants/:restaurant_id/stories`, authorize(), (req, res, next) =>
            this._storiesController.handleGetStories(req, res, next)
        );

        router.get(`${this._prefix}/restaurants/:restaurant_id/stories-counts`, authorize(), (req, res, next) =>
            this._storiesController.handleGetStoriesCounts(req, res, next)
        );
    }
}
