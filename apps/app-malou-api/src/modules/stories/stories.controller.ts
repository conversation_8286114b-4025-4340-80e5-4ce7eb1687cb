import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import {
    GetStoriesCountsDto,
    GetStoriesCountsParamsDto,
    getStoriesCountsParamsValidator,
    GetStoriesParamsDto,
    getStoriesParamsValidator,
    GetStoriesQueryDto,
    getStoriesQueryValidator,
    StoryItemDto,
} from '@malou-io/package-dto';
import { ApiResultV2 } from '@malou-io/package-utils';

import { Params, Query } from ':helpers/decorators/validators';
import { GetStoriesCountsUseCase } from ':modules/stories/use-cases/get-stories-counts/get-stories-counts.use-case';
import { GetStoriesUseCase } from ':modules/stories/use-cases/get-stories/get-stories.use-case';

@singleton()
export default class StoriesController {
    constructor(
        private readonly _getStoriesUseCase: GetStoriesUseCase,
        private readonly _getStoriesCountsUseCase: GetStoriesCountsUseCase
    ) {}

    @Query(getStoriesQueryValidator)
    @Params(getStoriesParamsValidator)
    async handleGetStories(
        req: Request<GetStoriesParamsDto, never, never, GetStoriesQueryDto>,
        res: Response<ApiResultV2<{ storiesItems: StoryItemDto[]; nextCursor: null | Date }>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { cursor, limit, filter } = req.query;

            const results = await this._getStoriesUseCase.execute(restaurantId, cursor, limit, filter);

            res.json({ data: results });
        } catch (err) {
            next(err);
        }
    }

    @Params(getStoriesCountsParamsValidator)
    async handleGetStoriesCounts(
        req: Request<GetStoriesCountsParamsDto>,
        res: Response<ApiResultV2<GetStoriesCountsDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;

            const socialPostsCounts = await this._getStoriesCountsUseCase.execute(restaurantId);

            return res.json({ data: socialPostsCounts });
        } catch (err) {
            next(err);
        }
    }
}
