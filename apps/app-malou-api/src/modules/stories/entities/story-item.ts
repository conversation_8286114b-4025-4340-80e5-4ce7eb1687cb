import { StoryItemDto } from '@malou-io/package-dto';
import { DeviceType, EntityConstructor, PlatformKey, PostPublicationStatus, PublicationErrorCode } from '@malou-io/package-utils';

import { PostAuthor } from ':modules/posts/v2/entities/author.entity';
import { SocialPostMedia } from ':modules/posts/v2/entities/social-post-media.entity';

type StoryItemProps = EntityConstructor<StoryItem> & { id: string };

export class StoryItem {
    id: string;
    platformKeys: PlatformKey[];
    published: PostPublicationStatus;
    isPublishing: boolean;
    feedbackMessageCount: number;
    plannedPublicationDate?: Date;
    media: SocialPostMedia | null;
    socialLink?: string;
    socialCreatedAt?: Date;
    sortDate: Date;
    author?: PostAuthor;
    mostRecentPublicationErrorCode?: PublicationErrorCode;
    bindingId?: string;
    createdFromDeviceType?: DeviceType;

    constructor(data: StoryItemProps) {
        this.id = data.id;
        this.platformKeys = data.platformKeys;
        this.published = data.published;
        this.isPublishing = data.isPublishing;
        this.feedbackMessageCount = data.feedbackMessageCount;
        this.plannedPublicationDate = data.plannedPublicationDate;
        this.media = data.media;
        this.socialLink = data.socialLink;
        this.socialCreatedAt = data.socialCreatedAt;
        this.sortDate = data.sortDate;
        this.author = data.author;
        this.mostRecentPublicationErrorCode = data.mostRecentPublicationErrorCode;
        this.bindingId = data.bindingId;
        this.createdFromDeviceType = data.createdFromDeviceType;
    }

    toDto(): StoryItemDto {
        return {
            id: this.id,
            platformKeys: this.platformKeys,
            published: this.published,
            isPublishing: this.isPublishing,
            feedbackMessageCount: this.feedbackMessageCount,
            plannedPublicationDate: this.plannedPublicationDate?.toISOString() ?? null,
            media: this.media ? this.media.toDto() : null,
            socialLink: this.socialLink,
            socialCreatedAt: this.socialCreatedAt?.toISOString(),
            sortDate: this.sortDate.toISOString(),
            author: this.author ? this.author.toDto() : undefined,
            mostRecentPublicationErrorCode: this.mostRecentPublicationErrorCode,
            bindingId: this.bindingId,
            createdFromDeviceType: this.createdFromDeviceType,
        };
    }
}
