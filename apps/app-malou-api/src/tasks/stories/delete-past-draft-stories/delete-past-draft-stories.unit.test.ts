import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { PostPublicationStatus, PostSource } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { StoriesRepository } from ':modules/stories/repository/stories.repository';
import { DeletePastDraftStoriesTask } from ':tasks/stories/delete-past-draft-stories/delete-past-draft-stories';

describe('DeletePastDraftStoriesTask', () => {
    beforeAll(() => {
        registerRepositories(['PostsRepository']);
    });

    describe('should delete past draft stories', () => {
        it('should delete draft stories with no attachments and no planned publication date', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .attachments([])
                                    .plannedPublicationDate(null)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const deletePastDraftStoriesTask = container.resolve(DeletePastDraftStoriesTask);

            // Verify the story exists before deletion
            const storiesBeforeDeletion = await storiesRepository.find({
                filter: { isStory: true, published: PostPublicationStatus.DRAFT },
            });
            expect(storiesBeforeDeletion.length).toBe(1);

            // Execute the task
            await deletePastDraftStoriesTask.execute();

            // Verify the story was deleted
            const storiesAfterDeletion = await storiesRepository.find({
                filter: { isStory: true, published: PostPublicationStatus.DRAFT },
            });
            expect(storiesAfterDeletion.length).toBe(0);
        });

        it('should delete draft stories with null attachments and planned publication date in the past', async () => {
            const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .attachments(undefined)
                                    .plannedPublicationDate(twoDaysAgo)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const deletePastDraftStoriesTask = container.resolve(DeletePastDraftStoriesTask);

            // Verify the story exists before deletion
            const storiesBeforeDeletion = await storiesRepository.find({
                filter: { isStory: true, published: PostPublicationStatus.DRAFT },
            });
            expect(storiesBeforeDeletion.length).toBe(1);

            // Execute the task
            await deletePastDraftStoriesTask.execute();

            // Verify the story was deleted
            const storiesAfterDeletion = await storiesRepository.find({
                filter: { isStory: true, published: PostPublicationStatus.DRAFT },
            });
            expect(storiesAfterDeletion.length).toBe(0);
        });

        it('should delete multiple draft stories that meet the criteria', async () => {
            const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .attachments([])
                                    .plannedPublicationDate(null)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .attachments(undefined)
                                    .plannedPublicationDate(twoDaysAgo)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .attachments([])
                                    .plannedPublicationDate(twoDaysAgo)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const deletePastDraftStoriesTask = container.resolve(DeletePastDraftStoriesTask);

            // Verify the stories exist before deletion
            const storiesBeforeDeletion = await storiesRepository.find({
                filter: { isStory: true, published: PostPublicationStatus.DRAFT },
            });
            expect(storiesBeforeDeletion.length).toBe(3);

            // Execute the task
            await deletePastDraftStoriesTask.execute();

            // Verify all stories were deleted
            const storiesAfterDeletion = await storiesRepository.find({
                filter: { isStory: true, published: PostPublicationStatus.DRAFT },
            });
            expect(storiesAfterDeletion.length).toBe(0);
        });
    });

    describe('should not delete stories that do not meet criteria', () => {
        it('should not delete draft stories with attachments', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .attachments([newDbId()])
                                    .plannedPublicationDate(null)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const deletePastDraftStoriesTask = container.resolve(DeletePastDraftStoriesTask);

            // Verify the story exists before deletion
            const storiesBeforeDeletion = await storiesRepository.find({
                filter: { isStory: true, published: PostPublicationStatus.DRAFT },
            });
            expect(storiesBeforeDeletion.length).toBe(1);

            // Execute the task
            await deletePastDraftStoriesTask.execute();

            // Verify the story was NOT deleted (has attachments)
            const storiesAfterDeletion = await storiesRepository.find({
                filter: { isStory: true, published: PostPublicationStatus.DRAFT },
            });
            expect(storiesAfterDeletion.length).toBe(1);
        });

        it('should not delete draft stories with future planned publication date', async () => {
            const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .attachments([])
                                    .plannedPublicationDate(tomorrow)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const deletePastDraftStoriesTask = container.resolve(DeletePastDraftStoriesTask);

            // Verify the story exists before deletion
            const storiesBeforeDeletion = await storiesRepository.find({
                filter: { isStory: true, published: PostPublicationStatus.DRAFT },
            });
            expect(storiesBeforeDeletion.length).toBe(1);

            // Execute the task
            await deletePastDraftStoriesTask.execute();

            // Verify the story was NOT deleted (future planned publication date)
            const storiesAfterDeletion = await storiesRepository.find({
                filter: { isStory: true, published: PostPublicationStatus.DRAFT },
            });
            expect(storiesAfterDeletion.length).toBe(1);
        });

        it('should not delete non-story posts', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .isStory(false)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .attachments([])
                                    .plannedPublicationDate(null)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const deletePastDraftStoriesTask = container.resolve(DeletePastDraftStoriesTask);

            // Verify the post exists before deletion
            const postsBeforeDeletion = await storiesRepository.find({
                filter: { isStory: false, published: PostPublicationStatus.DRAFT },
            });
            expect(postsBeforeDeletion.length).toBe(1);

            // Execute the task
            await deletePastDraftStoriesTask.execute();

            // Verify the post was NOT deleted (not a story)
            const postsAfterDeletion = await storiesRepository.find({
                filter: { isStory: false, published: PostPublicationStatus.DRAFT },
            });
            expect(postsAfterDeletion.length).toBe(1);
        });

        it('should not delete published stories', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .source(PostSource.SOCIAL)
                                    .attachments([])
                                    .plannedPublicationDate(null)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const deletePastDraftStoriesTask = container.resolve(DeletePastDraftStoriesTask);

            // Verify the story exists before deletion
            const storiesBeforeDeletion = await storiesRepository.find({
                filter: { isStory: true, published: PostPublicationStatus.PUBLISHED },
            });
            expect(storiesBeforeDeletion.length).toBe(1);

            // Execute the task
            await deletePastDraftStoriesTask.execute();

            // Verify the story was NOT deleted (published, not draft)
            const storiesAfterDeletion = await storiesRepository.find({
                filter: { isStory: true, published: PostPublicationStatus.PUBLISHED },
            });
            expect(storiesAfterDeletion.length).toBe(1);
        });
    });

    describe('mixed scenarios', () => {
        it('should delete only stories that meet all criteria', async () => {
            const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
            const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [
                                // Should be deleted: draft story, no attachments, no planned date
                                getDefaultPost()
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .attachments([])
                                    .plannedPublicationDate(null)
                                    .build(),
                                // Should be deleted: draft story, null attachments, past planned date
                                getDefaultPost()
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .attachments(undefined)
                                    .plannedPublicationDate(twoDaysAgo)
                                    .build(),
                                // Should NOT be deleted: has attachments
                                getDefaultPost()
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .attachments([newDbId()])
                                    .plannedPublicationDate(null)
                                    .build(),
                                // Should NOT be deleted: future planned date
                                getDefaultPost()
                                    .isStory(true)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .attachments([])
                                    .plannedPublicationDate(tomorrow)
                                    .build(),
                                // Should NOT be deleted: not a story
                                getDefaultPost()
                                    .isStory(false)
                                    .published(PostPublicationStatus.DRAFT)
                                    .source(PostSource.SOCIAL)
                                    .attachments([])
                                    .plannedPublicationDate(null)
                                    .build(),
                                // Should NOT be deleted: published
                                getDefaultPost()
                                    .isStory(true)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .source(PostSource.SOCIAL)
                                    .attachments([])
                                    .plannedPublicationDate(null)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const deletePastDraftStoriesTask = container.resolve(DeletePastDraftStoriesTask);

            // Verify all posts exist before deletion
            const allPostsBeforeDeletion = await storiesRepository.find({ filter: {} });
            expect(allPostsBeforeDeletion.length).toBe(6);

            // Execute the task
            await deletePastDraftStoriesTask.execute();

            // Verify only 2 stories were deleted (the ones that meet all criteria)
            const allPostsAfterDeletion = await storiesRepository.find({ filter: {} });
            expect(allPostsAfterDeletion.length).toBe(4);

            // Verify the correct stories were deleted
            const draftStoriesAfterDeletion = await storiesRepository.find({
                filter: { isStory: true, published: PostPublicationStatus.DRAFT },
            });
            expect(draftStoriesAfterDeletion.length).toBe(2); // 2 should remain (with attachments and future date)
        });

        it('should handle empty database gracefully', async () => {
            const testCase = new TestCaseBuilderV2<'posts'>({
                seeds: {
                    posts: {
                        data() {
                            return [];
                        },
                    },
                },
            });

            await testCase.build();

            const storiesRepository = container.resolve(StoriesRepository);
            const deletePastDraftStoriesTask = container.resolve(DeletePastDraftStoriesTask);

            // Verify no posts exist
            const postsBeforeDeletion = await storiesRepository.find({ filter: {} });
            expect(postsBeforeDeletion.length).toBe(0);

            // Execute the task (should not throw error)
            await expect(deletePastDraftStoriesTask.execute()).resolves.not.toThrow();

            // Verify still no posts exist
            const postsAfterDeletion = await storiesRepository.find({ filter: {} });
            expect(postsAfterDeletion.length).toBe(0);
        });
    });
});
