import { DateTime } from 'luxon';
import { autoInjectable } from 'tsyringe';

import { PostPublicationStatus } from '@malou-io/package-utils';

import { StoriesRepository } from ':modules/stories/repository/stories.repository';

@autoInjectable()
export class DeletePastDraftStoriesTask {
    constructor(private readonly _storiesRepository: StoriesRepository) {}

    async execute() {
        const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();

        const cursor = this._storiesRepository.model
            .find(
                {
                    $and: [
                        { isStory: true },
                        { published: PostPublicationStatus.DRAFT },
                        { $or: [{ attachments: [] }, { attachments: null }] },
                        { $or: [{ plannedPublicationDate: null }, { plannedPublicationDate: { $exists: true, $lt: yesterday } }] },
                    ],
                },
                { _id: 1 },
                { lean: true }
            )
            .cursor();

        let count = 0;

        await cursor.eachAsync(
            async (stories) => {
                await this._storiesRepository.deleteMany({ filter: { _id: { $in: stories.map((story) => story._id) } } });
                count += stories.length;
                console.log('Deleted', count, 'stories');
            },
            { batchSize: 100 }
        );
    }
}
