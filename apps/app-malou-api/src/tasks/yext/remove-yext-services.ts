import 'reflect-metadata';

import ':env';

import { differenceBy } from 'lodash';
import { container, singleton } from 'tsyringe';

import { CountryCode, MalouErrorCode, PlatformKey, waitFor, YextAddRequestStatus } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { YextAccount } from ':modules/publishers/yext/entities/yext-account.entity';
import { YextLocation } from ':modules/publishers/yext/entities/yext-location.entity';
import { YextAccountRepository } from ':modules/publishers/yext/repositories/yext-account.repository';
import { YextLocationRepository } from ':modules/publishers/yext/repositories/yext-location.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';
import { YextProvider } from ':providers/yext/yext.provider';

const YEXT_FOR_FOOD_SKU = '***********';
const YEXT_MENU_LISTING_SKU = 'GLOBAL-********';
const alreadyHandledRestaurants: string[] = [];
const removedRestaurants: string[] = [];

// Check current bill / contract with Yext
// https://www.yext.com/resellers/4180173/agreements/agreementsAndServices/viewAgreement/29160

@singleton()
class RemoveYextServicesTask {
    constructor(
        private readonly _yextProvider: YextProvider,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _yextLocationRepository: YextLocationRepository,
        private readonly _yextAccountRepository: YextAccountRepository
    ) {}

    async execute() {
        const restaurantsWithYextActivated = await this._restaurantsRepository.find({
            filter: {
                isYextActivated: true,
                active: true,
                'address.regionCode': 'US',
            },
            projection: {
                _id: 1,
                organizationId: 1,
                address: 1,
            },
            options: { lean: true },
        });

        if (restaurantsWithYextActivated.length === 0) {
            logger.info('No restaurants with Yext activated');
            return;
        }
        const platforms = await this._platformsRepository.find({
            filter: {
                key: PlatformKey.OPENTABLE,
                restaurantId: { $in: restaurantsWithYextActivated.map((restaurant) => restaurant._id.toString()) },
            },
            projection: {
                _id: 1,
                restaurantId: 1,
            },
            options: { lean: true },
        });
        const restaurantIdsWithOpentable = platforms.map((platform) => ({ _id: platform.restaurantId.toString() }));
        const restaurantsWithoutOpentable = differenceBy(restaurantsWithYextActivated, restaurantIdsWithOpentable, (restaurant) =>
            restaurant._id.toString()
        ).filter((restaurant) => !alreadyHandledRestaurants.includes(restaurant._id?.toString()));

        for (const restaurantWithoutOpentable of restaurantsWithoutOpentable) {
            try {
                const isInUsa = restaurantWithoutOpentable.address?.regionCode === CountryCode.UNITED_STATES;
                const serviceToRemove = isInUsa ? YEXT_MENU_LISTING_SKU : YEXT_FOR_FOOD_SKU;
                const isServiceActivated = await this._checkIfServiceIsActivatedForRestaurant(
                    restaurantWithoutOpentable._id.toString(),
                    serviceToRemove
                );

                if (isServiceActivated) {
                    await this._cancelSkuForRestaurant(restaurantWithoutOpentable._id.toString(), serviceToRemove);
                    removedRestaurants.push(restaurantWithoutOpentable._id.toString());
                    logger.info('Service removed for restaurant !', {
                        restaurantId: restaurantWithoutOpentable._id.toString(),
                        service: serviceToRemove,
                    });

                    // To stay above 7500$ / month for Yext
                    // if (removedRestaurantsCount >= this._MAX_RESTAURANTS_TO_REMOVE) {
                    //     logger.info('Max number of restaurants reached, stopping the task.');
                    //     break;
                    // }
                } else {
                    logger.info('Service already removed for restaurant, skipping.', {
                        restaurantId: restaurantWithoutOpentable._id.toString(),
                        service: serviceToRemove,
                    });
                }

                alreadyHandledRestaurants.push(restaurantWithoutOpentable._id.toString());
                await waitFor(300);
            } catch (error) {
                logger.error('Error while cancelling service', error);
                continue;
            }
        }

        logger.info('Service removal task completed', {
            removedRestaurantsCount: removedRestaurants.length,
            removedRestaurants,
            alreadyHandledRestaurants,
        });
    }

    private async _cancelSkuForRestaurant(restaurantId: string, sku: string): Promise<void> {
        const { yextAccount, yextLocation } = await this._getYextAccountAndLocation(restaurantId);
        const yextLocationId = yextLocation.partnerLocationId;
        const yextAccountId = yextAccount.partnerAccountId;
        await this._yextProvider.cancelServicesForLocation(yextAccountId, yextLocationId, [sku]);
    }

    private async _checkIfServiceIsActivatedForRestaurant(restaurantId: string, sku: string): Promise<boolean> {
        try {
            const { yextLocation } = await this._getYextAccountAndLocation(restaurantId);
            const services = await this._yextProvider.getLocationServices(yextLocation);
            return services.response.services.some((service) => service.sku === sku);
        } catch (error) {
            logger.error('Error while checking if service is activated for restaurant', error);
            return false;
        }
    }

    private async _getYextAccountAndLocation(restaurantId: string): Promise<{ yextAccount: YextAccount; yextLocation: YextLocation }> {
        const yextLocation = await this._yextLocationRepository.getYextLocationByRestaurantId(restaurantId);

        if (!yextLocation) {
            throw new MalouError(MalouErrorCode.YEXT_LOCATION_NOT_FOUND, {
                message: 'Cannot delete location : yext location not found',
                metadata: { restaurantId },
            });
        }

        if (
            [YextAddRequestStatus.SUBMITTED, YextAddRequestStatus.PROCESSING, YextAddRequestStatus.REVIEW].includes(
                yextLocation.addRequestStatus
            )
        ) {
            throw new MalouError(MalouErrorCode.YEXT_CANT_DELETE_BECAUSE_OF_ADD_REQUEST_STATUS, {
                message: 'Cannot delete location : add request status is pending',
                metadata: { restaurantId, addRequestStatus: yextLocation.addRequestStatus },
            });
        }

        const yextAccount = await this._yextAccountRepository.getById(yextLocation.yextAccountId);
        if (!yextAccount) {
            throw new MalouError(MalouErrorCode.YEXT_ACCOUNT_NOT_FOUND, {
                message: 'Cannot delete location: yext account not found',
                metadata: { restaurantId },
            });
        }
        return { yextAccount, yextLocation };
    }
}

const task = container.resolve(RemoveYextServicesTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
