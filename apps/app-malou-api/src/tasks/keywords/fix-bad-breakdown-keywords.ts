import 'reflect-metadata';

import ':env';

import fs from 'fs';
import { chunk, groupBy } from 'lodash';
import path from 'path';
import { container, singleton } from 'tsyringe';

import { IKeyword, toDbId } from '@malou-io/package-models';
import { ApplicationLanguage, filterByRequiredKeys, IBreakdown, mapLanguageStringToApplicationLanguage } from '@malou-io/package-utils';

import { BreakdownAndClassifyKeywordsUseCase } from ':modules/ai/use-cases';
import { Breakdown } from ':modules/keywords/entities/breakdown.entity';
import { KeywordsTempRepository } from ':modules/keywords/keywords-temp.repository';
import { TranslateKeywordsBreakdownUseCase } from ':modules/keywords/use-cases/translate-keywords-breakdown/translate-keywords-breakdown.use-case';
import { RestaurantKeywordsRepository } from ':modules/restaurant-keywords/restaurant-keywords.repository';
import { Translations } from ':modules/translations/entities/translations.entity';
import ':plugins/db';

@singleton()
export class FixBadBreakdownKeywords {
    readonly START_LAMBDA_DATE = new Date('2025-01-08');
    readonly MIN_KEYWORDS_CHUNK_TO_BREAKDOWN = 5;
    readonly USER_ID = '5fe31dcc23d1211966d31cb4';

    constructor(
        private readonly _keywordTempRepo: KeywordsTempRepository,
        private readonly _restaurantKeywordRepo: RestaurantKeywordsRepository,
        private readonly _breakdownAndClassifyKeywordsUseCase: BreakdownAndClassifyKeywordsUseCase,
        private readonly _translateKeywordsBreakdownUseCase: TranslateKeywordsBreakdownUseCase
    ) {}

    async execute() {
        const keywordsWithSingleBrick = await this._keywordTempRepo.find({
            filter: {
                createdAt: { $gte: this.START_LAMBDA_DATE },
                bricks: { $size: 1 },
            },
            options: {
                lean: true,
            },
            projection: { _id: true, text: true, bricks: true },
        });

        const keywordsWithBadBreakdown = keywordsWithSingleBrick.filter((k) => {
            const [brick] = k.bricks;
            return brick.text !== k.text;
        });

        const keywordIds = keywordsWithBadBreakdown.map((k) => k._id);

        fs.writeFileSync(path.join(__dirname, 'keywordIds.json'), JSON.stringify(keywordIds, null, 2));

        const restaurantKeywords = await this._restaurantKeywordRepo.find({
            filter: { keywordId: { $in: keywordIds } },
            options: { lean: true, populate: [{ path: 'keyword', select: { _id: true, text: true, bricks: true, language: true } }] },
        });

        const groupedByRestaurantId = groupBy(restaurantKeywords, (rk) => rk.restaurantId.toString());

        for (const [restaurantId, restaurantKywds] of Object.entries(groupedByRestaurantId)) {
            const keywords = restaurantKywds.map((rk) => rk.keyword);
            await this._fixBreakdownAndTranslationForKeywords(keywords, restaurantId);
            console.log('breakdown and translation fixed for restaurantId :>>', restaurantId);
        }
    }

    private async _fixBreakdownAndTranslationForKeywords(
        keywords: Pick<IKeyword, '_id' | 'bricks' | 'language' | 'text'>[],
        restaurantId: string
    ): Promise<void> {
        const keywordsGroupedByLang = groupBy(keywords, 'language');
        for (const [lang, langKeywords] of Object.entries(keywordsGroupedByLang)) {
            const keywordTexts = langKeywords.map((keyword) => keyword.text);
            const keywordChunks = chunk(keywordTexts, this.MIN_KEYWORDS_CHUNK_TO_BREAKDOWN);

            for (const keywordChunk of keywordChunks) {
                const breakdownRes = await this._breakdownAndClassifyKeywordsUseCase
                    .execute(keywordChunk, restaurantId, lang, this.USER_ID)
                    .then((breakdownResults) => breakdownResults.map((breakdownKeyword) => ({ ...breakdownKeyword, language: lang })));

                const keywordBreakdowns = breakdownRes.map((kw) => ({
                    id: langKeywords.find((keyword) => keyword.text === kw.keyword)?._id.toString(),
                    text: kw.keyword,
                    language: kw.language,
                    bricks: kw.bricks?.map((brick) => ({ text: brick.text, category: brick.category })) ?? [],
                }));

                const translatedBreakdowns = await this._translateKeywordsBreakdownUseCase.execute([
                    {
                        breakdowns: keywordBreakdowns.map((kw) => kw.bricks).flat(),
                        language: mapLanguageStringToApplicationLanguage(lang),
                    },
                ]);

                keywordBreakdowns.forEach((keyword) => {
                    keyword.bricks.forEach((brick, index) => {
                        const translatedBreakdown = this._findCorrectTranslatedBreakdowns(
                            translatedBreakdowns,
                            new Breakdown(brick),
                            mapLanguageStringToApplicationLanguage(keyword.language)
                        );

                        if (translatedBreakdown) {
                            keyword.bricks[index] = translatedBreakdown;
                        }
                    });
                });

                const promises = filterByRequiredKeys(keywordBreakdowns, ['id']).map((keyword) => {
                    const updatedBricks = keyword.bricks.map((brick) => ({
                        text: brick.text,
                        category: brick.category,
                    }));

                    return this._keywordTempRepo.findOneAndUpdate({
                        filter: { _id: toDbId(keyword.id) },
                        update: { bricks: updatedBricks },
                    });
                    return;
                });

                await Promise.all(promises);
            }
        }
    }

    private _findCorrectTranslatedBreakdowns(
        translatedBreakdowns: IBreakdown[],
        originalBreakdown: Omit<Breakdown, 'translations'>,
        language: ApplicationLanguage
    ): Breakdown | undefined {
        const correctTranslatedBreakdown = translatedBreakdowns.find(
            (translatedBreakdown) =>
                originalBreakdown.text === translatedBreakdown.text && language === translatedBreakdown.translations?.language
        );

        return correctTranslatedBreakdown && correctTranslatedBreakdown.translations
            ? new Breakdown({
                  text: correctTranslatedBreakdown.text,
                  category: correctTranslatedBreakdown.category,
                  translations: new Translations({
                      id: correctTranslatedBreakdown.translations.id,
                      fr: correctTranslatedBreakdown.translations.fr,
                      en: correctTranslatedBreakdown.translations.en,
                      es: correctTranslatedBreakdown.translations.es,
                      it: correctTranslatedBreakdown.translations.it,
                      language: correctTranslatedBreakdown.translations.language,
                      source: correctTranslatedBreakdown.translations.source,
                  }),
              })
            : undefined;
    }
}

const task = container.resolve(FixBadBreakdownKeywords);

task.execute()
    .then(() => process.exit(0))
    .catch((error) => {
        console.log('error :>>', error);
        process.exit(1);
    });
