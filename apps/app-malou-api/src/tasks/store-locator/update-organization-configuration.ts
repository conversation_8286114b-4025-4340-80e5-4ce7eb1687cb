import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { StoreLocatorCommonElementIds } from '@malou-io/package-utils';

import { GenerateTailwindConfigurationService } from ':modules/store-locator/services/generate-tailwind-configuration/generate-tailwind-configuration.service';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';

@singleton()
class CreateOrganizationConfigurationTask {
    constructor(
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _generateTailwindConfigurationService: GenerateTailwindConfigurationService
    ) {}

    async execute(): Promise<void> {
        await this._storeLocatorOrganizationConfigRepository.updateMany({
            filter: {},
            update: { 'styles.pages.common': {} },
        });
        await this._handleBolkiriConfiguration();
    }

    private async _handleBolkiriConfiguration(): Promise<void> {
        await this._storeLocatorOrganizationConfigRepository.updateOne({
            filter: { organizationId: toDbId('67cf1ef531d778287af0d2ef') },
            update: {
                'styles.pages.common': {
                    [StoreLocatorCommonElementIds.WHITE_MARK_WRAPPER]: ['font-secondary', 'bg-tertiary', 'text-white'],
                    [StoreLocatorCommonElementIds.WHITE_MARK_LOGO]: ['fill-white'],
                },
            },
        });
    }
}

const task = container.resolve(CreateOrganizationConfigurationTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
