import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { filterByRequiredKeys, isFromDomain, isNotNil, PlatformKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';
import { GmbPlaceActionsProvider } from ':providers/google/gmb.place-actions.provider';
import { GmbRefreshTokenService } from ':services/credentials/gmb/gmb-refresh-token.service';

@singleton()
class RemoveDeliverooUrlsTask {
    constructor(
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _gmbRefreshTokenService: GmbRefreshTokenService,
        private readonly _gmbPlaceActionsProvider: GmbPlaceActionsProvider
    ) {}

    async execute(): Promise<void> {
        const storeLocatorPages = await this._storeLocatorRestaurantPageRepository.find({
            filter: {},
            options: { lean: true },
        });

        for (const storeLocatorPage of storeLocatorPages) {
            await this._removeActionLinks({ restaurantId: storeLocatorPage.restaurantId.toString() });
        }

        const hasDeliverooOutsideCtas = storeLocatorPages.some(({ blocks }) => {
            const doesInformationBlockCtasUrlsIncludeDeliveroo = blocks?.information?.ctas?.some(({ url }) =>
                url?.toLowerCase()?.includes('deliveroo')
            );
            return doesInformationBlockCtasUrlsIncludeDeliveroo || blocks?.reviews?.cta?.url?.toLowerCase()?.includes('deliveroo');
        });
        if (hasDeliverooOutsideCtas) {
            logger.error('Deliveroo URLs found outside CTAs');
            return;
        }

        const hasDeliverooInsideCtas = storeLocatorPages.filter(({ blocks }) =>
            blocks?.callToActions?.ctas?.some(({ url }) => isFromDomain({ url, domain: 'deliveroo' }))
        );
        for (const storeLocatorPage of hasDeliverooInsideCtas) {
            const updatedCtas = storeLocatorPage.blocks.callToActions.ctas
                .map((cta) => {
                    if (isFromDomain({ url: cta.url, domain: 'deliveroo' })) {
                        logger.info('Removing Deliveroo URL from CTA:', { url: cta.url, restaurantId: storeLocatorPage.restaurantId });
                        return undefined; // Remove Deliveroo URL
                    }

                    return cta;
                })
                .filter(isNotNil);

            const hasStillDeliveroo = updatedCtas.some(({ url }) => url?.toLowerCase()?.includes('deliveroo'));
            if (hasStillDeliveroo) {
                logger.error('Deliveroo URLs found outside CTAs', { restaurantId: storeLocatorPage.restaurantId });
            }

            logger.info('Updated CTAs for page:', { restaurantId: storeLocatorPage.restaurantId, ctas: updatedCtas });

            await this._storeLocatorRestaurantPageRepository.updateOne({
                filter: { _id: storeLocatorPage._id },
                update: { 'blocks.callToActions.ctas': updatedCtas },
            });
        }
    }

    private async _removeActionLinks({ restaurantId }: { restaurantId: string }): Promise<void> {
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.GMB);

        if (!platform) {
            logger.error('[GMB PUBLISH] Platform not found', { restaurantId });
            return;
        }

        const { credentials, apiEndpointV2, apiEndpoint } = platform;
        const credentialId = credentials?.[0];
        if (!credentialId || !apiEndpointV2 || !apiEndpoint) {
            logger.error('[GMB PUBLISH] No platform credential', { restaurantId, platformId: platform._id });
            return;
        }

        const locationId = apiEndpointV2.replace('locations/', '');
        const { accessToken } = await this._gmbRefreshTokenService.getFreshTokenIfNecessary(credentialId);

        const {
            data: { placeActionLinks },
        } = await this._gmbPlaceActionsProvider.listPlaceActionLinks({
            accessToken,
            locationId,
        });
        if (!placeActionLinks || placeActionLinks.length === 0) {
            logger.error('[GMB PUBLISH] No place action links found', { restaurantId });
            return;
        }

        const deliverooLinks = filterByRequiredKeys(placeActionLinks, ['uri', 'name']).filter(({ uri }) =>
            isFromDomain({ url: uri, domain: 'deliveroo' })
        );
        await Promise.all(
            deliverooLinks.map(async ({ name }) => {
                try {
                    const placeActionLinkId = name.replace(`locations/${locationId}/placeActionLinks/`, '');
                    await this._gmbPlaceActionsProvider.deletePlaceActionLink({
                        accessToken,
                        locationId,
                        placeActionLinkId,
                    });
                } catch (error) {
                    logger.error('[GMB PUBLISH] Failed to delete place action link', {
                        restaurantId,
                        placeActionLinkId: name,
                        error,
                    });
                }
            })
        );
    }
}

const task = container.resolve(RemoveDeliverooUrlsTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
