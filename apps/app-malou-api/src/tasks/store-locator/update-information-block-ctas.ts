import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
class UpdateInformationBlockCtasTask {
    constructor(private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository) {}

    async execute(): Promise<void> {
        const storeLocatorPages = await this._storeLocatorRestaurantPageRepository.find({
            filter: {},
            options: { lean: true },
        });

        await Promise.all(
            storeLocatorPages.map((storeLocatorPage: any) =>
                this._storeLocatorRestaurantPageRepository.updateOne({
                    filter: { _id: storeLocatorPage._id },
                    update: { 'blocks.information.ctas': [storeLocatorPage.blocks.information.cta] },
                })
            )
        );
    }
}

const task = container.resolve(UpdateInformationBlockCtasTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
