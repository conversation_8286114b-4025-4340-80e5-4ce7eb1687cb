import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
class UpdateDescriptionsBlockTask {
    constructor(private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository) {}

    async execute(): Promise<void> {
        const storeLocatorPages = await this._storeLocatorRestaurantPageRepository.find({
            filter: {},
            options: { lean: true },
        });

        await Promise.all(
            storeLocatorPages.map((storeLocatorPage) =>
                this._storeLocatorRestaurantPageRepository.updateOne({
                    filter: { _id: storeLocatorPage._id },
                    update: { 'blocks.descriptions': { items: storeLocatorPage.blocks.descriptions } },
                })
            )
        );
    }
}

const task = container.resolve(UpdateDescriptionsBlockTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
