import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { StoreLocatorAiSettingsLanguageStyle, StoreLocatorLanguage } from '@malou-io/package-utils';

import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';

@singleton()
export class OrganizationConfigurationMigrationTask {
    private readonly DEFAULT_AI_SETTINGS = {
        tone: [],
        languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
        attributeIds: [],
        restaurantKeywords: [],
        specialAttributes: [],
    };
    private readonly DEFAULT_LANGUAGES = {
        primary: StoreLocatorLanguage.UNDETERMINED,
        secondary: [],
    };
    constructor(private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository) {}

    async execute(): Promise<void> {
        const configurations = await this._storeLocatorOrganizationConfigRepository.find({
            filter: {},
            options: { lean: true },
        });

        for (const config of configurations) {
            if (config.aiSettings && config.languages) {
                continue;
            }

            const updateData: any = {};

            if (!config.aiSettings) {
                updateData.aiSettings = this.DEFAULT_AI_SETTINGS;
            }

            if (!config.languages) {
                updateData.languages = this.DEFAULT_LANGUAGES;
            }

            await this._storeLocatorOrganizationConfigRepository.updateOne({
                filter: { organizationId: config.organizationId },
                update: updateData,
            });
        }
    }
}

const task = container.resolve(OrganizationConfigurationMigrationTask);

task.execute()
    .then(() => {
        console.log('Organization configuration migration task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error during organization configuration migration task:', error);
        process.exit(1);
    });
