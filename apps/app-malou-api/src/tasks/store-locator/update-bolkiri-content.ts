import 'reflect-metadata';

import ':env';

import assert from 'node:assert/strict';
import { container, singleton } from 'tsyringe';

import { IStoreLocatorRestaurantPage } from '@malou-io/package-models';
import { cleanUrl, filterByRequiredKeys, StoreLocatorPageStatus } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';
import { GoogleSheetsService } from ':services/google-sheets/google-sheets.service';

@singleton()
class UpdateBolkiriContent {
    private readonly _GSHEET_ID = '1qzedWigeqs1CEs7YkHq0Hoj7pJEKJ--KLlvUDMeaI6w';

    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _googleSheetsService: GoogleSheetsService
    ) {}

    async execute(): Promise<void> {
        const googleSheet = await this._googleSheetsService.loadGoogleSheet(this._GSHEET_ID);
        const workSheet = googleSheet.sheetsByTitle['Correction (à éditer)'];

        await workSheet.loadCells('A1:AP100'); // loads range of cells into local cache - DOES NOT RETURN THE CELLS

        let column = 1;
        let restaurantName = workSheet.getCell(0, column).value.toString();

        while (restaurantName) {
            column++;
            restaurantName = workSheet.getCell(0, column).value?.toString();
            if (!restaurantName) {
                break;
            }

            const restaurant = await this._restaurantsRepository.findOne({
                filter: { name: { $regex: RegExp(restaurantName, 'i') } },
                projection: { _id: 1 },
                options: { lean: true },
            });
            assert(restaurant);

            const storeLocatorRestaurantPage = await this._storeLocatorRestaurantPageRepository.findOne({
                filter: { restaurantId: restaurant._id, status: StoreLocatorPageStatus.PUBLISHED },
                options: { lean: true },
            });
            assert(storeLocatorRestaurantPage);

            const ctaTitle = workSheet.getCell(13, column).value.toString() as string;
            const ctas = this._getCtas(workSheet.getCell(14, column).value.toString() as string);

            const updatedCallToActionsBlock: IStoreLocatorRestaurantPage['blocks']['callToActions'] = {
                ...storeLocatorRestaurantPage.blocks.callToActions,
                title: ctaTitle,
                ctas,
            };

            await this._storeLocatorRestaurantPageRepository.updateOne({
                filter: { _id: storeLocatorRestaurantPage._id },
                update: {
                    'blocks.callToActions': updatedCallToActionsBlock,
                },
            });
        }
    }

    private _getCtas(rawCtas: string) {
        if (!rawCtas) {
            return [];
        }

        const ctas = rawCtas.split('\n\n');

        return filterByRequiredKeys(
            ctas.map((cta) => {
                const [text, url] = cta.trim().split('\n');
                logger.info('CTAS', { text, url });

                return {
                    text: text.trim(),
                    url: cleanUrl(url.trim()),
                };
            }),
            ['text', 'url']
        );
    }
}

const task = container.resolve(UpdateBolkiriContent);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
