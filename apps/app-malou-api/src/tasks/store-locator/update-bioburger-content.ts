import 'reflect-metadata';

import ':env';

import assert from 'node:assert/strict';
import { container, singleton } from 'tsyringe';

import { StoreLocatorPageStatus } from '@malou-io/package-utils';

import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';
import { GoogleSheetsService } from ':services/google-sheets/google-sheets.service';

@singleton()
class UpdateBioBurgerContent {
    private readonly _GSHEET_ID = '1nNQs5BzjKEMxil3nHp9Vi1tsIM9JJgicUWtzqOoUJtw';

    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _googleSheetsService: GoogleSheetsService
    ) {}

    async execute(): Promise<void> {
        const googleSheet = await this._googleSheetsService.loadGoogleSheet(this._GSHEET_ID);
        const workSheet = googleSheet.sheetsByTitle['Correction (à éditer)'];

        await workSheet.loadCells('A1:AP100'); // loads range of cells into local cache - DOES NOT RETURN THE CELLS

        let column = 1;
        let restaurantName = workSheet.getCell(0, column).value.toString();

        while (restaurantName) {
            column++;
            restaurantName = workSheet.getCell(0, column).value?.toString();
            if (!restaurantName) {
                break;
            }

            const restaurant = await this._restaurantsRepository.findOne({
                filter: { name: { $regex: RegExp(restaurantName, 'i') } },
                projection: { _id: 1 },
                options: { lean: true },
            });
            assert(restaurant);

            const storeLocatorRestaurantPage = await this._storeLocatorRestaurantPageRepository.findOne({
                filter: { restaurantId: restaurant._id, status: StoreLocatorPageStatus.PUBLISHED },
                options: { lean: true },
            });
            assert(storeLocatorRestaurantPage);

            const updatedHeadBlock = {
                ...storeLocatorRestaurantPage.blocks.head,
                description: workSheet.getCell(4, column).value.toString() as string,
                twitterDescription: workSheet.getCell(6, column).value.toString() as string,
            };
            const updatedGalleryBlock = {
                ...storeLocatorRestaurantPage.blocks.gallery,
                title: workSheet.getCell(7, column).value.toString() as string,
                subtitle: workSheet.getCell(8, column).value.toString() as string,
            };
            const updatedReviewsBlock = {
                ...storeLocatorRestaurantPage.blocks.reviews,
                title: workSheet.getCell(9, column).value.toString() as string,
            };
            const updatedSocialNetworks = {
                ...storeLocatorRestaurantPage.blocks.socialNetworks,
                title: workSheet.getCell(10, column).value.toString() as string,
            };
            const updatedDescriptions = this._getDescriptions(workSheet.getCell(11, column).value.toString());
            const updatedDescriptionBlock = {
                ...storeLocatorRestaurantPage.blocks.descriptions,
                items: updatedDescriptions.items.map((desc, index) => ({
                    title: desc.title,
                    image: storeLocatorRestaurantPage.blocks.descriptions.items[index].image,
                    blocks: desc.blocks,
                })),
            };

            await this._storeLocatorRestaurantPageRepository.updateOne({
                filter: { _id: storeLocatorRestaurantPage._id },
                update: {
                    'blocks.descriptions': updatedDescriptionBlock,
                    'blocks.head': updatedHeadBlock,
                    'blocks.gallery': updatedGalleryBlock,
                    'blocks.socialNetworks': updatedSocialNetworks,
                    'blocks.reviews': updatedReviewsBlock,
                },
            });
        }
    }

    private _getDescriptions(descs: string): { items: { title: string; blocks: { title: string; text: string }[] }[] } {
        const descriptions = descs.replaceAll('\n', '');
        const descriptionsArray = descriptions.split('############');

        return {
            items: descriptionsArray.map((desc, index) => {
                const [title, blocsRaw] = desc.trim().split('########');
                const blocks = blocsRaw
                    .trim()
                    .split('######')
                    .map((b) => {
                        const [titleBloc, text] = b.split('####');
                        return {
                            title: titleBloc.trim(),
                            text: text.trim(),
                        };
                    });

                return {
                    title: title.trim(),
                    blocks,
                };
            }),
        };
    }
}

const task = container.resolve(UpdateBioBurgerContent);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
