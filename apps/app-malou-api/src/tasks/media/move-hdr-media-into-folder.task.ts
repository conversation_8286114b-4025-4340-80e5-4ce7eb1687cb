import 'reflect-metadata';

import ':env';

import ffprobe from 'ffprobe';
import prompts from 'prompts';
import { autoInjectable, container } from 'tsyringe';

import { MediaModel, toDbId } from '@malou-io/package-models';

import { MediasRepository } from ':modules/media/medias.repository';

const data: { restaurantId: string; folderId: string }[] = [
    // { restaurantId: '67483bb0cab9718f64397ab5', folderId: '688b72d28614597656e8ef09' },
    // { restaurantId: '677f9f9a05e6ea52c7d75904', folderId: '688b72458614597656e8ce38' },
    // { restaurantId: '67483afdc2f485f57d669dba', folderId: '688b728a8614597656e8e03e' },
    // { restaurantId: '67483b38bf2efe03a2378dfb', folderId: '688b72b18614597656e8e7a4' },
    // { restaurantId: '67483bd3bf2efe03a237af9f', folderId: '688b73248614597656e90bf0' },
];

@autoInjectable()
class MoveHdrMediaIntoFolderTask {
    constructor(private readonly _mediaRepository: MediasRepository) {}

    async execute() {
        for (const { restaurantId, folderId } of data) {
            await this._moveHdrMediaIntoFolder(restaurantId, folderId);
        }
    }

    private async _moveHdrMediaIntoFolder(restaurantId: string, folderId: string) {
        const filter = {
            restaurantId: toDbId(restaurantId),
            type: 'video',
        };

        const count = await this._mediaRepository.countDocuments({ filter });

        const response = await prompts({
            type: 'confirm',
            name: 'value',
            message: `About to check ${count} medias`,
            initial: false,
        });

        if (!response.value) {
            console.log('exit...');
            process.exit(0);
        }

        const mediasCursor = await MediaModel.find(filter, '', { lean: true }).cursor();

        let i = 0;
        let failedCount = 0;
        for await (const media of mediasCursor) {
            i++;
            const url = media.storedObjects?.original.publicUrl ?? media.urls.original;
            if (!url) {
                continue;
            }
            try {
                const meta = await ffprobe(url, { path: 'ffprobe' });
                const videoStream = meta.streams.find((stream) => stream.codec_type === 'video');
                if (!videoStream) {
                    continue;
                }
                const { color_space, color_transfer, color_primaries } = videoStream;
                if (color_space === 'bt709') {
                    continue;
                }
                if (color_space === 'bt2020nc' && color_transfer === 'arib-std-b67' && color_primaries === 'bt2020') {
                    await this._mediaRepository.updateOne({
                        filter: { _id: media._id },
                        update: { folderId },
                    });
                    continue;
                }
                console.log('Color Metadata:', media._id.toString(), url);
                console.log('  color_space:', color_space, 'color_transfer:', color_transfer, 'color_primaries:', color_primaries);
            } catch (error) {
                failedCount++;
                console.log('error', media._id.toString(), error);
            }
        }
        console.log('failedCount', failedCount);
        console.log('processed', i);
    }
}

const task = container.resolve(MoveHdrMediaIntoFolderTask);

task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
