import 'reflect-metadata';

import ':env';

import assert from 'node:assert/strict';
import { container, singleton } from 'tsyringe';

import { SegmentAnalysisParentTopicModel } from '@malou-io/package-models';
import { filterByRequiredKeys } from '@malou-io/package-utils';

import { SegmentAnalysesRepository } from ':modules/segment-analyses/segment-analyses.repository';
import { SegmentAnalysisParentTopicsRepository } from ':modules/segment-analysis-parent-topics/segment-analysis-parent-topics.repository';
import ':plugins/db';

@singleton()
class LinkSegmentToParentTask {
    constructor(
        private readonly _segmentAnalysesRepository: SegmentAnalysesRepository,
        private readonly _segmentAnalysisParentTopicsRepository: SegmentAnalysisParentTopicsRepository
    ) {}

    async execute() {
        console.log('Starting LinkSegmentToParentTask...');
        const parentCursor = SegmentAnalysisParentTopicModel.find({
            lastLinkedAt: { $exists: false },
        }).cursor({ batchSize: 100, lean: true });
        await parentCursor.eachAsync(
            async (parentTopic) => {
                const linkedSegmentAnalyses = await this._segmentAnalysesRepository.findBySegmentAnalysisParentTopicId(
                    parentTopic._id.toString()
                );
                if (linkedSegmentAnalyses.length > 0) {
                    const oldestSegmentAnalysis = filterByRequiredKeys(linkedSegmentAnalyses, ['reviewSocialCreatedAt'])
                        .sort((a, b) => a.reviewSocialCreatedAt.getTime() - b.reviewSocialCreatedAt.getTime())
                        .pop();
                    assert(oldestSegmentAnalysis, 'No segment analysis found for parent topic');
                    await this._segmentAnalysisParentTopicsRepository.updateOne({
                        filter: { _id: parentTopic._id },
                        update: {
                            lastLinkedAt: oldestSegmentAnalysis.reviewSocialCreatedAt,
                            isAiVisible: true,
                        },
                    });
                }
            },
            { parallel: 10 }
        );
        console.log('LinkSegmentToParentTask completed successfully.');
    }
}

const task = container.resolve(LinkSegmentToParentTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
