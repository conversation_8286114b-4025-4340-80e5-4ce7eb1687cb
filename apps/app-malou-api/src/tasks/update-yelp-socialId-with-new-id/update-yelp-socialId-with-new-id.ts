import 'reflect-metadata';

import ':env';

import { writeFile } from 'fs/promises';
import { chunk } from 'lodash';
import path from 'path';
import { container } from 'tsyringe';

import { IPlatform, PlatformModel } from '@malou-io/package-models';
import { filterByRequiredKeys, PlatformKey, waitFor } from '@malou-io/package-utils';

import { YelpCredentialsUseCases } from ':modules/credentials/platforms/yelp/use-cases';
import ':plugins/db';

import prompts = require('prompts');

const yelpCredentialsUseCases: YelpCredentialsUseCases = container.resolve(YelpCredentialsUseCases);

async function main() {
    const filters = {
        key: PlatformKey.YELP,
    };

    const count: number = await PlatformModel.countDocuments(filters);

    const response = await prompts({
        type: 'confirm',
        name: 'value',
        message: `About to update ${count} platforms`,
        initial: false,
    });

    if (!response.value) {
        console.log('exit...');
        process.exit(0);
    }

    const allBulkOperations: any[] = [];

    const platformsDb: IPlatform[] = await PlatformModel.find(filters, {}, { lean: true, limit: 50 });
    const chunkedPlatforms = chunk(platformsDb, 5);
    let errors: string = '';
    for (const platforms of chunkedPlatforms) {
        const promises = filterByRequiredKeys(platforms, ['socialId']).map((e) => yelpCredentialsUseCases.businessDetails(e.socialId));
        const results = await Promise.allSettled(promises);

        const errorsString = results
            .filter((e): e is PromiseRejectedResult => e.status === 'rejected')
            .map((e) => e?.reason?.message)
            .join('\n');
        if (errorsString.length) {
            errors += errorsString + '\n';
        }

        const fulfilledResults = results
            .filter(
                (
                    result
                ): result is PromiseFulfilledResult<{
                    id: string;
                    alias: string;
                    rating: number;
                }> => result.status === 'fulfilled'
            )
            .map((result) => result.value);

        const bulkOperations = fulfilledResults.map((value) => ({
            updateOne: {
                filter: {
                    key: PlatformKey.YELP,
                    socialId: value.alias,
                },
                update: {
                    socialId: value.id,
                },
            },
        }));
        allBulkOperations.push(...bulkOperations);

        await PlatformModel.bulkWrite(bulkOperations, { ordered: false });
        await waitFor(500);
    }

    await writeFile(path.join(__dirname, 'bulk-operations.json'), JSON.stringify(allBulkOperations, null, 2) + '\n');
    await writeFile(path.join(__dirname, 'errors.json'), errors);
}

main()
    .then(() => process.exit(0))
    .catch((e) => {
        console.error('e :>> ', e);
        process.exit(1);
    });
