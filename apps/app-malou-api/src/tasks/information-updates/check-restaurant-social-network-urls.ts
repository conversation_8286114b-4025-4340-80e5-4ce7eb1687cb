import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { checkSocialNetworkUrl, filterByRequiredKeys } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';

// Check current restaurant urls because new validation can trigger new errors
@singleton()
class CheckRestaurantSocialNetworkUrlsTask {
    constructor(private readonly _restaurantsRepository: RestaurantsRepository) {}

    async execute() {
        const restaurants = await this._restaurantsRepository.find({
            filter: {
                active: true,
            },
            options: {
                lean: true,
            },
            projection: {
                socialNetworkUrls: 1,
            },
        });

        logger.info(`Checking ${restaurants.length} restaurants`);

        filterByRequiredKeys(restaurants, ['socialNetworkUrls'])
            .filter((restaurant) => restaurant.socialNetworkUrls?.length > 0)
            .forEach((restaurant) => {
                restaurant.socialNetworkUrls.forEach(({ key, url }) => {
                    if (!checkSocialNetworkUrl(url, key)) {
                        logger.error('Invalid social network urls', restaurant._id.toString(), url);
                    }
                });
            });
    }
}

const checkRestaurantSocialNetworkUrlsTask = container.resolve(CheckRestaurantSocialNetworkUrlsTask);
checkRestaurantSocialNetworkUrlsTask
    .execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
