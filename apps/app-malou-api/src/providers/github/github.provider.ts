import { createAppAuth } from '@octokit/auth-app';
import { AuthInterface } from '@octokit/auth-app/dist-types/types';
import axios, { AxiosInstance } from 'axios';
import { singleton } from 'tsyringe';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import { GithubWorkflowInputData, GithubWorkflowName } from ':providers/github/github.interfaces';

@singleton()
export class GithubProvider {
    private readonly _axiosInstance: AxiosInstance;
    private readonly _appAuth: AuthInterface;

    constructor() {
        // We want to connect through the Github App "malou-api" that we created in Github
        this._appAuth = createAppAuth({
            appId: Config.services.github.appId,
            privateKey: Config.services.github.privateKey?.replaceAll('||n||', '\n'),
            clientId: Config.services.github.clientId,
            clientSecret: Config.services.github.clientSecret,
        });

        this._axiosInstance = axios.create({
            baseURL: `https://api.github.com/repos/malou-io/malou-app`,
            headers: {
                Accept: 'application/vnd.github.v3+json',
            },
        });
    }

    // Connect as the Github App "malou-api" that we created in Github
    // Type 'installation' indicates that we're using the app's installation in the malou-app repo
    // https://github.com/octokit/auth-app.js?tab=readme-ov-file#authenticate-as-installation
    private async _getToken() {
        const { token } = await this._appAuth({
            type: 'installation',
            installationId: Config.services.github.installationId,
        });

        return token;
    }

    async triggerWorkflow<T extends GithubWorkflowName>({
        workflowName,
        inputs,
    }: {
        workflowName: T;
        inputs?: GithubWorkflowInputData[T];
    }): Promise<void> {
        const token = await this._getToken();

        await this._axiosInstance.post(
            `/actions/workflows/${workflowName}/dispatches`,
            {
                ref: Config.branchName || 'main',
                ...(inputs && { inputs }),
            },
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            }
        );
    }

    async cancelWorkflowRun({ runId }: { runId: string }): Promise<void> {
        try {
            const token = await this._getToken();

            await this._axiosInstance.post(
                `/actions/runs/${runId}/cancel`,
                {},
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                }
            );
        } catch (err) {
            if (axios.isAxiosError(err)) {
                if (err.response?.status === 409) {
                    logger.warn(
                        `[GITHUB] [Cancel workflow run] Can't cancel workflow run, it might have already been canceled or completed.`
                    );
                    return;
                }
            }

            logger.error('[GITHUB] [Cancel workflow run] Error canceling workflow run', {
                runId,
                err: (err as Error).message,
            });
            throw err;
        }
    }
}
