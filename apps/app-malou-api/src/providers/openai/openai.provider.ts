import { Configuration, CreateChatCompletionRequest, OpenAIApi } from 'openai';
import { singleton } from 'tsyringe';

import { filterByRequiredKeys, isNotNil, OpenaiModelVersion } from '@malou-io/package-utils';

import { Config } from ':config';

@singleton()
export default class OpenAiProvider {
    private readonly _MAX_TOKENS = 2048;
    private readonly _TIMEOUT_IN_MS = 45000;
    private readonly _OPEN_AI_MODEL = OpenaiModelVersion.DEFAULT_MODEL;
    private readonly _PROPOSALS_DEFAULT_COUNT = 1;

    private readonly _openAiConfiguration = new Configuration({
        apiKey: Config.services.openai.apiKey,
    });
    private readonly _openaiApi = new OpenAIApi(this._openAiConfiguration);

    async callWithPrompt({
        prompt,
        instructions,
        proposalsCount,
        context,
    }: {
        prompt: string;
        instructions?: string;
        proposalsCount?: number;
        context?: object;
    }): Promise<string[]> {
        const responsesCount = proposalsCount ?? this._PROPOSALS_DEFAULT_COUNT;

        const messages: CreateChatCompletionRequest['messages'] = [];
        if (instructions) {
            messages.push({ role: 'system', content: instructions });
        }
        if (context) {
            messages.push({ role: 'system', content: `Here is the JSON context for the restaurant:\n\n${JSON.stringify(context)}` });
        }
        messages.push({ role: 'user', content: prompt });

        const { data } = await this._openaiApi.createChatCompletion(
            {
                model: this._OPEN_AI_MODEL,
                max_tokens: this._MAX_TOKENS,
                temperature: 0.8,
                top_p: 0.5,
                messages,
                n: responsesCount,
            },
            {
                timeout: this._TIMEOUT_IN_MS,
            }
        );

        return filterByRequiredKeys(data.choices, ['message'])
            .map(({ message }) => message.content)
            .filter(isNotNil);
    }
}
