import { NextFunction, Request, Response, Send } from 'express';
import objectHash from 'object-hash';
import { container } from 'tsyringe';

import { InjectionToken } from ':helpers/injection';
import { Cache } from ':plugins/cache';

const CACHE_KEY_SEPARATOR = ':';

/**
 * Do not use the character CACHE_KEY_SEPARATOR in this enum because it is used to separate the prefix from the suffix
 */
export enum CachePrefixKey {
    GET_PLATFORM_RESTAURANT_ACCOUNTS = 'GET_PLATFORM_RESTAURANT_ACCOUNTS',
    GET_RESTAURANT_POSTS_INSIGHTS = 'GET_RESTAURANT_POSTS_INSIGHTS',
    GET_TOP_3_POSTS_INSIGHTS = 'GET_TOP_3_POSTS_INSIGHTS',
    GET_INSIGHTS_AGGREGATED_BY_RESTAURANT = 'GET_INSIGHTS_AGGREGATED_BY_RESTAURANT',
    KEYWORDS_SCORE = 'KEYWORDS_SCORE',
    GET_STORE_LOCATOR_PAGES = 'GET_STORE_LOCATOR_PAGES',
}

const cache = container.resolve<Cache>(InjectionToken.Cache);

/**
 * Use this middleware to cache the value returned by res.send or res.json functions.
 * Must be placed before the middleware that proceed the call.
 *
 * @param prefixKey
 * @param ttl in seconds
 * @param statusWhenCachedResponseSent Must be a success response code (2xx)
 */
export function cacheMiddleware(prefixKey: CachePrefixKey, ttl: number, statusWhenCachedResponseSent = 200) {
    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            const suffixKey: string = extractSuffixCacheKeyFromRequest(req);
            const key: string = computeCacheKey(prefixKey, suffixKey);
            const cachedData = await cache.get(key);

            if (cachedData) {
                return res.status(statusWhenCachedResponseSent).send(cachedData);
            }

            res.send = constructCachedResponseFunction(key, ttl, res.send);
            res.json = constructCachedResponseFunction(key, ttl, res.json);
            next();
        } catch (error) {
            next(error);
        }
    };
}

/**
 * Delete one or more keys from the cache.
 * Useful to delete key(s) set by the cacheMiddleware.
 *
 * Do NOT use this function if you suspect that the prefix will match a large
 * number of keys (more than 100?).
 *
 * @param prefixKey
 * @param ids Array of string that can match part of key.
 * If ids is empty, this will delete all keys that start with prefixKey.
 * If ids is not empty, this will delete all keys that start with prefixKey
 * You can give an array of array of string to execute multiple deletion at once.
 * This is useful to avoid fetching all keys in the cache multiple times.
 *
 * @return the number of keys deleted
 */
export async function deleteCachePrefix(prefixKey: CachePrefixKey, ids: string[] | string[][] = []): Promise<number> {
    const pattern = `${prefixKey}${CACHE_KEY_SEPARATOR}*`;
    const keys: string[] = await cache.getKeys(pattern);
    let idsCleaned: string[][];
    if (Array.isArray(ids[0])) {
        idsCleaned = ids as string[][];
    } else {
        idsCleaned = [ids as string[]];
    }
    const promises: Promise<number>[] = idsCleaned.map((e) => deleteCachePattern(prefixKey, e, keys));
    const results: number[] = await Promise.all(promises);
    return results.reduce((acc, cur) => acc + cur, 0);
}

async function deleteCachePattern(prefixKey: CachePrefixKey, ids: string[], keys: string[]): Promise<number> {
    const lookahead = ids.map((e) => `(?=.*${e})`).join('');
    const regex = `${prefixKey}${CACHE_KEY_SEPARATOR}${lookahead}.*`;
    const filteredKeys = keys.filter((key: string) => key.match(regex));
    if (filteredKeys.length > 0) {
        return cache.delete(filteredKeys);
    }
    return 0;
}

function constructCachedResponseFunction(key: string, ttl: number, originalFn: Send): Send {
    return function (this: Response & { malouApplicationCacheSet: boolean }, body: any) {
        const successStatusFirstNumber = '2';
        if (!this.statusCode.toString().startsWith(successStatusFirstNumber)) {
            return originalFn.call(this, body);
        }
        // We only want to cache the first value given to either res.send or res.json (ie the user call)
        // because res.send and res.json can call themselves (with transformed value)
        if (!this.malouApplicationCacheSet) {
            void cache.set(key, body, ttl);
            this.malouApplicationCacheSet = true;
        }
        return originalFn.call(this, body);
    };
}

export function computeCacheKey(prefix: string, suffix: string) {
    return `${prefix}${CACHE_KEY_SEPARATOR}${suffix}`;
}

export function extractSuffixCacheKeyFromRequest(req: Request): string {
    const bodyHash = req.body ? objectHash(req.body) : 'no-body';
    // req.original url contains the route (after the domain name) with all query params
    return `${req.originalUrl}@${bodyHash}`;
}
