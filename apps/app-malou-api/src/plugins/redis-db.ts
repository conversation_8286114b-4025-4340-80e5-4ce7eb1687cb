import Redis from 'ioredis';
import assert from 'node:assert/strict';

import { TimeInMilliseconds } from '@malou-io/package-utils';

import { Config } from ':config';
import { logger } from ':helpers/logger';

const connections: Set<Redis> = new Set();

let shuttingDown: boolean = false;

/**
 * It can be useful to call this function to create separate connections to subscribe to
 * pubsub channels (with the SUBSCRIBE command).
 *
 * The connection must be disposed after used with `.disconnect(false)`.
 */
export const createConnection = (): Redis => {
    assert(!shuttingDown);

    const c = new Redis({
        host: Config.redis.host,
        port: Config.redis.port,
        showFriendlyErrorStack: process.env.ELASTICACHE_PORT === 'local',
        maxRetriesPerRequest: 5,
        // Increment the time to reconnect for 10 seconds for each try with a maximum of 10 minutes
        retryStrategy(times) {
            const delay = Math.min(times * 10 * TimeInMilliseconds.SECOND, 10 * TimeInMilliseconds.MINUTE);
            return delay;
        },
    });

    connections.add(c);
    c.on('close', () => connections.delete(c));

    c.on('error', (err) => {
        logger.info(`Redis connection error: ${err}`);
    });

    c.on('end', () => {
        logger.info('[REDIS] Client disconnected');
    });

    return c;
};

/**
 * The default connection. This one should be used for most operations except for some
 * commands like SUBSCRIBE.
 */
export const client = createConnection();
client.on('connect', () => {
    logger.info('[REDIS] default connection open');
});

/** Closes all the connections */
export const shutdownAll = (): void => {
    shuttingDown = true;

    for (const conn of connections) {
        try {
            conn.disconnect(false);
        } catch (error) {
            logger.error('[REDIS] Failed to disconnect', error);
        }
    }
};
