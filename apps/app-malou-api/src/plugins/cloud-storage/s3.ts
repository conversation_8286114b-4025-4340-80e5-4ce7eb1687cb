import fs from 'fs';
import { singleton } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';

import { AppEntity, isNotNil, PictureSize, PictureSizeRecord } from '@malou-io/package-utils';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import { downloadFiles, getContentTypeByFile } from ':helpers/utils';
import { Media } from ':modules/media/entities/media.entity';
import { aws } from ':plugins/aws';
import { CloudStorage, GetSignedUrlParams, UploadMediaParams } from ':plugins/cloud-storage/cloud-storage.interface';

export const s3 = new aws.S3();

const AWS_REGION = Config.services.aws.region;
const S3_BUCKET_NAME = Config.services.s3.bucketName;
const S3_BUCKET_BASE_URL = `https://${S3_BUCKET_NAME}.s3.${AWS_REGION}.amazonaws.com`;

/**
 *
 * @param {String[]} urls
 * @param {string} s3Path
 * @param {string} s3MediaName
 * @returns String[] aws urls
 */
export async function uploadMedias(urls: string[], s3Path: string, s3MediaName: string) {
    const medias = await downloadFiles(urls, 'downloadedMedias');
    return Promise.all(
        medias.map(({ path, extension }, index) =>
            uploadMedia({
                localImage: path,
                s3Path,
                mediaName: s3MediaName + index.toString(),
                extension,
            })
        )
    );
}

export const uploadMedia = ({
    localImage,
    s3Path,
    mediaName,
    extension,
    mimetype,
}: {
    localImage: string;
    s3Path: string;
    mediaName: string;
    extension: string;
    mimetype?: string;
}) => {
    const awsKey = `${s3Path}/${mediaName}.${extension}`;
    return s3
        .putObject({
            Bucket: S3_BUCKET_NAME,
            Body: fs.readFileSync(localImage),
            Key: awsKey,
            ACL: 'public-read',
            ContentType: mimetype || getContentTypeByFile(awsKey),
        })
        .promise()
        .then(() => {
            if (fs.existsSync(localImage)) {
                fs.unlink(localImage, () =>
                    logger.info(`[S3_UPLOAD] - Image deleted`, {
                        localImage,
                    })
                );
            } else {
                logger.error(`[S3_UPLOAD] - Image not found`, {
                    localImage,
                });
            }
            return `${S3_BUCKET_BASE_URL}/${awsKey}`;
        });
};

export const deleteObject = (key: string, bucket = S3_BUCKET_NAME) => {
    const params = {
        Bucket: bucket,
        Key: key,
    };
    return s3.deleteObject(params, (err) => {
        if (err) logger.warn('[S3_DELETE]', err); // error
    });
};

@singleton()
export class AwsS3 implements CloudStorage {
    getBucketBaseUrl(): string {
        return S3_BUCKET_BASE_URL;
    }

    async emptyDirectory(dir: string): Promise<void> {
        const listParams = {
            Bucket: S3_BUCKET_NAME,
            Prefix: dir,
        };

        const listedObjects = await s3.listObjectsV2(listParams).promise();

        const contents = listedObjects.Contents ?? [];
        if (contents.length === 0) {
            return;
        }

        const deleteParams: {
            Bucket: string;
            Delete: {
                Objects: { Key: string }[];
            };
        } = {
            Bucket: S3_BUCKET_NAME,
            Delete: { Objects: [] },
        };

        contents.forEach(({ Key }) => {
            if (!Key) {
                return;
            }
            deleteParams.Delete.Objects.push({ Key });
        });

        await s3.deleteObjects(deleteParams).promise();

        if (listedObjects.IsTruncated) {
            await this.emptyDirectory(dir);
        }
    }

    async deleteObject(remoteObjectKey: string): Promise<void> {
        const params = {
            Bucket: S3_BUCKET_NAME,
            Key: remoteObjectKey,
        };

        await s3.deleteObject(params, (err) => {
            if (err) logger.warn('[S3_DELETE]', err); // error
        });
    }

    async getPutObjectSignedUrl(params: GetSignedUrlParams): Promise<string> {
        return s3.getSignedUrlPromise('putObject', {
            Bucket: S3_BUCKET_NAME,
            Key: params.key,
            ACL: 'public-read',
        });
    }

    async downloadAndSaveMedia(mediaObjectKey: string, localFilePath: string, syncWrite = false): Promise<string> {
        try {
            logger.info('[DEBUG][download] start', { mediaObjectKey, localFilePath });
            const s3Object = await this._getObject({ key: mediaObjectKey });

            logger.info('[DEBUG][download] s3Object');
            if (syncWrite) {
                logger.info('[DEBUG][download] writeWithAwait');
                await fs.promises.writeFile(localFilePath, s3Object.Body as Buffer);
            } else {
                logger.info('[DEBUG][download] writeFileSync');
                fs.writeFileSync(localFilePath, s3Object.Body as Buffer);
            }

            logger.info('[DEBUG][download] end');
            return localFilePath;
        } catch (err) {
            logger.info('[DEBUG][download] error');
            logger.error('Error downloading file', err);
            throw err;
        }
    }

    async getObject(mediaObjectKey: string): Promise<Buffer> {
        try {
            const s3Object = await this._getObject({ key: mediaObjectKey });
            return s3Object.Body as Buffer;
        } catch (err) {
            logger.error('Error downloading file', err);
            throw err;
        }
    }

    async uploadMedia({ localImagePath, remotePath, mediaName, extension, mimetype }: UploadMediaParams) {
        try {
            const awsKey = `${remotePath}/${mediaName}.${extension}`;
            const body = await fs.promises.readFile(localImagePath);

            await s3
                .putObject({
                    Bucket: S3_BUCKET_NAME,
                    Body: body,
                    Key: awsKey,
                    ACL: 'public-read',
                    ContentType: mimetype || getContentTypeByFile(awsKey),
                })
                .promise();
            await fs.promises.unlink(localImagePath);
            logger.info(`[S3] [UPLOAD] - ${localImagePath} deleted`);
            return `${S3_BUCKET_BASE_URL}/${awsKey}`;
        } catch (err) {
            logger.error('[S3] [UPLOAD] Error uploading file', err);
            throw err;
        }
    }

    async duplicateObject(
        format: string,
        urls: Media['urls'],
        entityRelated: AppEntity,
        entityId: string
    ): Promise<PictureSizeRecord<string>> {
        const uuid = uuidv4();
        const nonNilUrlPairs: [string, string][] = Object.entries(urls).filter(([_key, value]) => isNotNil(value)) as [string, string][];
        const promises = nonNilUrlPairs
            .filter(([key, _value]) => Object.values(PictureSize).includes(key as PictureSize))
            .map(async ([key, value]: [string, string]) => {
                const awsKey = `${entityRelated}/${entityId}/media/${uuid}/${key}.${format}`;
                return this._copyMedia({
                    awsSourceKey: value.split('.amazonaws.com/').pop() as string,
                    awsKey,
                }).then(() => ({
                    [key]: `${S3_BUCKET_BASE_URL}/${awsKey}`,
                }));
            });
        return Promise.all(promises).then((results) =>
            results.reduce((acc, curr) => {
                const [key, value] = Object.entries(curr)[0];
                acc[key] = value;
                return acc;
            }, {} as PictureSizeRecord<string>)
        );
    }

    async uploadBuffer({ buffer, fileKey, mimeType }: { buffer: Buffer; fileKey: string; mimeType: string }) {
        try {
            await s3
                .putObject({
                    Bucket: S3_BUCKET_NAME,
                    Body: buffer,
                    Key: fileKey,
                    ACL: 'public-read',
                    ContentType: mimeType,
                })
                .promise();
            return `${S3_BUCKET_BASE_URL}/${fileKey}`;
        } catch (err) {
            logger.error('[S3] Error uploading buffer', {
                err,
                fileKey,
            });
            throw err;
        }
    }

    private _getObject({ key, bucket = S3_BUCKET_NAME }: { key: string; bucket?: string }) {
        return s3
            .getObject({
                Bucket: bucket,
                Key: key,
            })
            .promise();
    }

    // https://docs.aws.amazon.com/AWSJavaScriptSDK/latest/AWS/S3.html#copyObject-property
    private _copyMedia = ({
        awsSourceKey,
        awsKey,
        metadataDirective = undefined,
    }: {
        awsSourceKey: string;
        awsKey: string;
        metadataDirective?: string;
    }) =>
        s3
            .copyObject({
                Bucket: S3_BUCKET_NAME,
                CopySource: `${S3_BUCKET_NAME}/${awsSourceKey}`,
                Key: awsKey,
                ACL: 'public-read',
                ContentType: getContentTypeByFile(awsKey),
                MetadataDirective: metadataDirective,
            })
            .promise();
}
