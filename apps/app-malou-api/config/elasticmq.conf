include classpath("application.conf")

node-address {
   protocol = http
   host = "*"
   port = 9324
   context-path = ""
}

rest-sqs {
   enabled = true
   bind-port = 9324
   bind-hostname = "0.0.0.0"
   // Possible values: relaxed, strict
   sqs-limits = strict
}

// set up your queues here
queues {
   malou_sqs_scrapper_api {
     defaultVisibilityTimeout = 10 seconds
     delay = 2 seconds
     receiveMessageWait = 0 seconds
   }
   malou_sqs_reviews_collect {
     defaultVisibilityTimeout = 60 seconds
     delay = 2 seconds
     receiveMessageWait = 0 seconds
   }
   malou_sqs_posts {
     defaultVisibilityTimeout = 10 seconds
     delay = 2 seconds
     receiveMessageWait = 0 seconds
   }
   malou_sqs_review_booster_sns {
     defaultVisibilityTimeout = 10 seconds
     delay = 2 seconds
     receiveMessageWait = 0 seconds
   }
   malou_sqs_start_daily_reviews_reports {
     defaultVisibilityTimeout = 10 seconds
     delay = 2 seconds
     receiveMessageWait = 0 seconds
   }
   malou_sqs_send_daily_reviews_report {
     defaultVisibilityTimeout = 10 seconds
     delay = 2 seconds
     receiveMessageWait = 0 seconds
   }
   malou_sqs_start_weekly_reviews_reports {
     defaultVisibilityTimeout = 10 seconds
     delay = 2 seconds
     receiveMessageWait = 0 seconds
   }
   malou_sqs_send_weekly_reviews_report {
     defaultVisibilityTimeout = 10 seconds
     delay = 2 seconds
     receiveMessageWait = 0 seconds
   }
   malou_sqs_start_weekly_performance_reports {
     defaultVisibilityTimeout = 10 seconds
     delay = 2 seconds
     receiveMessageWait = 0 seconds
   }
   malou_sqs_send_weekly_performance_report {
     defaultVisibilityTimeout = 10 seconds
     delay = 2 seconds
     receiveMessageWait = 0 seconds
   }
   malou_sqs_start_monthly_performance_reports {
     defaultVisibilityTimeout = 10 seconds
     delay = 2 seconds
     receiveMessageWait = 0 seconds
   }
   malou_sqs_send_monthly_performance_report {
     defaultVisibilityTimeout = 10 seconds
     delay = 2 seconds
     receiveMessageWait = 0 seconds
   }
   malou_sqs_thumbnail_generator {
     defaultVisibilityTimeout = 10 seconds
     delay = 2 seconds
     receiveMessageWait = 0 seconds
   }
   malou_sqs_create_message_queues_daily_insights {
     defaultVisibilityTimeout = 10 seconds
     delay = 2 seconds
     receiveMessageWait = 0 seconds
   }
   malou_sqs_daily_save_insights {
     defaultVisibilityTimeout = 10 seconds
     delay = 2 seconds
     receiveMessageWait = 0 seconds
   }
   malou_sqs_fetch_keywords_volume {
     defaultVisibilityTimeout = 10 seconds
     delay = 2 seconds
     receiveMessageWait = 0 seconds
   }
   malou_sqs_keywords_generation_processing {
     defaultVisibilityTimeout = 10 seconds
     delay = 2 seconds
     receiveMessageWait = 0 seconds
   }
   malou_sqs_keywords_failed_generation_processing {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_retry_empty_translations {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_create_message_queues_monthly_save_roi_insights {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_monthly_save_roi_insights {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_create_messages_monthly_update_similar_restaurants {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_monthly_update_similar_restaurants {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_reviews_catch_up {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_ratings_catch_up {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_monthly_check_restaurants_eligibility_to_activate_roi {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_daily_nagative_reviews_email_notifications {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_create_new_reviews_notification {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_create_post_error_notification {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_create_comments_notification {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_actualize_merge_information_updates {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_create_message_notification {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_create_mentions_notification {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_synchronize_recent_posts {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_create_platform_disconnected_notification {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_create_info_update_error_notification {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_aws_media_convert_video_progress = {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_fetch_review_semantic_analysis = {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   "malou_sqs_fetch_review_semantic_analysis.fifo" = {
        defaultVisibilityTimeout = 60 seconds
        delay = 2 seconds
        receiveMessageWait = 0 seconds
        fifo = true
        contentBasedDeduplication = true
   }
   malou_sqs_update_review_relevant_bricks = {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_create_message_monthly_save_keyword_search_impressions = {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   malou_sqs_monthly_save_keyword_search_impressions = {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
   }
   "malou_sqs_previous_reviews_analysis.fifo" = {
      defaultVisibilityTimeout = 10 seconds
      delay = 2 seconds
      receiveMessageWait = 0 seconds
      fifo = true
      contentBasedDeduplication = true
   }
}
