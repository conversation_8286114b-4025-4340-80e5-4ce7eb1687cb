---
import type {
    GetStoreLocatorMapDto,
    GetStoreLocatorPagesDto,
} from '@malou-io/package-dto';

import MapPage from ':components/MapPage.astro';
import StorePage from ':components/StorePage.astro';
import { config } from ':config';
import {
    PageType,
    type IStorePage,
    type PageData,
} from ':interfaces/pages.interfaces';
import { loadComponent } from ':utils/load-component';

// Generate static paths for each restaurant in every company
export async function getStaticPaths() {
    const resPages = await fetch(
        `${config.apiBaseUrl}/store-locator/${config.organizationId}/pages?api_key=${config.apiKey}${config.isDevMode ? `&isDevMode=${config.isDevMode}` : ''}`,
    );

    const { data: pagesData }: { data: GetStoreLocatorPagesDto } =
        await resPages.json();

    const storesForFooter = pagesData.restaurantsPages.map((s) => ({
        name: s.name,
        relativePath: s.relativePath,
    }));

    const allPages: PageData[] = pagesData.restaurantsPages.map((store) => {
        const extendedStore: IStorePage = store;
        return {
            params: {
                path: store.relativePath,
            },
            props: {
                data: extendedStore,
                urls: pagesData.urls[store.lang] ?? {},
                storesForFooter,
                type: PageType.STORE,
            },
        };
    });

    if (pagesData.mapPages.length > 0) {
        const mapPages: PageData[] = pagesData.mapPages.map((map) => {
            return {
                params: {
                    path: map.relativePath,
                },
                props: {
                    data: map,
                    urls: pagesData.urls[map.lang] ?? {},
                    storesForFooter,
                    type: PageType.MAP,
                },
            };
        });
        allPages.push(...mapPages);
    }

    return allPages;
}

// Fetch restaurant details
const { data, storesForFooter, urls, type } = Astro.props;

const [HeaderComponent, FooterComponent] = await Promise.all([
    loadComponent({
        organizationName: data.organizationName,
        component: 'header',
    }),
    loadComponent({
        organizationName: data.organizationName,
        component: 'footer',
    }),
]);
---

{
    type === PageType.STORE && (
        <StorePage
            store={data as IStorePage}
            urls={urls}
            storesForFooter={storesForFooter}
            HeaderComponent={HeaderComponent}
            FooterComponent={FooterComponent}
        />
    )
}
{
    type === PageType.MAP && (
        <MapPage
            map={data as GetStoreLocatorMapDto}
            urls={urls}
            storesForFooter={storesForFooter}
            HeaderComponent={HeaderComponent}
            FooterComponent={FooterComponent}
        />
    )
}
