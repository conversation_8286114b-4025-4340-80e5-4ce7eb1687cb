import { config } from ':config';

const componentMap: Record<string, Record<string, () => Promise<any>>> = {
    'groupe-rollroll-x-bolkiri': {
        header: () => import(':components/custom/bolkiri/Header.astro'),
        footer: () => import(':components/custom/bolkiri/Footer.astro'),
    },
    'krispy-kreme': {
        header: () => import(':components/custom/krispy-kreme/Header.astro'),
        footer: () => import(':components/custom/krispy-kreme/Footer.astro'),
    },
    bioburger: {
        header: () => import(':components/custom/bioburger/Header.astro'),
        footer: () => import(':components/custom/bioburger/Footer.astro'),
    },
};

// Function to dynamically load a component (header, footer, css, etc.)
export async function loadComponent({
    organizationName,
    component,
}: {
    organizationName: string;
    component: 'header' | 'footer';
}): Promise<any> {
    const organizationKey = organizationName.toLowerCase().replaceAll(' ', '-');
    const defaultOrganizationKey = 'bioburger'; // For non production environments

    const importComponent = !['production', 'test'].includes(config.environment)
        ? componentMap[defaultOrganizationKey]?.[component]
        : componentMap[organizationKey]?.[component];

    if (!importComponent) {
        throw new Error(
            `Component "${component}" not found for organization "${organizationName}".`,
        );
    }

    return (await importComponent()).default;
}
