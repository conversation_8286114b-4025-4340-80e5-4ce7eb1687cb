---
import Pin from ':assets/icons/pin.svg';
import SearchInput from ':components/map/SearchInput.astro';
import { initTranslationFunction } from ':i18n/index';
import { getStyles } from ':utils/get-element-styles';
import type { GetStoreLocatorMapDto } from '@malou-io/package-dto';
import { Picture } from 'astro:assets';
interface Props {
    stores: GetStoreLocatorMapDto['stores'];
    styles: GetStoreLocatorMapDto['styles'];
    inactivePin: GetStoreLocatorMapDto['mapComponents']['pins']['inactivePin'];
}

const { stores, styles, inactivePin } = Astro.props as Props;

const getElementStyles = getStyles({ styles });
const t = await initTranslationFunction();
---

<div class="flex h-fit flex-col items-center lg:h-[90vh]">
    <SearchInput
        styles={styles}
        classList="lg:flex hidden"
        inputClass="search-on-map-desktop"
        stores={stores}
    />
    <div
        id="store-list"
        class={`${getElementStyles({ elementId: 'store-list-items-wrapper' })} text-map-list-content  flex min-h-[80vh] w-full flex-col items-center gap-y-3 sm:overflow-auto py-6`}
    >
        {
            stores.map((store) => {
                return (
                    <div
                        data-lat={store.coordinates.lat}
                        data-lng={store.coordinates.lng}
                        data-id={store.id}
                        data-name={store.restaurantName}
                        data-address={store.fullAddress}
                        class={`${getElementStyles({ elementId: 'store-list-item' })} text-map-list-content location-selector border-map-list-location-selected font-primary box-border flex w-[90%] cursor-pointer flex-col gap-y-3 rounded-md py-4 pr-12 !pl-7`}
                    >
                        <p class="text-xl font-bold">{store.restaurantName}</p>
                        <p class="ji flex items-center gap-4 text-xs sm:gap-2">
                            <Pin
                                class={`${getElementStyles({ elementId: 'map-page-icons' })}`}
                                height={20}
                                width={20}
                            />
                            <span class="text-map-list-content">
                                {store.fullAddress}
                            </span>
                        </p>
                        {store.isNotOpenedYet && (
                            <div
                                class={`${getElementStyles({ elementId: 'map-page-store-not-open-yet' })} flex h-[27px] w-[200px] items-center justify-center rounded-md`}
                            >
                                <p class="!m-0 text-xs font-bold">
                                    {t('information.opening-soon')}
                                </p>
                            </div>
                        )}
                        <div class="flex w-[320px] flex-wrap items-center justify-between">
                            <div class="text-map-list-content flex flex-wrap items-center justify-start gap-1 text-xs">
                                {store.ctas?.map((cta) => (
                                    <>
                                        <a class="underline" href={cta.url}>
                                            {cta.text}
                                        </a>
                                        -
                                    </>
                                ))}
                                <a
                                    class="text-map-list-content text-xs underline"
                                    href={`/${store.relativePath}`}
                                >
                                    {t('map.more-details')}
                                </a>
                            </div>
                            <div
                                id={store.id}
                                class={`${getElementStyles({ elementId: 'store-list-item-distance-block' })} hidden items-center gap-1 rounded-md px-2 py-1 text-xs font-bold`}
                            />
                        </div>
                    </div>
                );
            })
        }
        <div
            id="no-stores"
            class="hidden h-full w-full flex-col items-center justify-center"
        >
            <Picture
                src={inactivePin.url}
                formats={['webp']}
                fallbackFormat="jpg"
                alt={inactivePin.description}
                class="mb-4 !h-10 !w-10 rounded-full object-contain"
                width={50}
                height={50}
                densities={[1, 2, 3]}
            />
            <p class="text-map-list-content text-md">
                {t('map.store-list.no-results')}
            </p>
        </div>
    </div>
</div>

<script src="./../../scripts/map/stores-list.js"></script>
