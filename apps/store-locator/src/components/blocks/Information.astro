---
import Pin from ':assets/icons/pin.svg';
import Phone from ':assets/icons/phone.svg';
import Voucher from ':assets/icons/voucher.svg';
import Cart from ':assets/icons/cart.svg';
import Hour from ':assets/icons/hour.svg';
import { Picture } from 'astro:assets';
import { initTranslationFunction } from ':i18n/index';
import type { IStorePage } from ':interfaces/pages.interfaces';
import { getStyles } from ':utils/get-element-styles';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    information: IStorePage['informationBlock'];
    styles: IStorePage['styles'];
}

const { information, styles } = Astro.props as Props;

const getElementStyles = getStyles({ styles });

// Hide imageUrl and all references to Malou
const { imageUrl, ...informationInClient } = information;

const t = await initTranslationFunction();
---

<display-schedule data-information={JSON.stringify(informationInClient)}
></display-schedule>

<div
    class={`${getElementStyles({ elementId: 'information-wrapper' })} mx-auto flex flex-col lg:flex-row`}
>
    <div class="relative w-full lg:flex lg:h-auto lg:min-h-0 lg:flex-1">
        <div
            class="h-[225px] w-full overflow-hidden sm:h-[450px] lg:absolute lg:inset-0 lg:h-full"
        >
            <Picture
                src={information.imageUrl}
                formats={['webp']}
                fallbackFormat="jpg"
                class="h-full w-full object-cover object-center"
                alt={information.imageDescription}
                widths={[3000, 2000, 1000, 750, 500, 250]}
                sizes="(max-width: 1024px) 100vw, 50vw"
                inferSize
            />
        </div>
    </div>

    <div class="flex min-h-fit w-full flex-1 flex-col gap-3 px-4 py-8 md:px-16">
        {
            information.isNotOpenedYet && (
                <p
                    class={`${getElementStyles({ elementId: 'information-opening-soon-banner' })} mb-4 p-6 text-center text-xl font-bold lg:text-3xl`}
                >
                    {t('information.opening-soon')}
                </p>
            )
        }
        <h1
            class={`${getElementStyles({ elementId: 'information-title' })} text-5xl md:text-6xl`}
        >
            {information.restaurantName}
        </h1>

        <div
            class="grid-cols-[repeat(7, auto);] sm:grid-rows-[repeat(7, auto);] mt-6 auto-cols-auto flex-wrap gap-y-5 text-sm font-normal sm:grid sm:auto-rows-auto sm:gap-x-3 md:gap-x-0 md:gap-y-2 md:text-base"
        >
            <div class="mb-4 sm:mb-0">
                <a
                    id="itinerary-link"
                    aria-label={t('information.itinerary.aria-label')}
                    href={information.itineraryUrl}
                    target="_blank"
                    class="analytics-tracker"
                    data-tracking-event-name="click-cta"
                    data-tracking-event-category="information-block"
                    data-tracking-event-label="itinerary-address"
                >
                    <p class="flex items-start gap-6 sm:gap-4">
                        <Pin
                            class={`${getElementStyles({ elementId: 'information-icons' })}`}
                            height={20}
                            width={20}
                        />
                        <span>{information.fullAddress}</span>
                    </p>
                </a>
            </div>

            <div class="mb-0">
                {
                    information.phone && (
                        <a
                            href={`tel:${information.phone}`}
                            class="analytics-tracker"
                            data-tracking-event-name="click-cta"
                            data-tracking-event-category="information-block"
                            data-tracking-event-label="phone"
                            aria-label={t('information.phone.aria-label')}
                        >
                            <p class="flex items-center gap-4">
                                <Phone
                                    class={`${getElementStyles({ elementId: 'information-icons' })}`}
                                    height={20}
                                    width={20}
                                />

                                {information.phone}
                            </p>
                        </a>
                    )
                }
            </div>

            {
                information.hours?.length > 0 && (
                    <>
                        <div class="col-span-2 mb-0">
                            <div class="my-4 w-full border-t" />
                        </div>

                        <div class="row-start-3">
                            <div class="flex gap-4">
                                <Hour
                                    class={`${getElementStyles({ elementId: 'information-icons' })}`}
                                    height={20}
                                    width={20}
                                />

                                <div
                                    id="hours-column-1"
                                    class="flex flex-col gap-2"
                                />
                            </div>
                        </div>

                        <div class="row-start-3 mt-2 mb-4 ml-9 md:mt-0 md:mb-0 md:ml-0">
                            <div
                                id="hours-column-2"
                                class="flex flex-col gap-2"
                            />
                        </div>
                    </>
                )
            }

            {
                information?.attributesNames?.length > 0 && (
                    <>
                        <div class="col-span-2 mb-0">
                            <div class="my-4 w-full border-t" />
                        </div>

                        <div class="col-span-2 row-start-5 mb-0">
                            <div class="flex items-center gap-4">
                                <Cart
                                    class={`${getElementStyles({ elementId: 'information-icons' })} min-w-5`}
                                    height={20}
                                    width={20}
                                />
                                <p>{information.attributesNames.join(', ')}</p>
                            </div>
                        </div>
                    </>
                )
            }

            {
                information?.paymentMethods?.length > 0 && (
                    <>
                        <div class="col-span-2 row-start-6 mb-0">
                            <div class="my-4 w-full border-t" />
                        </div>

                        <div class="col-span-2 row-start-7">
                            <div class="flex items-center gap-4">
                                <Voucher
                                    class={`${getElementStyles({ elementId: 'information-icons' })}`}
                                    height={20}
                                    width={20}
                                />
                                <p>{information.paymentMethods.join(', ')}</p>
                            </div>
                        </div>
                    </>
                )
            }
        </div>

        <div
            class={`${getElementStyles({ elementId: 'information-banner' })} fixed bottom-0 left-0 z-10 m-auto md:mt-6 md:mb-2 flex w-full justify-center gap-4 p-4 shadow-2xl md:static md:bg-transparent md:p-0 md:shadow-none`}
        >
            {
                information.phone && (
                    <a
                        href={`tel:${information.phone}`}
                        aria-label={t('information.phone.aria-label')}
                        data-tracking-event-name="click-cta"
                        data-tracking-event-category="information-block"
                        data-tracking-event-label="phone-banner"
                        class={`${getElementStyles({ elementId: 'information-banner-cta-1' })} analytics-tracker block border-[1px] border-solid bg-transparent px-7 py-4 font-bold shadow-md md:hidden`}
                    >
                        <Phone
                            class={getElementStyles({
                                elementId: 'information-banner-cta-icon',
                            })}
                            height={20}
                            width={20}
                        />
                    </a>
                )
            }

            {
                information.ctas?.[0] && (
                    <a
                        href={information.ctas[0].url}
                        aria-label={information.ctas[0].text}
                        target="_blank"
                        class={`${getElementStyles({ elementId: 'information-banner-cta-1' })} analytics-tracker border-[1px] border-solid px-7 py-4 font-extralight shadow-md hover:text-white`}
                        {...(information.ctas[0].tracker && {
                            'data-tracking-event-name':
                                information.ctas[0].tracker.eventName,
                            'data-tracking-event-category':
                                information.ctas[0].tracker.eventCategory,
                            'data-tracking-event-label':
                                information.ctas[0].tracker.eventLabel,
                        })}
                    >
                        <span>{information.ctas[0].text.toUpperCase()}</span>
                    </a>
                )
            }

            {
                information.ctas?.[1] && (
                    <a
                        href={information.ctas[1].url}
                        target="_blank"
                        aria-label={information.ctas[1].text}
                        class={`${getElementStyles({ elementId: 'information-banner-cta-2' })} analytics-tracker flex items-center gap-3 border-[1px] border-solid px-7 py-4 font-extralight shadow-md hover:bg-transparent`}
                        {...(information.ctas[1].tracker && {
                            'data-tracking-event-name':
                                information.ctas[1].tracker.eventName,
                            'data-tracking-event-category':
                                information.ctas[1].tracker.eventCategory,
                            'data-tracking-event-label':
                                information.ctas[1].tracker.eventLabel,
                        })}
                    >
                        <span>{information.ctas[1].text.toUpperCase()}</span>
                    </a>
                )
            }
        </div>
    </div>
</div>

<script>
    import type { IStorePage } from ':interfaces/pages.interfaces';

    enum Day {
        MONDAY = 'MONDAY',
        TUESDAY = 'TUESDAY',
        WEDNESDAY = 'WEDNESDAY',
        THURSDAY = 'THURSDAY',
        FRIDAY = 'FRIDAY',
        SATURDAY = 'SATURDAY',
        SUNDAY = 'SUNDAY',
    }

    const TRANSLATED_DAY_OF_THE_WEEK = {
        [Day.MONDAY]: 'Lundi',
        [Day.TUESDAY]: 'Mardi',
        [Day.WEDNESDAY]: 'Mercredi',
        [Day.THURSDAY]: 'Jeudi',
        [Day.FRIDAY]: 'Vendredi',
        [Day.SATURDAY]: 'Samedi',
        [Day.SUNDAY]: 'Dimanche',
    };

    function getFormattedScheduleOfTheDayOpenOrSoonOpen(
        hours: IStorePage['informationBlock']['hours'][0]['periods'],
        day: Day,
        now: Date,
        isTomorrow: boolean,
    ) {
        return (
            TRANSLATED_DAY_OF_THE_WEEK[day] +
            ' : ' +
            hours
                .map((p) => {
                    if (p.isClosed) {
                        return 'Fermé';
                    }

                    const nowOrTomorrow = new Date().setDate(
                        now.getDate() + (isTomorrow ? 1 : 0),
                    );

                    const openTimeHours = p.openTime!.split(':')[0];
                    const openTimeMinutes = p.openTime!.split(':')[1];
                    const closeTimeHours = p.closeTime!.split(':')[0];
                    const closeTimeMinutes = p.closeTime!.split(':')[1];

                    const openTimeDate = new Date(
                        new Date(nowOrTomorrow).setHours(
                            +openTimeHours!,
                            +openTimeMinutes!,
                        ),
                    );
                    const closeTimeDate = new Date(
                        new Date(nowOrTomorrow).setHours(
                            +closeTimeHours!,
                            +closeTimeMinutes!,
                        ),
                    );

                    if (now >= openTimeDate && now <= closeTimeDate) {
                        return `Ouvert maintenant - ${p.closeTime}`;
                    }

                    const openTimeDateForTomorrow = new Date(
                        new Date(nowOrTomorrow).setHours(
                            +openTimeHours!,
                            +openTimeMinutes!,
                        ),
                    );

                    // need to do this because setHours is mutating the date
                    const openTimeDateForTomorrow2 = new Date(
                        new Date(nowOrTomorrow).setHours(
                            +openTimeHours!,
                            +openTimeMinutes!,
                        ),
                    );

                    if (
                        now >=
                            new Date(
                                openTimeDateForTomorrow.setHours(
                                    openTimeDateForTomorrow.getHours() - 1,
                                ),
                            ) &&
                        now < openTimeDateForTomorrow2
                    ) {
                        return `Ouvre bientôt ${p.openTime} - ${p.closeTime}`;
                    }

                    return `${p.openTime} - ${p.closeTime}`;
                })
                .join(', ')
        );
    }

    function createScheduleElement(value: string) {
        const pTag = document.createElement('p');
        pTag.textContent = value;

        if (
            value.includes('Ouvert maintenant') ||
            value.includes('Ouvre bientôt')
        ) {
            pTag.classList.add('text-bold', 'font-primary-bold');
        }

        return pTag;
    }

    class DisplaySchedule extends HTMLElement {
        connectedCallback() {
            const information = JSON.parse(
                this.dataset.information!,
            ) as IStorePage['informationBlock'];

            // Add event listener to redirect to maps
            function redirectMaps(event: Event) {
                const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

                if (isIOS) {
                    window.location.href = `https://maps.apple.com/?q=${information.coordinates.lat},${information.coordinates.lng}`;
                    event.preventDefault(); // Stop default Google Maps link
                }
            }

            document
                .getElementById('itinerary-link')
                ?.addEventListener('click', redirectMaps);
            document
                .getElementById('itinerary-button')
                ?.addEventListener('click', redirectMaps);

            if (!information.hours || information.hours.length === 0) {
                return;
            }
            const today = new Date();
            const dayOfWeek = today.getDay();

            const todaySchedule = information.hours[dayOfWeek - 1];
            const tomorrowSchedule = information.hours[dayOfWeek];

            const scheduleOfToday = information.hours.find(
                (h) => h.day === todaySchedule!.day,
            );
            const textNow = getFormattedScheduleOfTheDayOpenOrSoonOpen(
                scheduleOfToday!.periods,
                todaySchedule!.day as Day,
                today,
                false,
            );

            const scheduleOfTomorrow = information.hours.find(
                (h) => h.day === tomorrowSchedule!.day,
            );
            const textTomorrow = getFormattedScheduleOfTheDayOpenOrSoonOpen(
                scheduleOfTomorrow!.periods,
                tomorrowSchedule!.day as Day,
                today,
                true,
            );

            const schedule = information.hours;
            // need to sort the days by starting with the current day
            const scheduleSortedByStartingTheCurrentDay = schedule
                .slice(new Date().getDay() - 1, schedule.length)
                .concat(schedule.slice(0, new Date().getDay() - 1));

            const ptags = scheduleSortedByStartingTheCurrentDay.map(
                (h, idx) => {
                    if (idx === 0) {
                        return createScheduleElement(textNow);
                    }
                    if (idx === 1) {
                        return createScheduleElement(textTomorrow);
                    }
                    return createScheduleElement(h.formattedHour);
                },
            );

            const firstColumn = document.getElementById('hours-column-1');
            const secondColumn = document.getElementById('hours-column-2');

            ptags.forEach((p) => {
                if (ptags.indexOf(p) < 4) {
                    firstColumn?.appendChild(p);
                } else {
                    secondColumn?.appendChild(p);
                }
            });
        }
    }

    customElements.define('display-schedule', DisplaySchedule);
</script>
