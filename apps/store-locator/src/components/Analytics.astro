---
import { config } from ':config';
import type { GetStoreLocatorHeadBlockDto } from '@malou-io/package-dto';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    headBlock: GetStoreLocatorHeadBlockDto;
}

const { headBlock } = Astro.props as Props;
const isLive = config.environment === 'production' && headBlock.isLive;
---

{
    headBlock.googleAnalytics && isLive && (
        /*
            Loading Google Analytics using Partytown is not working properly because events won't fire correctly using the window / worker communications. Especially when leaving the page after an outbound click.
            We have to load Google Analytics in the main thread. GA recommends to load it in the <head> section but it's better to set it at the end of the HTML to avoid blocking HTML rendering.
            We could have set it up in an Astro script but rendering of Astro scripts are deferred and we want it to load to gather events as soon as possible, alongside images and other assets.
            That's why we also init Google Analytics in the script tag below in inline mode. To get page views as soon as we can. 
        */
        <>
            <script
                is:inline
                src={`https://www.googletagmanager.com/gtag/js?id=${headBlock.googleAnalytics.id}`}
                onload="window.isGoogleAnalyticsScriptLoaded = true;"
            />

            <script
                is:inline
                define:vars={{
                    googleAnalyticsId: headBlock.googleAnalytics.id,
                    googleAnalyticsClientId: headBlock.googleAnalyticsClientId,
                    organizationId: headBlock.googleAnalytics.organizationId,
                    pageCategory: headBlock.googleAnalytics.pageCategory,
                    ...(headBlock.googleAnalytics.storeId && {
                        storeId: headBlock.googleAnalytics.storeId,
                    }),
                }}
            >
                window.googleAnalyticsId = `${googleAnalyticsId}`;
                window.googleAnalyticsClientId = `${googleAnalyticsClientId}`;
                window.organizationId = `${organizationId}`; window.pageCategory
                = `${pageCategory}`; if (storeId) window.storeId = `${storeId}`;
            </script>

            <script
                is:inline
                set:html={`
                    // Wait for gtag to be ready
                    function waitForGtag() {
                        if (window.isGoogleAnalyticsScriptLoaded) {
                            initGA();
                        } else {
                            setTimeout(waitForGtag, 100);
                        }
                    }
                    waitForGtag();

                    // Initialize Google Analytics with configuration
                    function initGA() {
                        window.dataLayer = window.dataLayer || [];
                        window.gtag = function () {
                            window.dataLayer.push(arguments);
                        };

                        gtag('js', new Date());

                        gtag('config', '${headBlock.googleAnalytics.id}', {
                            organization_id: '${headBlock.googleAnalytics.organizationId}',
                            page_category: '${headBlock.googleAnalytics.pageCategory}',
                            ${headBlock.googleAnalytics.storeId ? `store_id: '${headBlock.googleAnalytics.storeId}',` : ''}
                        });

                        ${
                            headBlock.googleAnalyticsClientId
                                ? `
                            gtag('config', '${headBlock.googleAnalyticsClientId}');
                        `
                                : ''
                        }
                    }
            `}
            />

            <script src="./../scripts/analytics.ts" />
        </>
    )
}
