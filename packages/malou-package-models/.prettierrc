{"arrowParens": "always", "bracketSameLine": true, "bracketSpacing": true, "endOfLine": "lf", "importOrder": ["reflect-metadata", "<THIRD_PARTY_MODULES>", "^@malou-io/(.*)$", "^:(core|modules|helpers)/(.*)$", "^[./]"], "importOrderCaseInsensitive": true, "importOrderSeparation": true, "importOrderSortSpecifiers": true, "plugins": ["@trivago/prettier-plugin-sort-imports"], "printWidth": 140, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "singleQuote": true, "tabWidth": 4, "trailingComma": "es5", "useTabs": false}