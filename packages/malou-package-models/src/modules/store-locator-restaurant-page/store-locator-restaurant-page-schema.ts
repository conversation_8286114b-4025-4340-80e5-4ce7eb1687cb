import { cleanUrl, isValidUrl, StoreLocatorLanguage, StoreLocatorPageStatus } from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const storeLocatorRestaurantPageJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        status: {
            type: 'string',
            enum: Object.values(StoreLocatorPageStatus),
            default: StoreLocatorPageStatus.DRAFT,
        },
        restaurantId: {
            type: 'string',
            format: 'objectId',
        },
        organizationId: {
            type: 'string',
            format: 'objectId',
        },
        lang: {
            type: 'string',
            enum: Object.values(StoreLocatorLanguage),
        },
        fullUrl: {
            type: 'string',
            format: 'uri',
            validate: {
                validator: (v) => isValidUrl(v),
                message: (props) => `Url should be valid, value: ${props.value}`,
            },
            set: cleanUrl,
        },
        relativePath: {
            type: 'string',
            description: 'Relative path to the store locator page',
        },
        hasBeenUpdated: {
            type: 'boolean',
            default: true,
            description: 'Indicates if the store locator page has been updated since the last publication',
        },
        blocks: {
            $ref: '#/definitions/Blocks',
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
    },
    definitions: {
        Blocks: {
            type: 'object',
            additionalProperties: false,
            properties: {
                head: {
                    $ref: '#/definitions/HeadBlock',
                },
                information: {
                    $ref: '#/definitions/InformationBlock',
                },
                gallery: {
                    $ref: '#/definitions/GalleryBlock',
                },
                reviews: {
                    $ref: '#/definitions/ReviewsBlock',
                },
                callToActions: {
                    $ref: '#/definitions/CallToActionsBlock',
                },
                descriptions: {
                    $ref: '#/definitions/DescriptionsBlock',
                },
                socialNetworks: {
                    $ref: '#/definitions/SocialNetworksBlock',
                },
            },
            required: ['head', 'information', 'gallery', 'reviews', 'socialNetworks', 'callToActions', 'descriptions'],
            title: 'Blocks',
        },
        HeadBlock: {
            type: 'object',
            additionalProperties: false,
            properties: {
                title: {
                    type: 'string',
                },
                description: {
                    type: 'string',
                },
                twitterDescription: {
                    type: 'string',
                },
                keywords: {
                    type: 'string',
                },
                schemaOrgCuisineType: {
                    type: 'string',
                },
                facebookImageUrl: {
                    type: 'string',
                    format: 'uri',
                    validate: {
                        validator: (v) => isValidUrl(v),
                        message: (props) => `Url should be valid, value: ${props.value}`,
                    },
                },
                twitterImageUrl: {
                    type: 'string',
                    format: 'uri',
                    validate: {
                        validator: (v) => isValidUrl(v),
                        message: (props) => `Url should be valid, value: ${props.value}`,
                    },
                },
                snippetImageUrl: {
                    type: 'string',
                    format: 'uri',
                    validate: {
                        validator: (v) => isValidUrl(v),
                        message: (props) => `Url should be valid, value: ${props.value}`,
                    },
                },
                backup: {
                    type: 'object',
                    additionalProperties: true,
                    properties: {},
                },
            },
            required: [
                'title',
                'description',
                'twitterDescription',
                'keywords',
                'schemaOrgCuisineType',
                'facebookImageUrl',
                'twitterImageUrl',
                'snippetImageUrl',
            ],
            title: 'HeadBlock',
        },
        InformationBlock: {
            type: 'object',
            additionalProperties: false,
            properties: {
                title: {
                    type: 'string',
                },
                image: {
                    $ref: '#/definitions/Image',
                },
                ctas: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/Cta',
                    },
                },
                backup: {
                    type: 'object',
                    additionalProperties: true,
                    properties: {},
                },
            },
            required: ['title', 'image'],
            title: 'InformationBlock',
        },
        GalleryBlock: {
            type: 'object',
            additionalProperties: false,
            properties: {
                title: {
                    type: 'string',
                },
                subtitle: {
                    type: 'string',
                },
                images: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/Image',
                    },
                },
                backup: {
                    type: 'object',
                    additionalProperties: true,
                    properties: {},
                },
            },
            required: ['title', 'subtitle', 'images'],
            title: 'GalleryBlock',
        },
        ReviewsBlock: {
            type: 'object',
            additionalProperties: false,
            properties: {
                title: {
                    type: 'string',
                },
                cta: {
                    $ref: '#/definitions/Cta',
                },
                backup: {
                    type: 'object',
                    additionalProperties: true,
                    properties: {},
                },
            },
            required: ['title'],
            title: 'ReviewsBlock',
        },
        CallToActionsBlock: {
            type: 'object',
            additionalProperties: false,
            properties: {
                title: {
                    type: 'string',
                },
                ctas: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/Cta',
                    },
                },
                backup: {
                    type: 'object',
                    additionalProperties: true,
                    properties: {},
                },
            },
            required: ['title', 'ctas'],
            title: 'CallToActionsBlock',
        },
        DescriptionsBlock: {
            type: 'object',
            additionalProperties: false,
            properties: {
                items: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/Description',
                    },
                },
                backup: {
                    type: 'object',
                    additionalProperties: true,
                    properties: {},
                },
            },
            required: ['items'],
            title: 'DescriptionsBlock',
        },
        Description: {
            type: 'object',
            additionalProperties: false,
            properties: {
                title: {
                    type: 'string',
                },
                image: {
                    $ref: '#/definitions/Image',
                },
                blocks: {
                    type: 'array',
                    items: {
                        type: 'object',
                        additionalProperties: false,
                        properties: {
                            title: {
                                type: 'string',
                            },
                            text: {
                                type: 'string',
                            },
                        },
                        required: ['title', 'text'],
                    },
                },
            },
            required: ['title', 'image', 'blocks'],
            title: 'Description',
        },
        SocialNetworksBlock: {
            type: 'object',
            additionalProperties: false,
            properties: {
                title: {
                    type: 'string',
                },
                backup: {
                    type: 'object',
                    additionalProperties: true,
                    properties: {},
                },
            },
            required: ['title'],
            title: 'SocialNetworksBlock',
        },
        Image: {
            type: 'object',
            additionalProperties: false,
            properties: {
                description: {
                    type: 'string',
                },
                url: {
                    type: 'string',
                    format: 'uri',
                    validate: {
                        validator: (v) => isValidUrl(v),
                        message: (props) => `Url should be valid, value: ${props.value}`,
                    },
                },
            },
            required: ['description', 'url'],
            title: 'Image',
        },
        Cta: {
            type: 'object',
            additionalProperties: false,
            properties: {
                text: {
                    type: 'string',
                },
                url: {
                    type: 'string',
                },
            },
            required: ['text', 'url'],
            title: 'Cta',
        },
    },
    required: [
        '_id',
        'createdAt',
        'updatedAt',
        'status',
        'relativePath',
        'restaurantId',
        'blocks',
        'organizationId',
        'lang',
        'fullUrl',
        'hasBeenUpdated',
    ],
    title: 'StoreLocatorRestaurantPage',
} as const satisfies JSONSchemaExtraProps;
