import { createIncrementedPublicBusinessId } from '@malou-io/package-utils';

import { DbId } from ':helpers/interfaces';
import { OrganizationModel, RestaurantModel } from ':modules/index';

export const generatePublicBusinessId = async (restaurantId: DbId): Promise<string | undefined> => {
    const restaurant = await RestaurantModel.findById(restaurantId, { organizationId: 1 }).lean();
    if (!restaurant?.organizationId) {
        return undefined;
    }

    const organization = await OrganizationModel.findOneAndUpdate(
        { _id: restaurant.organizationId },
        { $inc: { reviewPublicBusinessIdCount: 1 } },
        { new: true, fields: { name: 1, reviewPublicBusinessIdCount: 1 } }
    ).lean();
    if (!organization) {
        return undefined;
    }

    return createIncrementedPublicBusinessId(organization.reviewPublicBusinessIdCount, organization.name);
};
