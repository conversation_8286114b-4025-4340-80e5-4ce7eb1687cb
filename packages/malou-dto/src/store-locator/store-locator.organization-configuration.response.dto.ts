import { z } from 'zod';

import {
    isValidLanguageCodeISO_1,
    PlatformKey,
    StoreLocatorAiSettingsLanguageStyle,
    StoreLocatorLanguage,
    StoreLocatorMapPageElementIds,
    StoreLocatorRestaurantPageElementIds,
} from '@malou-io/package-utils';

export const storeLocatorOrganizationConfigurationResponseDtoValidator = z.object({
    id: z.string(),
    cloudfrontDistributionId: z.string(),
    baseUrl: z.string(),
    isLive: z.boolean(),
    organizationId: z.string(),
    organization: z.object({
        id: z.string(),
        name: z.string(),
    }),
    styles: z.object({
        fonts: z.array(
            z.object({
                class: z.string(),
                src: z.string(),
                weight: z.string().optional(),
                style: z.string().optional(),
            })
        ),
        colors: z.array(
            z.object({
                class: z.string(),
                value: z.string(),
            })
        ),
        pages: z.object({
            store: z.record(z.nativeEnum(StoreLocatorRestaurantPageElementIds), z.array(z.string())),
            map: z.record(z.nativeEnum(StoreLocatorMapPageElementIds), z.array(z.string())),
            storeDraft: z.record(z.nativeEnum(StoreLocatorRestaurantPageElementIds), z.array(z.string())),
            mapDraft: z.record(z.nativeEnum(StoreLocatorMapPageElementIds), z.array(z.string())),
        }),
    }),
    plugins: z
        .object({
            googleAnalytics: z
                .object({
                    trackingId: z.string(),
                })
                .optional(),
        })
        .optional(),
    languages: z.object({
        primary: z.nativeEnum(StoreLocatorLanguage).refine(isValidLanguageCodeISO_1, {
            message: 'Primary language code must be a valid ISO 639-1 code defined in LanguageCodeISO_1 enum',
        }),
        secondary: z
            .array(
                z.nativeEnum(StoreLocatorLanguage).refine(isValidLanguageCodeISO_1, {
                    message: 'Secondary language code must be a valid ISO 639-1 code defined in LanguageCodeISO_1 enum',
                })
            )
            .default([]),
    }),
    aiSettings: z.object({
        tone: z.array(z.string()),
        languageStyle: z.nativeEnum(StoreLocatorAiSettingsLanguageStyle),
        attributeIds: z.array(z.string()),
        restaurantKeywordIds: z.array(z.string()),
        specialAttributes: z.array(
            z.object({
                restaurantId: z.string(),
                text: z.string(),
            })
        ),
        attributes: z.array(
            z.object({
                id: z.string(),
                attributeId: z.string(),
                platformKey: z.nativeEnum(PlatformKey),
                attributeName: z.object({
                    fr: z.string(),
                    en: z.string().optional(),
                    es: z.string().optional(),
                    it: z.string().optional(),
                }),
            })
        ),
        keywords: z.array(
            z.object({
                restaurantKeywordId: z.string(),
                text: z.string(),
                restaurantId: z.string(),
                keywordId: z.string(),
            })
        ),
    }),
});

export type StoreLocatorOrganizationConfigurationResponseDto = z.infer<typeof storeLocatorOrganizationConfigurationResponseDtoValidator>;
