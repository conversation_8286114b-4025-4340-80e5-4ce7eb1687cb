{"name": "@malou-io/monorepo", "version": "1.0.0", "description": "", "main": "index.js", "packageManager": "pnpm@10.6.5", "keywords": [], "author": "", "license": "ISC", "engines": {"node": "^22", "pnpm": "~10.6"}, "pnpm": {"supportedArchitectures": {"cpu": ["x64", "arm64"], "os": ["win32", "darwin", "current"]}, "patchedDependencies": {"agenda@4.4.0": "patches/<EMAIL>"}}, "scripts": {"preinstall": "npx only-allow pnpm", "clean": "./scripts/clean.sh", "test": "echo \"Error: no test specified\" && exit 1", "db:seed": "pnpm run --filter=@malou-io/app-api db:seed", "start-local": "concurrently \"tsc -b -w --verbose --preserveWatchOutput\" \"sleep 5 && turbo run start-local\"", "start-local-light": "turbo run start-local --filter @malou-io/app-api --filter @malou-io/app-web", "start-local-api-light": "turbo run start-local --filter @malou-io/app-api", "start-dev": "concurrently \"tsc -b -w --verbose --preserveWatchOutput\" \"sleep 5 && turbo run start-dev\"", "start-dev-light": "turbo run start-dev --filter @malou-io/app-api --filter @malou-io/app-web", "start-dev-api-light": "turbo run start-dev --filter @malou-io/app-api", "start-staging": "concurrently \"tsc -b -w --verbose --preserveWatchOutput\" \"sleep 5 && turbo run start-staging\"", "start-staging-light": "turbo run start-staging --filter @malou-io/app-api --filter @malou-io/app-web", "start-staging-api-light": "turbo run start-staging --filter @malou-io/app-api", "start-production": "concurrently \"tsc -b -w --verbose --preserveWatchOutput\" \"sleep 5 && turbo run start-production\"", "start-production-light": "turbo run start-production --filter @malou-io/app-api --filter @malou-io/app-web", "start-production-api-light": "turbo run start-production --filter @malou-io/app-api", "watch-build-packages": "turbo run watch-build --filter='./packages/*'", "build": "turbo run build", "build-development": "turbo run build-development", "build-staging": "turbo run build-staging", "build-production": "turbo run build-production", "build-clean": "turbo run build-clean", "test:unit": "turbo run test:unit", "test:unit:angular": "turbo run test:unit:angular", "test:integration": "turbo run test:integration", "dev:check": "pnpm i && pnpm run build-clean && pnpm run build --force && concurrently \"pnpm run format\" \"pnpm run test:unit\" \"pnpm run test:unit:angular\" \"pnpm run lint-fix\"", "lint": "turbo run lint", "lint-fix": "turbo run lint-fix", "format": "turbo run format", "format:check": "turbo run format:check", "lint-staged": "turbo run lint-staged", "husky-setup": "husky install && chmod ug+x .husky/*", "start-email": "turbo run start-email", "experimentations-type-gen": "npx growthbook features generate-types --output ./packages/malou-utils/src/experimentation/types", "playwright-ui": "turbo run playwright-ui", "e2e": "turbo run e2e", "tce2e": "turbo run tce2e"}, "devDependencies": {"@turbo/gen": "^1.9.7", "concurrently": "^8.2.2", "growthbook": "^0.2.3", "husky": "^8.0.0", "lint-staged": "^15.5.0", "prettier": "^3.5.3", "tsc-alias": "^1.8.8", "tsconfig-paths": "^4.2.0", "turbo": "^1.13.4", "typescript": "^5.4.3"}}