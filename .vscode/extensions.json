{
    // See https://go.microsoft.com/fwlink/?LinkId=827846
    // for the documentation about the extensions.json format
    "recommendations": [
        "angular.ng-template",
        "astro-build.astro-vscode",
        "bradlc.vscode-tailwindcss",
        "dbaeumer.vscode-eslint",
        "eamodio.gitlens",
        "esbenp.prettier-vscode",
        "firsttris.vscode-jest-runner",
        "github.copilot-chat",
        "github.copilot",
        "github.vscode-github-actions",
        "hashicorp.terraform",
        "kuscamara.remove-unused-imports",
        "lokalise.i18n-ally",
        "ms-azuretools.vscode-docker",
        "ms-playwright.playwright",
        "ms-vsliveshare.vsliv",
        "richie5um2.vscode-sort-json",
        "streetsidesoftware.code-spell-checker",
        "usernamehw.errorlens",
        "yoavbls.pretty-ts-errors"
    ]
}
